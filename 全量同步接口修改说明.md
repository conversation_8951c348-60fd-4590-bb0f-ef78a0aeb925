# 全量同步接口修改说明

## 修改概述

根据需求，已对全量同步接口 `/sysgetway/api/sync/full/{hospitalId}/{tableName}` 进行了完全重构，**完全参考患者数据同步接口的处理方式**，实现了：

1. **处理方式一致**：与 `/sysgetway/api/sync/patient/{hospitalId}/{patientType}` 接口处理方式完全一致
2. **唯一区别**：患者接口是增量修改数据，全量接口是全量替换数据
3. **Hash结构存储**：使用Redis Hash结构存储数据，与患者接口保持一致
4. **异步处理**：采用异步方式处理，立即返回任务状态
5. **任务跟踪**：返回任务ID，可通过任务ID查询处理状态
6. **数据清除**：全量替换模式，先清除原有数据再写入新数据

## 接口变更

### 原接口
```http
PUT /sysgetway/api/sync/full/{hospitalId}/{tableName}
```
- 只支持从外部系统获取数据
- 同步处理，可能阻塞较长时间
- 直接覆盖Redis数据

### 新接口
```http
PUT /sysgetway/api/sync/full/{hospitalId}/{tableName}
Content-Type: application/json

[数据列表] (可选)
```

#### 请求参数
- `hospitalId`: 医院ID (路径参数)
- `tableName`: 表名 (路径参数)，可选值：department, user, patient
- `dataList`: 数据列表 (请求体，可选)

#### 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": 0,
    "message": "同步进行中",
    "hospitalId": "H001",
    "tableNameOrPatientType": "department",
    "startTime": "2024-01-01T10:00:00",
    "taskId": "uuid-task-id"
  }
}
```

## 处理流程

1. **接收请求**：接收医院ID、表名和可选的数据列表
2. **参数验证**：验证表名是否有效
3. **生成任务ID**：为异步任务生成唯一标识
4. **异步执行**：
   - 如果提供了数据，使用提供的数据
   - 如果未提供数据，从外部系统获取
   - 清除Redis中的原有数据
   - 保存新数据到Redis
   - 更新任务状态
5. **立即返回**：返回任务初始状态和任务ID

## 使用示例

### 1. 不提供数据（从外部系统获取）
```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/department" \
  -H "Content-Type: application/json"
```

### 2. 提供科室数据
```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/department" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "deptId": 1,
      "deptName": "内科",
      "deptCode": "NK",
      "parentId": 0,
      "deptLevel": 1,
      "status": 1
    },
    {
      "deptId": 2,
      "deptName": "外科", 
      "deptCode": "WK",
      "parentId": 0,
      "deptLevel": 1,
      "status": 1
    }
  ]'
```

### 3. 提供用户数据
```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/user" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "userId": "U001",
      "userName": "张医生",
      "userCode": "zhangys",
      "deptId": 1,
      "position": "主治医师",
      "mobile": "13800138001",
      "status": 1
    }
  ]'
```

### 4. 提供患者数据（Auto分类）
```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/patient" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "inpatientInfoId": "P001",
      "name": "张三",
      "idCard": "110101199001011234",
      "mobile": "13800138001",
      "sex": 1,
      "age": 30,
      "hospitalizationNo": "H001",
      "inhospitalDiagnose": "高血压",
      "deptId": 1,
      "sickbedNo": "1-101",
      "inhospitalTime": "2024-01-01T10:00:00",
      "status": 1,
      "category": "医保"
    },
    {
      "inpatientInfoId": "P002",
      "name": "李四",
      "idCard": "110101199002021234",
      "mobile": "13800138002",
      "sex": 2,
      "age": 25,
      "hospitalizationNo": "H002",
      "inhospitalDiagnose": "糖尿病",
      "deptId": 2,
      "sickbedNo": "2-102",
      "inhospitalTime": "2024-01-02T11:00:00",
      "outhospitalTime": "2024-01-08T16:00:00",
      "status": 2,
      "category": "自费"
    }
  ]'
```

**Auto分类结果**：
- P001 (status=1, 在院) → 存储到 `hospital:patient:type:H001:in` 和 `hospital:patient:type:H001:up`
- P002 (status=2, 出院) → 存储到 `hospital:patient:type:H001:out`

## 任务状态查询

使用返回的任务ID查询处理状态：

```bash
curl -X GET "http://localhost:9000/sysgetway/api/sync/task/{taskId}"
```

### 响应示例（患者数据Auto分类）：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": 1,
    "message": "同步成功",
    "hospitalId": "223",
    "tableNameOrPatientType": "patient",
    "startTime": "2024-01-01T10:00:00",
    "endTime": "2024-01-01T10:00:05",
    "costTime": 5000,
    "taskId": "uuid-task-id",

    // 基础统计信息
    "count": 8,
    "totalCount": 10,
    "successCount": 8,
    "updatedCount": 0,
    "insertedCount": 8,
    "skippedCount": 2,
    "failedCount": 2,

    // 患者分类统计
    "classificationStats": {
      "in": 3,
      "up": 3,
      "out": 2
    },

    // 性能统计
    "performanceStats": {
      "totalProcessingTime": 5000,
      "averageProcessingTimePerRecord": 500,
      "dataValidationTime": "N/A",
      "redisWriteTime": "N/A"
    },

    // 失败原因统计
    "failureReasons": {
      "缺少住院号": 1,
      "缺少入院时间": 1
    }
  }
}
```

### 响应示例（非患者数据）：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": 1,
    "message": "同步成功",
    "hospitalId": "223",
    "tableNameOrPatientType": "department",
    "startTime": "2024-01-01T10:00:00",
    "endTime": "2024-01-01T10:00:03",
    "costTime": 3000,
    "taskId": "uuid-task-id",

    // 基础统计信息
    "count": 5,
    "totalCount": 5,
    "successCount": 5,
    "updatedCount": 0,
    "insertedCount": 5,
    "skippedCount": 0,
    "failedCount": 0,

    // 性能统计
    "performanceStats": {
      "totalProcessingTime": 3000,
      "averageProcessingTimePerRecord": 600,
      "dataValidationTime": "N/A",
      "redisWriteTime": "N/A"
    }
  }
}
```

## 统计字段说明

### 基础状态
- `status: 0` - 同步进行中
- `status: 1` - 同步成功
- `status: -1` - 同步失败

### 数据统计字段
| 字段名 | 说明 | 适用场景 |
|--------|------|----------|
| `count` | 最终成功处理的数据量 | 所有接口 |
| `totalCount` | 总输入数据量 | 所有接口 |
| `successCount` | 成功处理的数据量 | 所有接口 |
| `updatedCount` | 更新的数据量 | 增量同步（患者接口） |
| `insertedCount` | 插入的数据量 | 全量同步 |
| `skippedCount` | 跳过的数据量（无效数据） | 所有接口 |
| `failedCount` | 失败的数据量 | 所有接口 |

### 特殊统计字段
| 字段名 | 说明 | 适用场景 |
|--------|------|----------|
| `classificationStats` | 分类统计信息 | 患者数据auto分类 |
| `performanceStats` | 性能统计信息 | 所有接口 |
| `failureReasons` | 失败原因统计 | 有失败数据时 |

### 患者分类统计说明
- `in`: 入院患者数量（status=1的患者）
- `up`: 在院患者数量（status=1的患者，与in相同）
- `out`: 出院患者数量（status=2的患者）

**注意**：在院患者会同时计入`in`和`up`分类，这是按照患者接口的auto逻辑设计的。

## 技术实现

### 主要修改文件

1. **SyncController.java**
   - 修改 `syncFull` 方法，支持接收数据列表
   - 添加参数验证和错误处理
   - 调用新的异步服务方法

2. **SyncService.java**
   - 添加 `syncFullAsync` 方法接口定义

3. **SyncServiceImpl.java**
   - 完全重写 `executeFullSyncTask` 方法，**完全参考患者数据同步方式**
   - 添加 `buildRedisKey` 方法构建Redis键（与患者接口一致）
   - 添加 `generateFieldName` 方法生成字段名（与患者接口一致）
   - 使用Hash结构存储数据，与患者接口保持一致
   - 实现全量替换逻辑：先删除原有数据，再写入新数据

### 关键特性（完全参考患者接口）

1. **Hash结构存储**：使用 `redisUtils.hSetAll` 批量存储数据到Redis Hash结构
2. **全量替换模式**：先使用 `redisUtils.delete` 清除原有数据，再写入新数据
3. **字段名生成**：根据数据类型生成唯一字段名（department用deptId，user用userId，patient用住院号+时间）
4. **JSON序列化**：使用 `JSON.toJSONString` 并包含null值处理
5. **异步处理**：使用 `CompletableFuture.supplyAsync` 实现真正的异步执行
6. **任务管理**：集成现有的 `AsyncTaskManager` 进行任务管理
7. **状态跟踪**：支持通过任务ID查询处理状态和结果
8. **错误处理**：完善的异常处理和错误状态记录

## 与患者接口的对比

| 特性 | 患者接口 (`/api/sync/patient/{hospitalId}/{patientType}`) | 全量接口 (`/api/sync/full/{hospitalId}/{tableName}`) |
|------|----------------------------------------------------------|-----------------------------------------------------|
| **数据处理方式** | 增量修改（合并现有数据） | 全量替换（先删除再写入） |
| **Redis存储结构** | Hash结构 | Hash结构（完全一致） |
| **Redis键格式** | `hospital:patient:type:{hospitalId}:{patientType}` | 患者：`hospital:patient:type:{hospitalId}:{patientType}`<br/>非患者：`hospital:{tableName}:{hospitalId}` |
| **字段名生成** | `hosptime_住院号_时间` | 患者：`hosptime_住院号_时间`（完全一致）<br/>非患者：`dept_ID`、`user_ID` |
| **患者分类逻辑** | 按指定patientType过滤 | **Auto分类**：按患者状态自动分类到in/up/out |
| **异步处理** | ✅ 支持 | ✅ 支持（完全一致） |
| **任务管理** | ✅ 支持 | ✅ 支持（完全一致） |
| **JSON序列化** | `Feature.WriteNulls` | `Feature.WriteNulls`（完全一致） |
| **错误处理** | ✅ 完善 | ✅ 完善（完全一致） |

## 患者数据Auto分类逻辑

全量同步接口现在完全按照患者接口的auto方式处理患者数据：

### 分类规则
- **在院患者** (status=1)：同时存储到 `in` 和 `up` 分类
- **出院患者** (status=2)：存储到 `out` 分类
- **无效患者**：缺少住院号或入院时间的患者会被跳过

### Redis存储结构
```
hospital:patient:type:{hospitalId}:in    # 入院患者
hospital:patient:type:{hospitalId}:up    # 在院患者
hospital:patient:type:{hospitalId}:out   # 出院患者
```

### 字段名格式
```
hosptime_{住院号}_{入院时间yyyyMMddHHmmss}
例如：hosptime_H001_20240101100000
```

## 兼容性

- 保持原有接口路径不变
- 向后兼容：不提供数据时行为与原接口一致
- 扩展功能：提供数据时使用新的处理逻辑
- **处理方式统一**：与患者接口处理方式完全一致

## 注意事项

1. **数据格式**：需要符合对应表的结构要求
2. **全量替换**：Redis中的数据会被完全替换，请确保数据的完整性
3. **异步处理**：处理完成前，建议通过任务ID查询状态
4. **性能优化**：大量数据会自动使用管道批量写入
5. **字段名唯一性**：系统会根据数据类型自动生成唯一字段名
