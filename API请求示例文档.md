# SysGetway API 请求示例文档

本文档提供了 SysGetway 系统主要 API 接口的详细请求示例，帮助开发人员快速理解和使用这些接口。

## 目录

- [全量同步接口](#全量同步接口)
  - [科室数据全量同步](#科室数据全量同步)
  - [医护人员数据全量同步](#医护人员数据全量同步)
  - [患者数据全量同步](#患者数据全量同步)
- [患者分类同步接口](#患者分类同步接口)
  - [入院患者同步](#入院患者同步)
  - [在院患者同步](#在院患者同步)
  - [出院患者同步](#出院患者同步)
  - [自动分类患者同步](#自动分类患者同步)
- [状态查询接口](#状态查询接口)
  - [同步状态查询](#同步状态查询)
  - [任务状态查询](#任务状态查询)

## 全量同步接口

全量同步接口用于清空并重新同步指定医院的指定表数据。支持科室、医护人员和患者三种数据类型。

### 科室数据全量同步

**接口**：`PUT /sysgetway/api/sync/full/{hospitalId}/department`

**请求示例**：

```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/department" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "departmentId": 1,
      "code": "NK",
      "name": "内科",
      "updatedAt": "2024-01-01T10:00:00"
    },
    {
      "departmentId": 2,
      "code": "WK",
      "name": "外科",
      "updatedAt": "2024-01-01T10:00:00"
    },
    {
      "departmentId": 3,
      "code": "EK",
      "name": "儿科",
      "updatedAt": "2024-01-01T10:00:00"
    }
  ]'
```

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "IN_PROGRESS",
    "message": "同步任务正在处理中",
    "hospitalId": "H001",
    "tableNameOrPatientType": "department",
    "startTime": "2024-07-18T14:30:00",
    "taskId": "task_12345678"
  },
  "timestamp": 1689675000000
}
```

### 医护人员数据全量同步

**接口**：`PUT /sysgetway/api/sync/full/{hospitalId}/user`

**请求示例**：

```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/user" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "userName": "D001",
      "name": "张医生",
      "sex": 1,
      "roleId": 1,
      "deptId": 1,
      "mobile": "13800138001",
      "inpatientWard": "内科一病区",
      "updatedAt": "2024-01-01T10:00:00"
    },
    {
      "userName": "N001",
      "name": "李护士",
      "sex": 2,
      "roleId": 2,
      "deptId": 1,
      "mobile": "13800138002",
      "inpatientWard": "内科一病区,内科二病区",
      "updatedAt": "2024-01-01T10:00:00"
    },
    {
      "userName": "D002",
      "name": "王医生",
      "sex": 1,
      "roleId": 1,
      "deptId": 2,
      "mobile": "13800138003",
      "inpatientWard": "外科一病区",
      "updatedAt": "2024-01-01T10:00:00"
    }
  ]'
```

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "IN_PROGRESS",
    "message": "同步任务正在处理中",
    "hospitalId": "H001",
    "tableNameOrPatientType": "user",
    "startTime": "2024-07-18T14:35:00",
    "taskId": "task_23456789"
  },
  "timestamp": 1689675300000
}
```

### 患者数据全量同步

**接口**：`PUT /sysgetway/api/sync/full/{hospitalId}/patient`

**请求示例**：

```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/full/H001/patient" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "inpatientInfoId": "P001",
      "name": "张三",
      "idCard": "110101199001011234",
      "mobile": "13800138001",
      "sex": 1,
      "age": 30,
      "birthday": "1990-01-01",
      "hospitalizationNo": "H001",
      "inhospitalDiagnose": "高血压",
      "deptId": 1,
      "sickbedNo": "1-101",
      "doctorId": "D001",
      "nurseId": "N001",
      "nurseLevel": 2,
      "inhospitalTime": "2024-01-01T10:00:00",
      "status": 1,
      "category": "医保",
      "inpatientWard": "内科一病区",
      "updatedAt": "2024-01-01T10:00:00"
    },
    {
      "inpatientInfoId": "P002",
      "name": "李四",
      "idCard": "110101199002021234",
      "mobile": "13800138002",
      "sex": 2,
      "age": 25,
      "birthday": "1990-02-02",
      "hospitalizationNo": "H002",
      "inhospitalDiagnose": "糖尿病",
      "deptId": 1,
      "sickbedNo": "1-102",
      "doctorId": "D001",
      "nurseId": "N001",
      "nurseLevel": 2,
      "inhospitalTime": "2024-01-02T10:00:00",
      "status": 1,
      "category": "医保",
      "inpatientWard": "内科一病区",
      "updatedAt": "2024-01-02T10:00:00"
    },
    {
      "inpatientInfoId": "P003",
      "name": "王五",
      "idCard": "110101199003031234",
      "mobile": "13800138003",
      "sex": 1,
      "age": 35,
      "birthday": "1990-03-03",
      "hospitalizationNo": "H003",
      "inhospitalDiagnose": "肺炎",
      "deptId": 2,
      "sickbedNo": "2-101",
      "doctorId": "D002",
      "nurseId": "N002",
      "nurseLevel": 3,
      "inhospitalTime": "2024-01-03T10:00:00",
      "outhospitalTime": "2024-01-10T14:00:00",
      "status": 2,
      "category": "自费",
      "inpatientWard": "外科一病区",
      "updatedAt": "2024-01-10T14:00:00"
    }
  ]'
```

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "IN_PROGRESS",
    "message": "同步任务正在处理中",
    "hospitalId": "H001",
    "tableNameOrPatientType": "patient",
    "startTime": "2024-07-18T14:40:00",
    "taskId": "task_34567890"
  },
  "timestamp": 1689675600000
}
```

## 患者分类同步接口

患者分类同步接口用于同步指定医院的特定类型患者数据。支持入院、在院、出院和自动分类四种类型。

### 入院患者同步

**接口**：`PUT /sysgetway/api/sync/patient/{hospitalId}/in`

**请求示例**：

```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/patient/H001/in" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "inpatientInfoId": "P001",
      "name": "张三",
      "idCard": "110101199001011234",
      "mobile": "13800138001",
      "sex": 1,
      "age": 30,
      "birthday": "1990-01-01",
      "hospitalizationNo": "H001",
      "inhospitalDiagnose": "高血压",
      "deptId": 1,
      "sickbedNo": "1-101",
      "doctorId": "D001",
      "nurseId": "N001",
      "nurseLevel": 2,
      "inhospitalTime": "2024-07-18T10:00:00",
      "status": 1,
      "category": "医保",
      "inpatientWard": "内科一病区",
      "updatedAt": "2024-07-18T10:00:00"
    }
  ]'
```

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "IN_PROGRESS",
    "message": "同步任务正在处理中",
    "hospitalId": "H001",
    "tableNameOrPatientType": "patient:in",
    "startTime": "2024-07-18T14:45:00",
    "taskId": "task_45678901"
  },
  "timestamp": 1689675900000
}
```

### 在院患者同步

**接口**：`PUT /sysgetway/api/sync/patient/{hospitalId}/up`

**请求示例**：

```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/patient/H001/up" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "inpatientInfoId": "P002",
      "name": "李四",
      "idCard": "110101199002021234",
      "mobile": "13800138002",
      "sex": 2,
      "age": 25,
      "birthday": "1990-02-02",
      "hospitalizationNo": "H002",
      "inhospitalDiagnose": "糖尿病",
      "deptId": 1,
      "sickbedNo": "1-102",
      "doctorId": "D001",
      "nurseId": "N001",
      "nurseLevel": 2,
      "inhospitalTime": "2024-07-10T10:00:00",
      "status": 1,
      "category": "医保",
      "inpatientWard": "内科一病区",
      "updatedAt": "2024-07-18T11:00:00"
    }
  ]'
```

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "IN_PROGRESS",
    "message": "同步任务正在处理中",
    "hospitalId": "H001",
    "tableNameOrPatientType": "patient:up",
    "startTime": "2024-07-18T14:50:00",
    "taskId": "task_56789012"
  },
  "timestamp": 1689676200000
}
```

### 出院患者同步

**接口**：`PUT /sysgetway/api/sync/patient/{hospitalId}/out`

**请求示例**：

```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/patient/H001/out" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "inpatientInfoId": "P003",
      "name": "王五",
      "idCard": "110101199003031234",
      "mobile": "13800138003",
      "sex": 1,
      "age": 35,
      "birthday": "1990-03-03",
      "hospitalizationNo": "H003",
      "inhospitalDiagnose": "肺炎",
      "deptId": 2,
      "sickbedNo": "2-101",
      "doctorId": "D002",
      "nurseId": "N002",
      "nurseLevel": 3,
      "inhospitalTime": "2024-07-01T10:00:00",
      "outhospitalTime": "2024-07-15T14:00:00",
      "status": 2,
      "category": "自费",
      "inpatientWard": "外科一病区",
      "updatedAt": "2024-07-15T14:00:00"
    }
  ]'
```

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "IN_PROGRESS",
    "message": "同步任务正在处理中",
    "hospitalId": "H001",
    "tableNameOrPatientType": "patient:out",
    "startTime": "2024-07-18T14:55:00",
    "taskId": "task_67890123"
  },
  "timestamp": 1689676500000
}
```

### 自动分类患者同步

**接口**：`PUT /sysgetway/api/sync/patient/{hospitalId}/auto`

**请求示例**：

```bash
curl -X PUT "http://localhost:9000/sysgetway/api/sync/patient/H001/auto" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "inpatientInfoId": "P001",
      "name": "张三",
      "idCard": "110101199001011234",
      "mobile": "13800138001",
      "sex": 1,
      "age": 30,
      "hospitalizationNo": "H001",
      "inhospitalDiagnose": "高血压",
      "deptId": 1,
      "sickbedNo": "1-101",
      "inhospitalTime": "2024-07-18T10:00:00",
      "status": 1
    },
    {
      "inpatientInfoId": "P003",
      "name": "王五",
      "idCard": "110101199003031234",
      "mobile": "13800138003",
      "sex": 1,
      "age": 35,
      "hospitalizationNo": "H003",
      "inhospitalDiagnose": "肺炎",
      "deptId": 2,
      "sickbedNo": "2-101",
      "inhospitalTime": "2024-07-01T10:00:00",
      "outhospitalTime": "2024-07-15T14:00:00",
      "status": 2
    }
  ]'
```

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "IN_PROGRESS",
    "message": "同步任务正在处理中",
    "hospitalId": "H001",
    "tableNameOrPatientType": "patient:auto",
    "startTime": "2024-07-18T15:00:00",
    "taskId": "task_78901234"
  },
  "timestamp": 1689676800000
}
```

## 状态查询接口

### 同步状态查询

**接口**：`GET /sysgetway/api/sync/status/{hospitalId}/{tableName}`

**请求示例**：

```bash
curl -X GET "http://localhost:9000/sysgetway/api/sync/status/H001/patient"
```

### 任务状态查询

**接口**：`GET /sysgetway/api/sync/task/{taskId}`

**请求示例**：

```bash
curl -X GET "http://localhost:9000/sysgetway/api/sync/task/task_12345678"
```

**响应示例**：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "status": "COMPLETED",
    "message": "同步任务已完成",
    "hospitalId": "H001",
    "tableNameOrPatientType": "department",
    "startTime": "2024-07-18T14:30:00",
    "endTime": "2024-07-18T14:30:05",
    "duration": 5000,
    "totalCount": 3,
    "successCount": 3,
    "failCount": 0,
    "taskId": "task_12345678"
  },
  "timestamp": 1689675005000
}
```

## 数据字段说明

### 科室数据字段 (Department)

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| departmentId | Integer | 是 | 科室ID，自增主键 | 1 |
| code | String | 是 | 科室编码 | "NK" |
| name | String | 是 | 科室名称 | "内科" |
| updatedAt | DateTime | 否 | 数据更新时间 | "2024-01-01T10:00:00" |

### 医护人员数据字段 (User)

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| userName | String | 是 | 医护人员账号（可以是工号） | "D001" |
| name | String | 是 | 医护人员姓名 | "张医生" |
| sex | Integer | 否 | 性别，1-男，2-女 | 1 |
| roleId | Integer | 否 | 角色ID，1-医生，2-护士 | 1 |
| deptId | Integer | 否 | 科室ID | 1 |
| mobile | String | 否 | 手机号 | "13800138001" |
| inpatientWard | String | 否 | 所管理的病区，多个用逗号分隔 | "内科一病区,内科二病区" |
| updatedAt | DateTime | 否 | 数据更新时间 | "2024-01-01T10:00:00" |

### 患者数据字段 (Patient)

| 字段名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| inpatientInfoId | String | 是 | 患者信息唯一ID，每次出入院一个ID编号 | "P001" |
| name | String | 是 | 患者姓名 | "张三" |
| idCard | String | 否 | 身份证号 | "110101199001011234" |
| mobile | String | 否 | 手机号码 | "13800138001" |
| sex | Integer | 否 | 性别，1-男，2-女 | 1 |
| age | Integer | 否 | 年龄 | 30 |
| birthday | Date | 否 | 生日 | "1990-01-01" |
| hospitalizationNo | String | 否 | 住院号 | "H001" |
| inhospitalDiagnose | String | 否 | 入院诊断 | "高血压" |
| deptId | Integer | 否 | 科室ID | 1 |
| sickbedNo | String | 否 | 床位号 | "1-101" |
| doctorId | String | 否 | 责任医生ID | "D001" |
| nurseId | String | 否 | 责任护士ID | "N001" |
| nurseLevel | Integer | 否 | 护理级别，1-一级，2-二级，3-三级，4-特级 | 2 |
| inhospitalTime | DateTime | 否 | 入院时间 | "2024-01-01T10:00:00" |
| outhospitalTime | DateTime | 否 | 出院时间 | "2024-01-10T14:00:00" |
| status | Integer | 否 | 患者当前状态，1-在院，2-出院 | 1 |
| category | String | 否 | 患者类别，如医保、自费等 | "医保" |
| inpatientWard | String | 否 | 病区 | "内科一病区" |
| updatedAt | DateTime | 否 | 数据更新时间 | "2024-01-01T10:00:00" |

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数错误 | 检查请求参数格式和必填字段 |
| 404 | 接口不存在 | 检查请求URL路径 |
| 500 | 服务器内部错误 | 查看服务器日志，联系管理员 |

### 错误响应示例

```json
{
  "code": 400,
  "message": "无效的表名: invalid_table",
  "data": null,
  "timestamp": 1689675000000
}
```

## 最佳实践

### 1. 数据量控制
- 单次请求建议不超过1000条记录
- 大量数据建议分批处理
- 监控请求响应时间，避免超时

### 2. 异步处理
- 所有同步接口都是异步处理
- 通过返回的taskId查询任务状态
- 建议轮询间隔为3-5秒

### 3. 错误重试
- 网络异常时可以重试
- 参数错误不建议重试
- 重试间隔建议指数退避

### 4. 数据一致性
- 全量同步会清空原有数据
- 患者分类同步是增量更新
- 建议在业务低峰期执行全量同步

## 测试脚本

项目提供了完整的测试脚本，位于项目根目录：

- `test_full_sync_api.sh` - 全量同步接口测试
- `test_patient_auto_classification.sh` - 患者自动分类测试
- `test_task_status_details.sh` - 任务状态查询测试

使用方法：

```bash
# 添加执行权限
chmod +x test_full_sync_api.sh

# 执行测试
./test_full_sync_api.sh
```

## 联系方式

如有问题或建议，请联系开发团队或查看项目文档。
```
