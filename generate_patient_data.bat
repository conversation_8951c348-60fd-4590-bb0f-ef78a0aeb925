@echo off
echo 患者数据生成器
echo ==============

rem 默认生成20000条数据
set COUNT=20000

rem 检查是否提供了命令行参数
if not "%~1"=="" (
    set /a COUNT=%~1 2>nul
    if errorlevel 1 (
        echo 警告: 无效的数量参数，使用默认值 %COUNT%
    )
)

rem 设置输出文件名
set OUTPUT_FILE=patient_data_%COUNT%.json

rem 编译Java文件
echo 编译Java文件...
cd /d %~dp0
javac -cp "./target/classes;./target/test-classes;./target/dependency/*" src/test/java/com/sysgetway/core/example/MassPatientDataGenerator.java

rem 运行生成器
echo 运行数据生成器，生成 %COUNT% 条患者数据，输出到 %OUTPUT_FILE% ...
java -cp "./target/classes;./target/test-classes;./target/dependency/*" com.sysgetway.core.example.MassPatientDataGenerator %COUNT%

echo 数据生成完成，已保存到 %OUTPUT_FILE%
echo 文件大小：
dir %OUTPUT_FILE% | findstr /C:"%OUTPUT_FILE%"

echo.
echo 按任意键退出...
pause > nul 