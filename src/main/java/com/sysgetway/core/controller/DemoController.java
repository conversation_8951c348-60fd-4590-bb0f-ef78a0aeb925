package com.sysgetway.core.controller;

import com.sysgetway.core.common.exception.BusinessException;
import com.sysgetway.core.common.util.ResponseResult;
import com.sysgetway.core.model.vo.DemoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 示例控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/demo")
@Api(tags = "示例接口", description = "用于展示API规范和统一返回格式")
public class DemoController {

    /**
     * 获取示例数据
     * @return 示例数据
     */
    @GetMapping
    @ApiOperation(value = "获取示例数据", notes = "获取示例数据列表")
    public ResponseResult<List<DemoVO>> list() {
        log.info("获取示例数据列表");
        List<DemoVO> demoList = new ArrayList<>();
        demoList.add(new DemoVO(1L, "示例1", "描述1"));
        demoList.add(new DemoVO(2L, "示例2", "描述2"));
        demoList.add(new DemoVO(3L, "示例3", "描述3"));
        return ResponseResult.success("获取示例数据成功", demoList);
    }

    /**
     * 根据ID获取示例数据
     * @param id 示例ID
     * @return 示例数据
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID获取示例数据", notes = "根据ID获取示例数据详情")
    public ResponseResult<DemoVO> getById(@ApiParam(value = "示例ID", required = true) @PathVariable @NotNull(message = "ID不能为空") Long id) {
        log.info("根据ID获取示例数据，ID: {}", id);
        // 模拟业务逻辑
        if (id > 0 && id <= 3) {
            DemoVO demo = new DemoVO(id, "示例" + id, "描述" + id);
            return ResponseResult.success("获取示例数据成功", demo);
        } else {
            throw new BusinessException("示例数据不存在");
        }
    }

    /**
     * 创建示例数据
     * @param demo 示例数据
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation(value = "创建示例数据", notes = "创建新的示例数据")
    public ResponseResult<DemoVO> create(@ApiParam(value = "示例数据", required = true) @RequestBody @Valid DemoVO demo) {
        log.info("创建示例数据: {}", demo);
        // 模拟业务逻辑
        demo.setId(System.currentTimeMillis());
        return ResponseResult.success("创建示例数据成功", demo);
    }

    /**
     * 更新示例数据
     * @param id 示例ID
     * @param demo 示例数据
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新示例数据", notes = "根据ID更新示例数据")
    public ResponseResult<DemoVO> update(
            @ApiParam(value = "示例ID", required = true) @PathVariable @NotNull(message = "ID不能为空") Long id,
            @ApiParam(value = "示例数据", required = true) @RequestBody @Valid DemoVO demo) {
        log.info("更新示例数据，ID: {}, 数据: {}", id, demo);
        // 模拟业务逻辑
        if (id > 0 && id <= 3) {
            demo.setId(id);
            return ResponseResult.success("更新示例数据成功", demo);
        } else {
            throw new BusinessException("示例数据不存在");
        }
    }

    /**
     * 删除示例数据
     * @param id 示例ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除示例数据", notes = "根据ID删除示例数据")
    public ResponseResult<Void> delete(@ApiParam(value = "示例ID", required = true) @PathVariable @NotNull(message = "ID不能为空") Long id) {
        log.info("删除示例数据，ID: {}", id);
        // 模拟业务逻辑
        if (id > 0 && id <= 3) {
            return ResponseResult.success("删除示例数据成功");
        } else {
            throw new BusinessException("示例数据不存在");
        }
    }

    /**
     * 根据名称搜索示例数据
     * @param name 名称
     * @return 示例数据列表
     */
    @GetMapping("/search")
    @ApiOperation(value = "搜索示例数据", notes = "根据名称搜索示例数据")
    public ResponseResult<List<DemoVO>> search(@ApiParam(value = "名称", required = true) @RequestParam @NotBlank(message = "名称不能为空") String name) {
        log.info("搜索示例数据，名称: {}", name);
        // 模拟业务逻辑
        List<DemoVO> demoList = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            if (("示例" + i).contains(name)) {
                demoList.add(new DemoVO((long) i, "示例" + i, "描述" + i));
            }
        }
        return ResponseResult.success("搜索示例数据成功", demoList);
    }
} 