package com.sysgetway.core.controller;

import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.common.util.RedisUtils;
import com.sysgetway.core.common.util.ResponseResult;
import com.sysgetway.core.common.util.AsyncTaskManager;
import com.sysgetway.core.model.dto.SyncResultDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 监控控制器
 */
@RestController
@RequestMapping("/api/monitor")
@Api(tags = "系统监控接口")
@Slf4j
public class MonitorController {
    
    @Resource
    private RedisUtils redisUtils;
    
    @Resource
    private AsyncTaskManager asyncTaskManager;
    
    /**
     * 获取患者分类同步统计
     *
     * @param hospitalId 医院ID
     * @return 各类患者的同步统计信息
     */
    @GetMapping("/patient-sync/stats/{hospitalId}")
    @ApiOperation(value = "获取患者分类同步统计", notes = "获取指定医院各类患者的同步统计信息")
    @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path")
    public ResponseResult<Map<String, Object>> getPatientSyncStats(@PathVariable String hospitalId) {
        log.info("接收到获取患者分类同步统计请求，医院ID: {}", hospitalId);
        
        Map<String, Object> stats = new HashMap<>();
        
        // 获取各类患者的同步状态
        SyncResultDTO inStats = getSyncStatus(hospitalId, "patient:" + SyncConstants.PatientType.IN);
        SyncResultDTO upStats = getSyncStatus(hospitalId, "patient:" + SyncConstants.PatientType.UP);
        SyncResultDTO outStats = getSyncStatus(hospitalId, "patient:" + SyncConstants.PatientType.OUT);
        
        // 构建统计信息
        stats.put("inPatients", buildPatientTypeStat(inStats, SyncConstants.PatientType.IN));
        stats.put("upPatients", buildPatientTypeStat(upStats, SyncConstants.PatientType.UP));
        stats.put("outPatients", buildPatientTypeStat(outStats, SyncConstants.PatientType.OUT));
        
        // 计算总统计信息
        int totalCount = (inStats != null ? inStats.getCount() : 0) + 
                         (upStats != null ? upStats.getCount() : 0) + 
                         (outStats != null ? outStats.getCount() : 0);
        stats.put("totalCount", totalCount);
        
        // 获取全部患者同步状态
        SyncResultDTO allPatientStats = getSyncStatus(hospitalId, SyncConstants.TableName.PATIENT);
        stats.put("allPatients", buildSyncStat(allPatientStats));
        
        return ResponseResult.success(stats);
    }
    
    /**
     * 获取同步任务统计
     *
     * @return 同步任务统计信息
     */
    @GetMapping("/sync/stats")
    @ApiOperation(value = "获取同步任务统计", notes = "获取同步任务统计信息")
    public ResponseResult<Map<String, Object>> getSyncTaskStats() {
        log.info("接收到获取同步任务统计请求");
        
        Map<String, Object> stats = new HashMap<>();
        
        // 这里应该从某个集中存储中获取所有医院的同步任务统计信息
        // 由于没有实际存储，这里仅模拟返回一些统计数据
        
        // 模拟任务执行统计
        Map<String, Integer> taskStats = new HashMap<>();
        taskStats.put("totalTasks", 100);
        taskStats.put("successfulTasks", 90);
        taskStats.put("failedTasks", 5);
        taskStats.put("inProgressTasks", 5);
        
        // 模拟同步性能统计
        Map<String, Object> performanceStats = new HashMap<>();
        performanceStats.put("avgResponseTime", 3.5); // 秒
        performanceStats.put("maxResponseTime", 10.2); // 秒
        performanceStats.put("minResponseTime", 0.8); // 秒
        performanceStats.put("p95ResponseTime", 5.7); // 秒
        
        // 模拟数据量统计
        Map<String, Object> dataStats = new HashMap<>();
        dataStats.put("totalSyncedRecords", 50000);
        dataStats.put("departmentRecords", 100);
        dataStats.put("userRecords", 1000);
        dataStats.put("patientRecords", 48900);
        
        // 模拟医院统计
        Map<String, Object> hospitalStats = new HashMap<>();
        hospitalStats.put("totalHospitals", 10);
        hospitalStats.put("activeHospitals", 8);
        
        stats.put("taskStats", taskStats);
        stats.put("performanceStats", performanceStats);
        stats.put("dataStats", dataStats);
        stats.put("hospitalStats", hospitalStats);
        
        return ResponseResult.success(stats);
    }
    
    /**
     * 获取系统健康状态
     *
     * @return 系统健康状态
     */
    @GetMapping("/health")
    @ApiOperation(value = "获取系统健康状态", notes = "获取系统健康状态")
    public ResponseResult<Map<String, Object>> getSystemHealth() {
        log.info("接收到获取系统健康状态请求");
        
        Map<String, Object> health = new HashMap<>();
        
        // 系统状态
        health.put("status", "UP");
        
        // Redis健康状况
        Map<String, Object> redis = new HashMap<>();
        try {
            // 测试Redis连接
            redisUtils.set("health_check", "ok", 10);
            String value = (String) redisUtils.get("health_check");
            redis.put("status", "UP");
            redis.put("version", "5.0.7");
        } catch (Exception e) {
            redis.put("status", "DOWN");
            redis.put("error", e.getMessage());
            health.put("status", "DOWN");
        }
        
        // JVM指标
        Map<String, Object> jvm = new HashMap<>();
        jvm.put("status", "UP");
        jvm.put("heapUsed", Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory());
        jvm.put("heapTotal", Runtime.getRuntime().totalMemory());
        jvm.put("processors", Runtime.getRuntime().availableProcessors());
        
        // 磁盘空间
        Map<String, Object> diskSpace = new HashMap<>();
        diskSpace.put("status", "UP");
        diskSpace.put("free", "8.5 GB");
        diskSpace.put("threshold", "1 GB");
        
        // 组合健康状态
        health.put("redis", redis);
        health.put("jvm", jvm);
        health.put("diskSpace", diskSpace);
        health.put("timestamp", System.currentTimeMillis());
        
        return ResponseResult.success(health);
    }
    
    /**
     * 获取系统状态
     *
     * @return 系统状态信息
     */
    @GetMapping("/status")
    @ApiOperation(value = "获取系统状态", notes = "获取系统运行状态，包括JVM信息、内存使用情况等")
    public ResponseResult<Map<String, Object>> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 获取JVM信息
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> jvmInfo = new HashMap<>();
        jvmInfo.put("totalMemory", runtime.totalMemory() / 1024 / 1024 + "MB");
        jvmInfo.put("freeMemory", runtime.freeMemory() / 1024 / 1024 + "MB");
        jvmInfo.put("maxMemory", runtime.maxMemory() / 1024 / 1024 + "MB");
        jvmInfo.put("availableProcessors", runtime.availableProcessors());
        status.put("jvm", jvmInfo);
        
        // 获取系统信息
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("osName", System.getProperty("os.name"));
        systemInfo.put("osVersion", System.getProperty("os.version"));
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        status.put("system", systemInfo);
        
        // 获取任务信息
        Map<String, Object> taskInfo = new HashMap<>();
        taskInfo.put("runningTaskCount", asyncTaskManager.getTaskCount());
        status.put("task", taskInfo);
        
        return ResponseResult.success(status);
    }
    
    /**
     * 获取正在运行的任务列表
     *
     * @return 任务列表
     */
    @GetMapping("/tasks")
    @ApiOperation(value = "获取任务列表", notes = "获取当前正在运行的异步任务列表")
    public ResponseResult<List<String>> getRunningTasks() {
        List<String> tasks = new ArrayList<>();
        
        // 获取所有任务键
        for (String taskKey : asyncTaskManager.getAllTaskKeys()) {
            tasks.add(taskKey);
        }
        
        return ResponseResult.success(tasks);
    }
    
    /**
     * 获取系统内存使用情况
     *
     * @return 内存使用情况
     */
    @GetMapping("/memory")
    @ApiOperation(value = "获取内存使用情况", notes = "获取系统内存使用详情")
    public ResponseResult<Map<String, Object>> getMemoryUsage() {
        Map<String, Object> memoryInfo = new HashMap<>();
        
        // 获取JVM内存信息
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        long usedMemory = totalMemory - freeMemory;
        
        memoryInfo.put("total", totalMemory / 1024 / 1024);
        memoryInfo.put("free", freeMemory / 1024 / 1024);
        memoryInfo.put("used", usedMemory / 1024 / 1024);
        memoryInfo.put("max", maxMemory / 1024 / 1024);
        memoryInfo.put("usageRate", String.format("%.2f%%", (double) usedMemory / totalMemory * 100));
        
        return ResponseResult.success(memoryInfo);
    }
    
    /**
     * 获取同步状态
     *
     * @param hospitalId 医院ID
     * @param tableNameOrType 表名或患者类型
     * @return 同步状态
     */
    private SyncResultDTO getSyncStatus(String hospitalId, String tableNameOrType) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableNameOrType);
        Object obj = redisUtils.get(key);
        if (obj != null) {
            return com.alibaba.fastjson2.JSON.parseObject(obj.toString(), SyncResultDTO.class);
        }
        return null;
    }
    
    /**
     * 构建患者类型统计信息
     *
     * @param syncResult 同步结果
     * @param patientType 患者类型
     * @return 统计信息
     */
    private Map<String, Object> buildPatientTypeStat(SyncResultDTO syncResult, String patientType) {
        Map<String, Object> stat = buildSyncStat(syncResult);
        stat.put("patientType", patientType);
        
        // 根据患者类型添加额外信息
        switch (patientType) {
            case SyncConstants.PatientType.IN:
                stat.put("description", "入院患者");
                break;
            case SyncConstants.PatientType.UP:
                stat.put("description", "在院患者");
                break;
            case SyncConstants.PatientType.OUT:
                stat.put("description", "出院患者");
                break;
        }
        
        return stat;
    }
    
    /**
     * 构建同步统计信息
     *
     * @param syncResult 同步结果
     * @return 统计信息
     */
    private Map<String, Object> buildSyncStat(SyncResultDTO syncResult) {
        Map<String, Object> stat = new HashMap<>();
        
        if (syncResult == null) {
            stat.put("status", "未同步");
            stat.put("count", 0);
            stat.put("lastSyncTime", null);
            return stat;
        }
        
        // 基本信息
        stat.put("status", getStatusText(syncResult.getStatus()));
        stat.put("message", syncResult.getMessage());
        stat.put("count", syncResult.getCount());
        stat.put("startTime", syncResult.getStartTime());
        stat.put("endTime", syncResult.getEndTime());
        stat.put("costTime", syncResult.getCostTime());
        
        // 如果有错误信息，添加到统计中
        if (syncResult.getStatus() == SyncConstants.SyncStatus.FAILED && syncResult.getErrorMessage() != null) {
            stat.put("errorMessage", syncResult.getErrorMessage());
            stat.put("retryCount", syncResult.getRetryCount());
        }
        
        return stat;
    }
    
    /**
     * 获取状态文本
     *
     * @param status 状态码
     * @return 状态文本
     */
    private String getStatusText(Integer status) {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case SyncConstants.SyncStatus.SUCCESS:
                return "成功";
            case SyncConstants.SyncStatus.IN_PROGRESS:
                return "进行中";
            case SyncConstants.SyncStatus.FAILED:
                return "失败";
            default:
                return "未知状态: " + status;
        }
    }
} 