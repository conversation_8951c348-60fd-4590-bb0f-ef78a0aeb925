package com.sysgetway.core.controller;

import com.alibaba.fastjson2.JSON;
import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.common.exception.BusinessException;
import com.sysgetway.core.common.util.RedisUtils;
import com.sysgetway.core.common.util.ResponseResult;
import com.sysgetway.core.entity.Department;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.entity.User;
import com.sysgetway.core.model.vo.HospitalDataImportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 数据查询控制器
 */
@RestController
@RequestMapping("/api")
@Api(tags = "数据查询接口")
@Slf4j
public class DataController {
    
    @Resource
    private RedisUtils redisUtils;
    
    /**
     * 患者分类查询接口
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型
     * @return 患者列表
     */
    @GetMapping("/patients/{hospitalId}/{patientType}")
    @ApiOperation(value = "患者分类查询", notes = "根据患者分类查询指定医院的患者数据。patientType可选值：in(入院患者)、up(在院患者)、out(出院患者)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "patientType", value = "患者类型", required = true, paramType = "path", 
                    allowableValues = "in,up,out")
    })
    public ResponseResult<List<Patient>> getPatientsByType(@PathVariable String hospitalId, @PathVariable String patientType) {
        log.info("接收到患者分类查询请求，医院ID: {}，患者类型: {}", hospitalId, patientType);
        
        // 验证患者类型是否有效
        if (!SyncConstants.PatientType.isValid(patientType)) {
            log.error("无效的患者类型: {}", patientType);
            throw new BusinessException("无效的患者类型: " + patientType);
        }
        
        // 从Redis获取数据
        String redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT_TYPE + hospitalId + ":" + patientType;
        Object obj = redisUtils.get(redisKey);
        
        List<Patient> patients = new ArrayList<>();
        if (obj != null) {
            patients = com.alibaba.fastjson2.JSON.parseArray(obj.toString(), Patient.class);
        }
        
        return ResponseResult.success(patients);
    }
    
    /**
     * 患者分类统计接口
     *
     * @param hospitalId 医院ID
     * @return 各分类患者的统计数据
     */
    @GetMapping("/patients/{hospitalId}/stats")
    @ApiOperation(value = "患者分类统计", notes = "获取指定医院各分类患者的统计数据")
    @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path")
    public ResponseResult<Map<String, Integer>> getPatientStats(@PathVariable String hospitalId) {
        log.info("接收到患者分类统计请求，医院ID: {}", hospitalId);
        
        Map<String, Integer> stats = new HashMap<>();
        
        // 分别查询各类患者数量
        String inKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT_TYPE + hospitalId + ":" + SyncConstants.PatientType.IN;
        String upKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT_TYPE + hospitalId + ":" + SyncConstants.PatientType.UP;
        String outKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT_TYPE + hospitalId + ":" + SyncConstants.PatientType.OUT;
        
        Object inObj = redisUtils.get(inKey);
        Object upObj = redisUtils.get(upKey);
        Object outObj = redisUtils.get(outKey);
        
        // 计算各类患者数量
        stats.put("inPatients", inObj != null ? com.alibaba.fastjson2.JSON.parseArray(inObj.toString(), Patient.class).size() : 0);
        stats.put("upPatients", upObj != null ? com.alibaba.fastjson2.JSON.parseArray(upObj.toString(), Patient.class).size() : 0);
        stats.put("outPatients", outObj != null ? com.alibaba.fastjson2.JSON.parseArray(outObj.toString(), Patient.class).size() : 0);
        
        // 计算总患者数量
        stats.put("totalPatients", stats.get("inPatients") + stats.get("upPatients") + stats.get("outPatients"));
        
        return ResponseResult.success(stats);
    }
    
    /**
     * 导入患者数据
     *
     * @param hospitalId 医院ID
     * @param patients 患者数据列表
     * @return 导入结果
     */
    @PostMapping("/patients/{hospitalId}/import")
    @ApiOperation(value = "导入患者数据", notes = "导入指定医院的患者数据，并存储到Redis中。接口会自动对患者进行分类（入院、在院、出院）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "patients", value = "患者数据列表", required = true, paramType = "body", dataType = "List<Patient>",
                    example = "[{\"inpatientInfoId\":\"P001\",\"name\":\"张三\",\"idCard\":\"110101199001011234\",\"mobile\":\"13800138006\"," +
                             "\"sex\":1,\"age\":30,\"hospitalizationNo\":\"H001\",\"inhospitalDiagnose\":\"高血压\",\"deptId\":2," +
                             "\"sickbedNo\":\"2-101\",\"doctorId\":\"D002\",\"nurseId\":\"N002\",\"nurseLevel\":2," +
                             "\"inhospitalTime\":\"2023-06-10T10:00:00\",\"outhospitalTime\":null,\"status\":1,\"category\":\"医保\"}]")
    })
    public ResponseResult<Map<String, Object>> importPatients(@PathVariable String hospitalId, @RequestBody List<Patient> patients) {
        log.info("接收到导入患者数据请求，医院ID: {}，患者数量: {}", hospitalId, patients.size());
        
        if (patients == null || patients.isEmpty()) {
            return ResponseResult.error("患者数据不能为空");
        }
        
        try {
            // 更新患者数据的时间戳
            for (Patient patient : patients) {
                if (patient.getUpdatedAt() == null) {
                    patient.setUpdatedAt(LocalDateTime.now());
                }
            }
            
            // 将患者数据保存到Redis（使用Hash结构）
            String redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT + hospitalId;
            
            // 创建一个Map来存储所有患者数据
            Map<String, Object> allPatientsMap = new HashMap<>();
            int validPatientCount = 0;
            int skippedPatientCount = 0;
            
            for (Patient patient : patients) {
                // 使用患者的inpatientInfoId作为Hash的字段名，如果为null则使用其他唯一标识
                String fieldName = null;
                try {
                    if (patient.getInpatientInfoId() != null) {
                        fieldName = patient.getInpatientInfoId();
                        log.debug("使用inpatientInfoId作为字段名: {}", fieldName);
                    } else if (patient.getIdCard() != null) {
                        // 尝试使用身份证号作为备选字段名
                        fieldName = "id_" + patient.getIdCard();
                        log.debug("使用身份证号作为字段名: {}", fieldName);
                    } else if (patient.getHospitalizationNo() != null) {
                        // 尝试使用住院号作为备选字段名
                        fieldName = "hosp_" + patient.getHospitalizationNo();
                        log.debug("使用住院号作为字段名: {}", fieldName);
                    } else if (patient.getMobile() != null) {
                        // 尝试使用手机号作为备选字段名
                        fieldName = "mobile_" + patient.getMobile();
                        log.debug("使用手机号作为字段名: {}", fieldName);
                    } else if (patient.getName() != null && patient.getBirthday() != null) {
                        // 尝试使用姓名+生日作为备选字段名
                        fieldName = "name_birth_" + patient.getName() + "_" + patient.getBirthday();
                        log.debug("使用姓名+生日作为字段名: {}", fieldName);
                    } else if (patient.getName() != null && patient.getAge() != null) {
                        // 尝试使用姓名+年龄作为备选字段名
                        fieldName = "name_age_" + patient.getName() + "_" + patient.getAge();
                        log.debug("使用姓名+年龄作为字段名: {}", fieldName);
                    } else if (patient.getName() != null) {
                        // 只有姓名时，添加时间戳确保唯一性
                        fieldName = "name_" + patient.getName() + "_" + System.currentTimeMillis();
                        log.debug("使用姓名+时间戳作为字段名: {}", fieldName);
                    } else {
                        // 如果没有可用的唯一标识，生成一个随机ID
                        fieldName = "random_" + UUID.randomUUID().toString().replace("-", "");
                        log.debug("使用随机UUID作为字段名: {}", fieldName);
                    }
                } catch (Exception e) {
                    // 捕获任何可能的异常，确保字段名生成不会失败
                    log.error("生成字段名时发生异常: {}", e.getMessage(), e);
                    fieldName = "error_" + UUID.randomUUID().toString().replace("-", "");
                    log.debug("异常情况下使用随机UUID作为字段名: {}", fieldName);
                }
                
                // 确保字段名不为null，这是一个额外的安全检查
                if (fieldName == null) {
                    fieldName = "fallback_" + UUID.randomUUID().toString().replace("-", "");
                    log.warn("所有字段名生成方法失败，使用后备随机UUID: {}", fieldName);
                }
                
                try {
                    String patientJson = JSON.toJSONString(patient);
                    allPatientsMap.put(fieldName, patientJson);
                    validPatientCount++;
                } catch (Exception e) {
                    log.error("将患者数据转换为JSON时发生异常: {}", e.getMessage(), e);
                    skippedPatientCount++;
                }
            }
            
            log.info("有效患者数据: {}, 跳过的患者数据: {}", validPatientCount, skippedPatientCount);
            
            if (!allPatientsMap.isEmpty()) {
                // 删除旧数据
                if (redisUtils.hasKey(redisKey)) {
                    redisUtils.delete(redisKey);
                }
                
                // 使用Hash结构存储所有患者数据
                redisUtils.hSetAll(redisKey, allPatientsMap);
                
                // 设置过期时间
                redisUtils.expire(redisKey, SyncConstants.ExpireTime.DEFAULT);
            } else {
                log.warn("没有有效的患者数据可存储");
            }
            
            // 同时按患者类型保存数据
            Map<String, List<Patient>> patientTypeMap = new HashMap<>();
            patientTypeMap.put(SyncConstants.PatientType.IN, new ArrayList<>());
            patientTypeMap.put(SyncConstants.PatientType.UP, new ArrayList<>());
            patientTypeMap.put(SyncConstants.PatientType.OUT, new ArrayList<>());
            
            // 分类患者
            for (Patient patient : patients) {
                try {
                    if (patient.getInhospitalTime() != null && patient.getOuthospitalTime() == null) {
                        // 入院患者：有入院时间，没有出院时间
                        patientTypeMap.get(SyncConstants.PatientType.IN).add(patient);
                    }
                    
                    if (patient.getStatus() != null && patient.getStatus() == 1) {
                        // 在院患者：状态为在院（1）
                        patientTypeMap.get(SyncConstants.PatientType.UP).add(patient);
                    }
                    
                    if (patient.getStatus() != null && patient.getStatus() == 2) {
                        // 出院患者：状态为出院（2）
                        patientTypeMap.get(SyncConstants.PatientType.OUT).add(patient);
                    }
                } catch (Exception e) {
                    // 处理单个患者分类异常，但继续处理其他患者
                    log.error("患者[{}]分类处理异常: {}", patient.getInpatientInfoId(), e.getMessage());
                }
            }
            
            // 保存各类型患者数据（使用Hash结构）
            for (Map.Entry<String, List<Patient>> entry : patientTypeMap.entrySet()) {
                String typeRedisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT_TYPE + hospitalId + ":" + entry.getKey();
                
                // 创建一个Map来存储特定类型的患者数据
                Map<String, Object> typePatientsMap = new HashMap<>();
                int typeValidCount = 0;
                
                for (Patient patient : entry.getValue()) {
                    // 使用患者的inpatientInfoId作为Hash的字段名，如果为null则使用其他唯一标识
                    String fieldName = null;
                    try {
                        if (patient.getInpatientInfoId() != null) {
                            fieldName = patient.getInpatientInfoId();
                            log.debug("使用inpatientInfoId作为字段名: {}", fieldName);
                        } else if (patient.getIdCard() != null) {
                            // 尝试使用身份证号作为备选字段名
                            fieldName = "id_" + patient.getIdCard();
                            log.debug("使用身份证号作为字段名: {}", fieldName);
                        } else if (patient.getHospitalizationNo() != null) {
                            // 尝试使用住院号作为备选字段名
                            fieldName = "hosp_" + patient.getHospitalizationNo();
                            log.debug("使用住院号作为字段名: {}", fieldName);
                        } else if (patient.getMobile() != null) {
                            // 尝试使用手机号作为备选字段名
                            fieldName = "mobile_" + patient.getMobile();
                            log.debug("使用手机号作为字段名: {}", fieldName);
                        } else if (patient.getName() != null && patient.getBirthday() != null) {
                            // 尝试使用姓名+生日作为备选字段名
                            fieldName = "name_birth_" + patient.getName() + "_" + patient.getBirthday();
                            log.debug("使用姓名+生日作为字段名: {}", fieldName);
                        } else if (patient.getName() != null && patient.getAge() != null) {
                            // 尝试使用姓名+年龄作为备选字段名
                            fieldName = "name_age_" + patient.getName() + "_" + patient.getAge();
                            log.debug("使用姓名+年龄作为字段名: {}", fieldName);
                        } else if (patient.getName() != null) {
                            // 只有姓名时，添加时间戳确保唯一性
                            fieldName = "name_" + patient.getName() + "_" + System.currentTimeMillis();
                            log.debug("使用姓名+时间戳作为字段名: {}", fieldName);
                        } else {
                            // 如果没有可用的唯一标识，生成一个随机ID
                            fieldName = "random_" + UUID.randomUUID().toString().replace("-", "");
                            log.debug("使用随机UUID作为字段名: {}", fieldName);
                        }
                    } catch (Exception e) {
                        // 捕获任何可能的异常，确保字段名生成不会失败
                        log.error("生成字段名时发生异常: {}", e.getMessage(), e);
                        fieldName = "error_" + UUID.randomUUID().toString().replace("-", "");
                        log.debug("异常情况下使用随机UUID作为字段名: {}", fieldName);
                    }
                    
                    // 确保字段名不为null，这是一个额外的安全检查
                    if (fieldName == null) {
                        fieldName = "fallback_" + UUID.randomUUID().toString().replace("-", "");
                        log.warn("所有字段名生成方法失败，使用后备随机UUID: {}", fieldName);
                    }
                    
                    try {
                        String patientJson = JSON.toJSONString(patient);
                        typePatientsMap.put(fieldName, patientJson);
                        typeValidCount++;
                    } catch (Exception e) {
                        log.error("将患者数据转换为JSON时发生异常: {}", e.getMessage(), e);
                    }
                }
                
                // 删除旧数据
                if (redisUtils.hasKey(typeRedisKey)) {
                    redisUtils.delete(typeRedisKey);
                }
                
                // 使用Hash结构存储特定类型的患者数据
                if (!typePatientsMap.isEmpty()) {
                    redisUtils.hSetAll(typeRedisKey, typePatientsMap);
                    
                    // 设置过期时间
                    redisUtils.expire(typeRedisKey, SyncConstants.ExpireTime.DEFAULT);
                }
                
                // 更新统计数据
                if (SyncConstants.PatientType.IN.equals(entry.getKey())) {
                    patientTypeMap.put(SyncConstants.PatientType.IN, new ArrayList<>(entry.getValue().subList(0, typeValidCount)));
                } else if (SyncConstants.PatientType.UP.equals(entry.getKey())) {
                    patientTypeMap.put(SyncConstants.PatientType.UP, new ArrayList<>(entry.getValue().subList(0, typeValidCount)));
                } else if (SyncConstants.PatientType.OUT.equals(entry.getKey())) {
                    patientTypeMap.put(SyncConstants.PatientType.OUT, new ArrayList<>(entry.getValue().subList(0, typeValidCount)));
                }
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", validPatientCount);
            result.put("inPatients", patientTypeMap.get(SyncConstants.PatientType.IN).size());
            result.put("upPatients", patientTypeMap.get(SyncConstants.PatientType.UP).size());
            result.put("outPatients", patientTypeMap.get(SyncConstants.PatientType.OUT).size());
            
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("导入患者数据失败: {}", e.getMessage(), e);
            return ResponseResult.error("导入患者数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 综合数据导入接口，同时处理科室、医护人员和患者数据
     *
     * @param hospitalId 医院ID
     * @param dataImport 数据导入对象，包含科室、医护人员和患者数据
     * @return 导入结果
     */
    @PostMapping("/hospital/{hospitalId}/import")
    @ApiOperation(value = "综合数据导入", notes = "导入指定医院的综合数据，包括科室、医护人员和患者数据，并存储到Redis中")
    @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path")
    public ResponseResult<Map<String, Object>> importHospitalData(@PathVariable String hospitalId, 
                                                             @RequestBody HospitalDataImportVO dataImport) {
        log.info("接收到综合数据导入请求，医院ID: {}", hospitalId);
        
        // 初始化结果Map
        Map<String, Object> result = new HashMap<>();
        int totalDepartments = 0;
        int totalUsers = 0;
        int totalPatients = 0;
        Map<String, Integer> patientTypeCount = new HashMap<>();
        patientTypeCount.put("inPatients", 0);
        patientTypeCount.put("upPatients", 0);
        patientTypeCount.put("outPatients", 0);
        
        try {
            // 1. 处理科室数据
            List<Department> departments = dataImport.getDepartments();
            if (departments != null && !departments.isEmpty()) {
                // 更新数据时间戳
                departments.forEach(dept -> {
                    if (dept.getUpdatedAt() == null) {
                        dept.setUpdatedAt(LocalDateTime.now());
                    }
                });
                
                // 保存到Redis
                String deptRedisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_DEPARTMENT + hospitalId;
                redisUtils.set(deptRedisKey, JSON.toJSONString(departments), SyncConstants.ExpireTime.DEFAULT);
                totalDepartments = departments.size();
                log.info("成功导入{}个科室数据", totalDepartments);
            }
            
            // 2. 处理医护人员数据
            List<User> users = dataImport.getUsers();
            if (users != null && !users.isEmpty()) {
                // 更新数据时间戳
                users.forEach(user -> {
                    if (user.getUpdatedAt() == null) {
                        user.setUpdatedAt(LocalDateTime.now());
                    }
                });
                
                // 保存到Redis
                String userRedisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_USER + hospitalId;
                redisUtils.set(userRedisKey, JSON.toJSONString(users), SyncConstants.ExpireTime.DEFAULT);
                totalUsers = users.size();
                log.info("成功导入{}个医护人员数据", totalUsers);
            }
            
            // 3. 处理患者数据
            List<Patient> patients = dataImport.getPatients();
            if (patients != null && !patients.isEmpty()) {
                // 分类患者数据
                Map<String, List<Patient>> patientTypeMap = new HashMap<>();
                patientTypeMap.put(SyncConstants.PatientType.IN, new ArrayList<>());
                patientTypeMap.put(SyncConstants.PatientType.UP, new ArrayList<>());
                patientTypeMap.put(SyncConstants.PatientType.OUT, new ArrayList<>());
                
                // 更新时间戳并分类
                for (Patient patient : patients) {
                    if (patient.getUpdatedAt() == null) {
                        patient.setUpdatedAt(LocalDateTime.now());
                    }
                    
                    try {
                        // 入院患者：有入院时间，没有出院时间
                        if (patient.getInhospitalTime() != null && patient.getOuthospitalTime() == null) {
                            patientTypeMap.get(SyncConstants.PatientType.IN).add(patient);
                        }
                        
                        // 在院患者：状态为在院（1）
                        if (patient.getStatus() != null && patient.getStatus() == 1) {
                            patientTypeMap.get(SyncConstants.PatientType.UP).add(patient);
                        }
                        
                        // 出院患者：状态为出院（2）
                        if (patient.getStatus() != null && patient.getStatus() == 2) {
                            patientTypeMap.get(SyncConstants.PatientType.OUT).add(patient);
                        }
                    } catch (Exception e) {
                        log.error("患者[{}]分类处理异常: {}", patient.getInpatientInfoId(), e.getMessage());
                    }
                }
                
                // 保存患者总数据（使用Hash结构）
                String patientRedisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT + hospitalId;
                
                // 创建一个Map来存储所有患者数据
                Map<String, Object> allPatientsMap = new HashMap<>();
                int validPatientCount = 0;
                int skippedPatientCount = 0;
                
                for (Patient patient : patients) {
                    // 使用患者的inpatientInfoId作为Hash的字段名，如果为null则使用其他唯一标识
                    String fieldName = null;
                    try {
                        if (patient.getInpatientInfoId() != null) {
                            fieldName = patient.getInpatientInfoId();
                            log.debug("使用inpatientInfoId作为字段名: {}", fieldName);
                        } else if (patient.getIdCard() != null) {
                            // 尝试使用身份证号作为备选字段名
                            fieldName = "id_" + patient.getIdCard();
                            log.debug("使用身份证号作为字段名: {}", fieldName);
                        } else if (patient.getHospitalizationNo() != null) {
                            // 尝试使用住院号作为备选字段名
                            fieldName = "hosp_" + patient.getHospitalizationNo();
                            log.debug("使用住院号作为字段名: {}", fieldName);
                        } else if (patient.getMobile() != null) {
                            // 尝试使用手机号作为备选字段名
                            fieldName = "mobile_" + patient.getMobile();
                            log.debug("使用手机号作为字段名: {}", fieldName);
                        } else if (patient.getName() != null && patient.getBirthday() != null) {
                            // 尝试使用姓名+生日作为备选字段名
                            fieldName = "name_birth_" + patient.getName() + "_" + patient.getBirthday();
                            log.debug("使用姓名+生日作为字段名: {}", fieldName);
                        } else if (patient.getName() != null && patient.getAge() != null) {
                            // 尝试使用姓名+年龄作为备选字段名
                            fieldName = "name_age_" + patient.getName() + "_" + patient.getAge();
                            log.debug("使用姓名+年龄作为字段名: {}", fieldName);
                        } else if (patient.getName() != null) {
                            // 只有姓名时，添加时间戳确保唯一性
                            fieldName = "name_" + patient.getName() + "_" + System.currentTimeMillis();
                            log.debug("使用姓名+时间戳作为字段名: {}", fieldName);
                        } else {
                            // 如果没有可用的唯一标识，生成一个随机ID
                            fieldName = "random_" + UUID.randomUUID().toString().replace("-", "");
                            log.debug("使用随机UUID作为字段名: {}", fieldName);
                        }
                    } catch (Exception e) {
                        // 捕获任何可能的异常，确保字段名生成不会失败
                        log.error("生成字段名时发生异常: {}", e.getMessage(), e);
                        fieldName = "error_" + UUID.randomUUID().toString().replace("-", "");
                        log.debug("异常情况下使用随机UUID作为字段名: {}", fieldName);
                    }
                    
                    // 确保字段名不为null，这是一个额外的安全检查
                    if (fieldName == null) {
                        fieldName = "fallback_" + UUID.randomUUID().toString().replace("-", "");
                        log.warn("所有字段名生成方法失败，使用后备随机UUID: {}", fieldName);
                    }
                    
                    try {
                        String patientJson = JSON.toJSONString(patient);
                        allPatientsMap.put(fieldName, patientJson);
                        validPatientCount++;
                    } catch (Exception e) {
                        log.error("将患者数据转换为JSON时发生异常: {}", e.getMessage(), e);
                        skippedPatientCount++;
                    }
                }
                
                log.info("有效患者数据: {}, 跳过的患者数据: {}", validPatientCount, skippedPatientCount);
                
                if (!allPatientsMap.isEmpty()) {
                    // 删除旧数据
                    if (redisUtils.hasKey(patientRedisKey)) {
                        redisUtils.delete(patientRedisKey);
                    }
                    
                    // 使用Hash结构存储所有患者数据
                    redisUtils.hSetAll(patientRedisKey, allPatientsMap);
                    
                    // 设置过期时间
                    redisUtils.expire(patientRedisKey, SyncConstants.ExpireTime.DEFAULT);
                } else {
                    log.warn("没有有效的患者数据可存储");
                }
                
                totalPatients = validPatientCount;
                
                // 保存各类型患者数据（使用Hash结构）
                for (Map.Entry<String, List<Patient>> entry : patientTypeMap.entrySet()) {
                    String typeRedisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT_TYPE + hospitalId + ":" + entry.getKey();
                    
                    // 创建一个Map来存储特定类型的患者数据
                    Map<String, Object> typePatientsMap = new HashMap<>();
                    int typeValidCount = 0;
                    
                    for (Patient patient : entry.getValue()) {
                        // 使用患者的inpatientInfoId作为Hash的字段名，如果为null则使用其他唯一标识
                        String fieldName = null;
                        try {
                            if (patient.getInpatientInfoId() != null) {
                                fieldName = patient.getInpatientInfoId();
                                log.debug("使用inpatientInfoId作为字段名: {}", fieldName);
                            } else if (patient.getIdCard() != null) {
                                // 尝试使用身份证号作为备选字段名
                                fieldName = "id_" + patient.getIdCard();
                                log.debug("使用身份证号作为字段名: {}", fieldName);
                            } else if (patient.getHospitalizationNo() != null) {
                                // 尝试使用住院号作为备选字段名
                                fieldName = "hosp_" + patient.getHospitalizationNo();
                                log.debug("使用住院号作为字段名: {}", fieldName);
                            } else if (patient.getMobile() != null) {
                                // 尝试使用手机号作为备选字段名
                                fieldName = "mobile_" + patient.getMobile();
                                log.debug("使用手机号作为字段名: {}", fieldName);
                            } else if (patient.getName() != null && patient.getBirthday() != null) {
                                // 尝试使用姓名+生日作为备选字段名
                                fieldName = "name_birth_" + patient.getName() + "_" + patient.getBirthday();
                                log.debug("使用姓名+生日作为字段名: {}", fieldName);
                            } else if (patient.getName() != null && patient.getAge() != null) {
                                // 尝试使用姓名+年龄作为备选字段名
                                fieldName = "name_age_" + patient.getName() + "_" + patient.getAge();
                                log.debug("使用姓名+年龄作为字段名: {}", fieldName);
                            } else if (patient.getName() != null) {
                                // 只有姓名时，添加时间戳确保唯一性
                                fieldName = "name_" + patient.getName() + "_" + System.currentTimeMillis();
                                log.debug("使用姓名+时间戳作为字段名: {}", fieldName);
                            } else {
                                // 如果没有可用的唯一标识，生成一个随机ID
                                fieldName = "random_" + UUID.randomUUID().toString().replace("-", "");
                                log.debug("使用随机UUID作为字段名: {}", fieldName);
                            }
                        } catch (Exception e) {
                            // 捕获任何可能的异常，确保字段名生成不会失败
                            log.error("生成字段名时发生异常: {}", e.getMessage(), e);
                            fieldName = "error_" + UUID.randomUUID().toString().replace("-", "");
                            log.debug("异常情况下使用随机UUID作为字段名: {}", fieldName);
                        }
                        
                        // 确保字段名不为null，这是一个额外的安全检查
                        if (fieldName == null) {
                            fieldName = "fallback_" + UUID.randomUUID().toString().replace("-", "");
                            log.warn("所有字段名生成方法失败，使用后备随机UUID: {}", fieldName);
                        }
                        
                        try {
                            String patientJson = JSON.toJSONString(patient);
                            typePatientsMap.put(fieldName, patientJson);
                            typeValidCount++;
                        } catch (Exception e) {
                            log.error("将患者数据转换为JSON时发生异常: {}", e.getMessage(), e);
                        }
                    }
                    
                    // 删除旧数据
                    if (redisUtils.hasKey(typeRedisKey)) {
                        redisUtils.delete(typeRedisKey);
                    }
                    
                    // 使用Hash结构存储特定类型的患者数据
                    if (!typePatientsMap.isEmpty()) {
                        redisUtils.hSetAll(typeRedisKey, typePatientsMap);
                        
                        // 设置过期时间
                        redisUtils.expire(typeRedisKey, SyncConstants.ExpireTime.DEFAULT);
                    }
                    
                    // 记录各类型数量
                    if (SyncConstants.PatientType.IN.equals(entry.getKey())) {
                        patientTypeCount.put("inPatients", typeValidCount);
                    } else if (SyncConstants.PatientType.UP.equals(entry.getKey())) {
                        patientTypeCount.put("upPatients", typeValidCount);
                    } else if (SyncConstants.PatientType.OUT.equals(entry.getKey())) {
                        patientTypeCount.put("outPatients", typeValidCount);
                    }
                }
                
                log.info("成功导入{}个患者数据", totalPatients);
            }
            
            // 4. 更新同步时间
            LocalDateTime syncTime = LocalDateTime.now();
            saveLastSyncTimeToRedis(hospitalId, SyncConstants.TableName.DEPARTMENT, syncTime);
            saveLastSyncTimeToRedis(hospitalId, SyncConstants.TableName.USER, syncTime);
            saveLastSyncTimeToRedis(hospitalId, SyncConstants.TableName.PATIENT, syncTime);
            
            // 构建结果
            result.put("totalDepartments", totalDepartments);
            result.put("totalUsers", totalUsers);
            result.put("totalPatients", totalPatients);
            result.put("patientTypeCount", patientTypeCount);
            result.put("syncTime", syncTime.toString());
            
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("导入医院综合数据失败", e);
            return ResponseResult.error("导入医院综合数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询Redis数据
     *
     * @param hospitalId 医院ID
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return Redis数据
     */
    @GetMapping("/data/{hospitalId}/{entityType}/{entityId}")
    @ApiOperation(value = "查询单个实体数据", notes = "查询指定医院指定实体的Redis数据。entityType可选值：department, user, patient")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "entityType", value = "实体类型", required = true, paramType = "path", 
                    allowableValues = "department,user,patient"),
            @ApiImplicitParam(name = "entityId", value = "实体ID", required = true, paramType = "path")
    })
    public ResponseResult<Object> getEntityData(@PathVariable String hospitalId, @PathVariable String entityType, 
                                                @PathVariable String entityId) {
        log.info("接收到查询实体数据请求，医院ID: {}，实体类型: {}，实体ID: {}", hospitalId, entityType, entityId);
        
        // 验证实体类型是否有效
        if (!isValidEntityType(entityType)) {
            log.error("无效的实体类型: {}", entityType);
            throw new BusinessException("无效的实体类型: " + entityType);
        }
        
        // 构建Redis键
        String redisKey = buildEntityRedisKey(hospitalId, entityType);
        Object obj = redisUtils.get(redisKey);
        
        if (obj == null) {
            return ResponseResult.success(null);
        }
        
        // 解析列表数据
        List<?> entityList = com.alibaba.fastjson2.JSON.parseArray(obj.toString(), getEntityClass(entityType));
        
        // 查找特定ID的实体
        Object result = null;
        for (Object entity : entityList) {
            String id = getEntityId(entity, entityType);
            if (entityId.equals(id)) {
                result = entity;
                break;
            }
        }
        
        return ResponseResult.success(result);
    }
    
    /**
     * 批量查询Redis数据
     *
     * @param hospitalId 医院ID
     * @param entityType 实体类型
     * @param ids 实体ID列表
     * @return Redis数据列表
     */
    @PostMapping("/data/{hospitalId}/{entityType}/batch")
    @ApiOperation(value = "批量查询实体数据", notes = "批量查询指定医院指定实体类型的Redis数据。entityType可选值：department, user, patient")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "entityType", value = "实体类型", required = true, paramType = "path", 
                    allowableValues = "department,user,patient")
    })
    public ResponseResult<List<Object>> batchGetEntityData(@PathVariable String hospitalId, @PathVariable String entityType, 
                                                         @RequestBody List<String> ids) {
        log.info("接收到批量查询实体数据请求，医院ID: {}，实体类型: {}，ID数量: {}", hospitalId, entityType, ids.size());
        
        // 验证实体类型是否有效
        if (!isValidEntityType(entityType)) {
            log.error("无效的实体类型: {}", entityType);
            throw new BusinessException("无效的实体类型: " + entityType);
        }
        
        // 构建Redis键
        String redisKey = buildEntityRedisKey(hospitalId, entityType);
        Object obj = redisUtils.get(redisKey);
        
        if (obj == null) {
            return ResponseResult.success(new ArrayList<>());
        }
        
        // 解析列表数据
        List<?> entityList = com.alibaba.fastjson2.JSON.parseArray(obj.toString(), getEntityClass(entityType));
        
        // 查找特定ID的实体
        List<Object> resultList = new ArrayList<>();
        for (Object entity : entityList) {
            String id = getEntityId(entity, entityType);
            if (ids.contains(id)) {
                resultList.add(entity);
            }
        }
        
        return ResponseResult.success(resultList);
    }
    
    /**
     * 条件查询Redis数据
     *
     * @param hospitalId 医院ID
     * @param entityType 实体类型
     * @return Redis数据列表
     */
    @GetMapping("/data/{hospitalId}/{entityType}")
    @ApiOperation(value = "条件查询实体数据", notes = "根据条件查询指定医院指定实体类型的Redis数据。entityType可选值：department, user, patient")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "hospitalId", value = "医院ID", required = true, paramType = "path"),
            @ApiImplicitParam(name = "entityType", value = "实体类型", required = true, paramType = "path", 
                    allowableValues = "department,user,patient"),
            @ApiImplicitParam(name = "keyword", value = "关键字", required = false, paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "返回数量限制", required = false, paramType = "query", defaultValue = "20")
    })
    public ResponseResult<List<?>> getEntityDataByCondition(@PathVariable String hospitalId, @PathVariable String entityType,
                                                         @RequestParam(required = false) String keyword, 
                                                         @RequestParam(defaultValue = "20") int limit) {
        log.info("接收到条件查询实体数据请求，医院ID: {}，实体类型: {}，关键字: {}, 限制: {}", hospitalId, entityType, keyword, limit);
        
        // 验证实体类型是否有效
        if (!isValidEntityType(entityType)) {
            log.error("无效的实体类型: {}", entityType);
            throw new BusinessException("无效的实体类型: " + entityType);
        }
        
        // 构建Redis键
        String redisKey = buildEntityRedisKey(hospitalId, entityType);
        Object obj = redisUtils.get(redisKey);
        
        if (obj == null) {
            return ResponseResult.success(new ArrayList<>());
        }
        
        // 解析列表数据
        List<?> entityList = com.alibaba.fastjson2.JSON.parseArray(obj.toString(), getEntityClass(entityType));
        
        // 如果没有关键字，直接返回列表（限制数量）
        if (keyword == null || keyword.isEmpty()) {
            int resultSize = Math.min(limit, entityList.size());
            return ResponseResult.success(entityList.subList(0, resultSize));
        }
        
        // 根据关键字过滤
        List<Object> resultList = new ArrayList<>();
        for (Object entity : entityList) {
            if (entityMatchesKeyword(entity, keyword, entityType)) {
                resultList.add(entity);
                
                // 达到限制数量时停止
                if (resultList.size() >= limit) {
                    break;
                }
            }
        }
        
        return ResponseResult.success(resultList);
    }
    
    /**
     * 验证实体类型是否有效
     *
     * @param entityType 实体类型
     * @return 是否有效
     */
    private boolean isValidEntityType(String entityType) {
        return SyncConstants.TableName.DEPARTMENT.equals(entityType) || 
               SyncConstants.TableName.USER.equals(entityType) || 
               SyncConstants.TableName.PATIENT.equals(entityType);
    }
    
    /**
     * 构建实体Redis键
     *
     * @param hospitalId 医院ID
     * @param entityType 实体类型
     * @return Redis键
     */
    private String buildEntityRedisKey(String hospitalId, String entityType) {
        switch (entityType) {
            case SyncConstants.TableName.DEPARTMENT:
                return SyncConstants.RedisKeyPrefix.HOSPITAL_DEPARTMENT + hospitalId;
            case SyncConstants.TableName.USER:
                return SyncConstants.RedisKeyPrefix.HOSPITAL_USER + hospitalId;
            case SyncConstants.TableName.PATIENT:
                return SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT + hospitalId;
            default:
                throw new BusinessException("无效的实体类型: " + entityType);
        }
    }
    
    /**
     * 获取实体类
     *
     * @param entityType 实体类型
     * @return 实体类
     */
    private Class<?> getEntityClass(String entityType) {
        switch (entityType) {
            case SyncConstants.TableName.DEPARTMENT:
                return com.sysgetway.core.entity.Department.class;
            case SyncConstants.TableName.USER:
                return com.sysgetway.core.entity.User.class;
            case SyncConstants.TableName.PATIENT:
                return com.sysgetway.core.entity.Patient.class;
            default:
                throw new BusinessException("无效的实体类型: " + entityType);
        }
    }
    
    /**
     * 获取实体ID
     *
     * @param entity 实体
     * @param entityType 实体类型
     * @return 实体ID
     */
    private String getEntityId(Object entity, String entityType) {
        switch (entityType) {
            case SyncConstants.TableName.DEPARTMENT:
                return String.valueOf(((com.sysgetway.core.entity.Department) entity).getDepartmentId());
            case SyncConstants.TableName.USER:
                return ((com.sysgetway.core.entity.User) entity).getUserName();
            case SyncConstants.TableName.PATIENT:
                return ((com.sysgetway.core.entity.Patient) entity).getInpatientInfoId();
            default:
                throw new BusinessException("无效的实体类型: " + entityType);
        }
    }
    
    /**
     * 实体是否匹配关键字
     *
     * @param entity 实体
     * @param keyword 关键字
     * @param entityType 实体类型
     * @return 是否匹配
     */
    private boolean entityMatchesKeyword(Object entity, String keyword, String entityType) {
        if (keyword == null || keyword.isEmpty()) {
            return true;
        }
        
        switch (entityType) {
            case SyncConstants.TableName.DEPARTMENT:
                com.sysgetway.core.entity.Department dept = (com.sysgetway.core.entity.Department) entity;
                return (dept.getName() != null && dept.getName().contains(keyword)) || 
                       (dept.getCode() != null && dept.getCode().contains(keyword));
                
            case SyncConstants.TableName.USER:
                com.sysgetway.core.entity.User user = (com.sysgetway.core.entity.User) entity;
                return (user.getName() != null && user.getName().contains(keyword)) || 
                       (user.getUserName() != null && user.getUserName().contains(keyword)) ||
                       (user.getMobile() != null && user.getMobile().contains(keyword));
                
            case SyncConstants.TableName.PATIENT:
                com.sysgetway.core.entity.Patient patient = (com.sysgetway.core.entity.Patient) entity;
                return (patient.getName() != null && patient.getName().contains(keyword)) || 
                       (patient.getInpatientInfoId() != null && patient.getInpatientInfoId().contains(keyword)) ||
                       (patient.getHospitalizationNo() != null && patient.getHospitalizationNo().contains(keyword)) ||
                       (patient.getMobile() != null && patient.getMobile().contains(keyword));
                
            default:
                throw new BusinessException("无效的实体类型: " + entityType);
        }
    }
    
    /**
     * 保存最后同步时间到Redis
     *
     * @param hospitalId 医院ID
     * @param tableName 表名
     * @param time 同步时间
     */
    private void saveLastSyncTimeToRedis(String hospitalId, String tableName, LocalDateTime time) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_LAST_TIME, hospitalId, tableName);
        long expireTime = SyncConstants.ExpireTime.LAST_SYNC_TIME;
        String timeStr = time.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        
        // 处理过期时间为0的情况（永不过期）
        if (expireTime <= 0) {
            redisUtils.set(key, timeStr);
            log.debug("保存最后同步时间到Redis（永不过期）: {}", key);
        } else {
            redisUtils.set(key, timeStr, expireTime);
            log.debug("保存最后同步时间到Redis（过期时间: {}秒）: {}", expireTime, key);
        }
    }
} 