package com.sysgetway.core.model.dto;

import com.sysgetway.core.entity.Patient;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 同步结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "数据同步结果信息")
public class SyncResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 同步状态，1：成功，0：进行中，-1：失败
     */
    @ApiModelProperty(value = "同步状态", notes = "1-成功，0-进行中，-1-失败", allowableValues = "1,0,-1", example = "1")
    private Integer status;

    /**
     * 状态描述
     */
    @ApiModelProperty(value = "状态描述", example = "同步成功")
    private String message;

    /**
     * 同步数据量
     */
    @ApiModelProperty(value = "同步数据量", example = "100")
    private Integer count;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "同步开始时间", example = "2023-01-01T10:00:00")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "同步结束时间", example = "2023-01-01T10:05:00")
    private LocalDateTime endTime;

    /**
     * 同步耗时(毫秒)
     */
    @ApiModelProperty(value = "同步耗时（毫秒）", example = "300000")
    private Long costTime;

    /**
     * 异常信息(如果同步失败)
     */
    @ApiModelProperty(value = "异常信息（如果同步失败）", example = "连接超时")
    private String errorMessage;

    /**
     * 失败重试次数
     */
    @ApiModelProperty(value = "失败重试次数", example = "3")
    private Integer retryCount;

    /**
     * 最后同步时间(用于增量同步)
     */
    @ApiModelProperty(value = "最后同步时间（用于增量同步）", example = "2023-01-01T10:00:00")
    private LocalDateTime lastSyncTime;

    /**
     * 医院ID
     */
    @ApiModelProperty(value = "医院ID", example = "hospital001")
    private String hospitalId;

    /**
     * 表名或患者类型
     */
    @ApiModelProperty(value = "表名或患者类型", notes = "如：patient、department、user、patient:in、patient:up、patient:out", example = "patient")
    private String tableNameOrPatientType;

    /**
     * 同步失败的患者数据列表
     */
    @ApiModelProperty(value = "同步失败的患者数据列表")
    private List<Patient> failedPatients;

    /**
     * 同步失败的原因映射(key: 患者唯一标识 value: 失败原因)
     */
    @ApiModelProperty(value = "同步失败的原因映射", notes = "key: 患者唯一标识，value: 失败原因")
    private Map<String, String> failedReasons;

    /**
     * 任务ID，用于异步任务跟踪
     */
    @ApiModelProperty(value = "任务ID，用于异步任务跟踪", example = "task_123456")
    private String taskId;

    /**
     * 更新的记录信息列表，包含更新的患者ID、姓名和更新类型等信息
     */
    @ApiModelProperty(value = "更新的记录信息列表", notes = "包含更新的患者ID、姓名和更新类型等信息")
    private List<Map<String, Object>> updatedRecords;

    // ========== 详细统计信息 ==========

    /**
     * 总处理数据量（原始输入数据量）
     */
    @ApiModelProperty(value = "总处理数据量（原始输入数据量）", example = "1000")
    private Integer totalCount;

    /**
     * 成功处理的数据量
     */
    @ApiModelProperty(value = "成功处理的数据量", example = "950")
    private Integer successCount;

    /**
     * 更新的数据量（对于增量同步，表示实际更新的数据）
     */
    @ApiModelProperty(value = "更新的数据量", notes = "对于增量同步，表示实际更新的数据", example = "50")
    private Integer updatedCount;

    /**
     * 新增的数据量（对于增量同步，表示新增的数据）
     */
    @ApiModelProperty(value = "新增的数据量", notes = "对于增量同步，表示新增的数据", example = "900")
    private Integer insertedCount;

    /**
     * 跳过的数据量（无效或重复数据）
     */
    @ApiModelProperty(value = "跳过的数据量", notes = "无效或重复数据", example = "30")
    private Integer skippedCount;

    /**
     * 失败的数据量
     */
    @ApiModelProperty(value = "失败的数据量", example = "20")
    private Integer failedCount;

    /**
     * 失败原因统计（key: 失败原因, value: 失败数量）
     */
    @ApiModelProperty(value = "失败原因统计", notes = "key: 失败原因，value: 失败数量")
    private Map<String, Integer> failureReasons;

    /**
     * 分类统计信息（用于患者数据auto分类）
     * key: 分类名称（如in, up, out）
     * value: 该分类的数据量
     */
    @ApiModelProperty(value = "分类统计信息", notes = "用于患者数据自动分类，key: 分类名称（如in, up, out），value: 该分类的数据量")
    private Map<String, Integer> classificationStats;

    /**
     * 性能统计信息
     */
    @ApiModelProperty(value = "性能统计信息", notes = "包含同步过程中的性能指标")
    private Map<String, Object> performanceStats;
}