package com.sysgetway.core.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 示例VO类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "示例数据VO")
public class DemoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty(value = "示例ID")
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    @Size(max = 50, message = "名称长度不能超过50个字符")
    @ApiModelProperty(value = "示例名称", required = true)
    private String name;

    /**
     * 描述
     */
    @Size(max = 200, message = "描述长度不能超过200个字符")
    @ApiModelProperty(value = "示例描述")
    private String description;
} 