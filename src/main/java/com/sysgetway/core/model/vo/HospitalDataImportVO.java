package com.sysgetway.core.model.vo;

import com.sysgetway.core.entity.Department;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.entity.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 医院数据导入VO
 * 包含科室、医护人员和患者数据
 */
@Data
@ApiModel(description = "医院综合数据导入对象")
public class HospitalDataImportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 科室数据列表
     */
    @ApiModelProperty(value = "科室数据列表", notes = "包含科室的基本信息，如科室ID、编码、名称等")
    private List<Department> departments;

    /**
     * 医护人员数据列表
     */
    @ApiModelProperty(value = "医护人员数据列表", notes = "包含医生和护士的基本信息，如姓名、工号、科室等")
    private List<User> users;

    /**
     * 患者数据列表
     */
    @ApiModelProperty(value = "患者数据列表", notes = "包含患者的基本信息、住院信息、诊断信息等，系统会自动进行患者分类")
    private List<Patient> patients;
}