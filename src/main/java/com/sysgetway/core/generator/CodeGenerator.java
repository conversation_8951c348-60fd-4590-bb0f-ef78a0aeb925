package com.sysgetway.core.generator;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.Collections;
import java.util.Scanner;

/**
 * MyBatis-Plus代码生成器
 */
public class CodeGenerator {

    /**
     * 数据库URL
     */
    private static final String DB_URL = "******************************************************************************************";

    /**
     * 数据库用户名
     */
    private static final String DB_USERNAME = "root";

    /**
     * 数据库密码
     */
    private static final String DB_PASSWORD = "g5C4cQ!Z4tR4";

    /**
     * 作者
     */
    private static final String AUTHOR = "SysGetway";

    /**
     * 父包名
     */
    private static final String PARENT_PACKAGE = "com.sysgetway.core";

    /**
     * XML文件输出路径
     */
    private static final String XML_OUTPUT_DIR = "src/main/resources/mapper";

    /**
     * 项目根路径
     */
    private static final String PROJECT_PATH = System.getProperty("user.dir");

    /**
     * 启动入口
     */
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        System.out.println("请输入表名，多个表名用英文逗号分隔：");
        String tables = scanner.nextLine();

        System.out.println("请输入模块名（用于生成包路径）：");
        String moduleName = scanner.nextLine();

        generate(tables, moduleName);

        scanner.close();
    }

    /**
     * 生成代码
     * @param tables 表名，多个表名用英文逗号分隔
     * @param moduleName 模块名
     */
    public static void generate(String tables, String moduleName) {
        FastAutoGenerator.create(DB_URL, DB_USERNAME, DB_PASSWORD)
            // 全局配置
            .globalConfig(builder -> {
                builder.author(AUTHOR) // 设置作者
                    .dateType(DateType.TIME_PACK) // 使用Java 8日期类型
                    .commentDate("yyyy-MM-dd") // 注释日期格式
                    .disableOpenDir() // 禁止打开输出目录
                    .enableSwagger() // 开启Swagger
                    .fileOverride() // 覆盖已有文件
                    .outputDir(PROJECT_PATH + "/src/main/java"); // 指定输出目录
            })
            // 包配置
            .packageConfig(builder -> {
                builder.parent(PARENT_PACKAGE) // 设置父包名
                    .moduleName(moduleName) // 设置模块名
                    .entity("entity") // 实体类包名
                    .service("service") // 服务类包名
                    .serviceImpl("service.impl") // 服务实现类包名
                    .mapper("mapper") // Mapper类包名
                    .controller("controller") // 控制器包名
                    .pathInfo(Collections.singletonMap(OutputFile.xml, PROJECT_PATH + "/" + XML_OUTPUT_DIR + "/" + moduleName)); // 设置mapperXml生成路径
            })
            // 策略配置
            .strategyConfig(builder -> {
                builder.addInclude(tables.split(",")) // 设置需要生成的表名
                    // 实体类策略
                    .entityBuilder()
                        .enableLombok() // 启用Lombok
                        .enableChainModel() // 启用链式模型
                        .enableRemoveIsPrefix() // 启用移除is前缀
                        .enableTableFieldAnnotation() // 启用表字段注解
                        .logicDeleteColumnName("deleted") // 逻辑删除字段名
                        .naming(NamingStrategy.underline_to_camel) // 表名命名策略
                        .columnNaming(NamingStrategy.underline_to_camel) // 列名命名策略
                        .addSuperEntityColumns("id", "created_at", "updated_at", "deleted") // 公共字段
                        .formatFileName("%s") // 格式化文件名
                    // 控制器策略
                    .controllerBuilder()
                        .enableRestStyle() // 启用REST风格
                        .enableHyphenStyle() // 启用连字符风格
                        .formatFileName("%sController") // 格式化文件名
                    // 服务策略
                    .serviceBuilder()
                        .formatServiceFileName("I%sService") // 格式化服务接口文件名
                        .formatServiceImplFileName("%sServiceImpl") // 格式化服务实现类文件名
                    // Mapper策略
                    .mapperBuilder()
                        .enableMapperAnnotation() // 启用Mapper注解
                        .formatMapperFileName("%sMapper") // 格式化Mapper文件名
                        .formatXmlFileName("%sMapper"); // 格式化XML文件名
            })
            // 模板引擎配置
            .templateEngine(new FreemarkerTemplateEngine())
            // 执行
            .execute();

        System.out.println("代码生成完成！");
    }
}
