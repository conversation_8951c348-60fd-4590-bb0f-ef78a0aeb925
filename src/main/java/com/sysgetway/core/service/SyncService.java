package com.sysgetway.core.service;

import com.sysgetway.core.common.util.ResponseResult;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.model.dto.SyncResultDTO;

import java.util.List;

/**
 * 同步服务接口
 */
public interface SyncService {

    /**
     * 全量同步
     *
     * @param hospitalId 医院ID
     * @param tableName 表名(department, user, patient)
     * @return 同步结果，包含同步状态、同步数据量、耗时等信息
     */
    ResponseResult<SyncResultDTO> syncFull(String hospitalId, String tableName);

    /**
     * 全量同步（异步，带数据）
     * 立即返回任务ID，不阻塞主线程，任务在后台异步执行
     * 会清除原有数据，然后保存新数据到Redis中
     *
     * @param hospitalId 医院ID
     * @param tableName 表名(department, user, patient)
     * @param dataList 数据列表（可选，如果为null则从外部系统获取）
     * @return 同步结果，包含任务ID和初始状态
     */
    ResponseResult<SyncResultDTO> syncFullAsync(String hospitalId, String tableName, List<Object> dataList);
    
    /**
     * 增量同步
     *
     * @param hospitalId 医院ID
     * @param tableName 表名(department, user, patient)
     * @return 同步结果，包含同步状态、同步数据量、耗时等信息
     */
    ResponseResult<SyncResultDTO> syncIncremental(String hospitalId, String tableName);
    
    /**
     * 患者分类同步
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型(in, up, out)
     * @return 同步结果，包含同步状态、同步数据量、耗时等信息
     */
    ResponseResult<SyncResultDTO> syncPatientByType(String hospitalId, String patientType);
    
    /**
     * 患者分类同步（带数据）
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型(in, up, out)
     * @param patients 患者数据列表（符合数据视图结构说明）
     * @return 同步结果，包含同步状态、同步数据量、耗时等信息
     */
    ResponseResult<SyncResultDTO> syncPatientByType(String hospitalId, String patientType, List<Patient> patients);
    
    /**
     * 患者分类异步同步（带数据）
     * 立即返回任务ID，不阻塞主线程，任务在后台异步执行
     *
     * @param hospitalId 医院ID
     * @param patientType 患者类型(in, up, out)
     * @param patients 患者数据列表（符合数据视图结构说明）
     * @return 同步结果，包含任务ID和初始状态
     */
    ResponseResult<SyncResultDTO> syncPatientByTypeAsync(String hospitalId, String patientType, List<Patient> patients);
    
    /**
     * 获取同步状态
     *
     * @param hospitalId 医院ID
     * @param tableName 表名
     * @return 同步状态信息
     */
    ResponseResult<SyncResultDTO> getSyncStatus(String hospitalId, String tableName);
    
    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息，包含同步状态、更新的数据记录等
     */
    ResponseResult<SyncResultDTO> getTaskStatus(String taskId);
    
    /**
     * 获取最后同步时间
     *
     * @param hospitalId 医院ID
     * @param tableName 表名
     * @return 最后同步时间
     */
    ResponseResult<String> getLastSyncTime(String hospitalId, String tableName);
    
    /**
     * 清理任务状态
     *
     * @param hospitalId 医院ID
     * @param tableNameOrType 表名或患者类型
     * @param syncType 同步类型
     * @return 清理结果
     */
    ResponseResult<String> clearTaskStatus(String hospitalId, String tableNameOrType, String syncType);
    
    /**
     * 清理所有任务状态
     *
     * @return 清理结果
     */
    ResponseResult<String> clearAllTaskStatus();
} 