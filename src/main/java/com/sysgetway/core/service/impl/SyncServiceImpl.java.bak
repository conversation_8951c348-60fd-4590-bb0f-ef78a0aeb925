package com.sysgetway.core.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter.Feature;
import com.sysgetway.core.common.constant.ResultCode;
import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.common.exception.BusinessException;
import com.sysgetway.core.common.util.RedisUtils;
import com.sysgetway.core.common.util.ResponseResult;
import com.sysgetway.core.entity.Department;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.entity.User;
import com.sysgetway.core.model.dto.SyncResultDTO;
import com.sysgetway.core.service.SyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.atomic.AtomicInteger;
import java.util.HashMap;
import java.util.UUID;
import org.springframework.beans.BeanUtils;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Objects;
import java.util.Collections;

/**
 * 同步服务实现类
 */
@Service
@Slf4j
public class SyncServiceImpl implements SyncService {

    @Resource
    private RedisUtils redisUtils;
    
    // 用于记录正在进行的同步任务
    private static final Map<String, CompletableFuture<SyncResultDTO>> RUNNING_TASKS = new ConcurrentHashMap<>();
    
    // 用于记录同步任务的重试次数
    private static final Map<String, AtomicInteger> RETRY_COUNT = new ConcurrentHashMap<>();
    
    // 日期时间格式化器
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    
    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 3;
    
    @Override
    public ResponseResult<SyncResultDTO> syncFull(String hospitalId, String tableName) {
        log.info("开始全量同步医院[{}]的[{}]数据", hospitalId, tableName);
        
        // 验证表名是否有效
        if (!SyncConstants.TableName.isValid(tableName)) {
            log.error("无效的表名: {}", tableName);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的表名: " + tableName);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, tableName, SyncConstants.SyncType.FULL);
        
        // 检查是否有正在运行的任务
        if (RUNNING_TASKS.containsKey(taskKey)) {
            log.info("医院[{}]的[{}]全量同步任务正在进行中", hospitalId, tableName);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, tableName);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType(tableName)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return ResponseResult.success(result);
        }
        
        // 异步执行同步任务
        CompletableFuture<SyncResultDTO> future = syncFullAsync(hospitalId, tableName);
        RUNNING_TASKS.put(taskKey, future);
        
        // 返回初始状态
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .build();
                
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, initialResult);
                
        return ResponseResult.success(initialResult);
    }
    
    /**
     * 异步执行全量同步
     */
    @Async
    public CompletableFuture<SyncResultDTO> syncFullAsync(String hospitalId, String tableName) {
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .build();
        
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, result);
        
        String taskKey = buildTaskKey(hospitalId, tableName, SyncConstants.SyncType.FULL);
        
        try {
            // 模拟获取数据的耗时操作
            List<?> dataList = mockFetchData(tableName);
            
            if (dataList == null || dataList.isEmpty()) {
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.NO_DATA);
                result.setCount(0);
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            } else {
                // 将数据存入Redis
                saveDataToRedis(hospitalId, tableName, dataList);
                
                // 更新结果
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.SUCCESS);
                result.setCount(dataList.size());
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                
                // 更新最后同步时间
                saveLastSyncTimeToRedis(hospitalId, tableName, result.getEndTime());
            }
            
            log.info("医院[{}]的[{}]数据全量同步完成，数据量: {}", hospitalId, tableName, result.getCount());
        } catch (Exception e) {
            log.error("医院[{}]的[{}]数据全量同步失败", hospitalId, tableName, e);
            
            // 尝试重试
            AtomicInteger retryCount = RETRY_COUNT.computeIfAbsent(taskKey, k -> new AtomicInteger(0));
            int currentRetryCount = retryCount.incrementAndGet();
            
            if (currentRetryCount <= MAX_RETRY_COUNT) {
                log.info("医院[{}]的[{}]数据全量同步失败，第{}次重试", hospitalId, tableName, currentRetryCount);
                // 递归重试
                return syncFullAsync(hospitalId, tableName);
            }
            
            // 重试次数用完，记录失败
            result.setStatus(SyncConstants.SyncStatus.FAILED);
            result.setMessage(SyncConstants.SyncMessage.FAILED);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
            result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            result.setRetryCount(currentRetryCount);
            
            // 清除重试记录
            RETRY_COUNT.remove(taskKey);
        } finally {
            // 更新同步状态到Redis
            saveSyncStatusToRedis(hospitalId, tableName, result);
            
            // 移除运行中的任务
            RUNNING_TASKS.remove(taskKey);
        }
        
        return CompletableFuture.completedFuture(result);
    }
    
    @Override
    public ResponseResult<SyncResultDTO> syncIncremental(String hospitalId, String tableName) {
        log.info("开始增量同步医院[{}]的[{}]数据", hospitalId, tableName);
        
        // 验证表名是否有效
        if (!SyncConstants.TableName.isValid(tableName)) {
            log.error("无效的表名: {}", tableName);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的表名: " + tableName);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, tableName, SyncConstants.SyncType.INCREMENTAL);
        
        // 检查是否有正在运行的任务
        if (RUNNING_TASKS.containsKey(taskKey)) {
            log.info("医院[{}]的[{}]增量同步任务正在进行中", hospitalId, tableName);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, tableName);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType(tableName)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return ResponseResult.success(result);
        }
        
        // 获取最后同步时间
        LocalDateTime lastSyncTime = getLastSyncTimeFromRedis(hospitalId, tableName);
        if (lastSyncTime == null) {
            // 如果没有最后同步时间，执行全量同步
            log.info("医院[{}]的[{}]数据没有最后同步时间，将执行全量同步", hospitalId, tableName);
            return syncFull(hospitalId, tableName);
        }
        
        // 异步执行增量同步任务
        CompletableFuture<SyncResultDTO> future = syncIncrementalAsync(hospitalId, tableName, lastSyncTime);
        RUNNING_TASKS.put(taskKey, future);
        
        // 返回初始状态
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .lastSyncTime(lastSyncTime)
                .build();
                
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, initialResult);
                
        return ResponseResult.success(initialResult);
    }
    
    /**
     * 异步执行增量同步
     */
    @Async
    public CompletableFuture<SyncResultDTO> syncIncrementalAsync(String hospitalId, String tableName, LocalDateTime lastSyncTime) {
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .lastSyncTime(lastSyncTime)
                .build();
        
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, tableName, result);
        
        String taskKey = buildTaskKey(hospitalId, tableName, SyncConstants.SyncType.INCREMENTAL);
        
        try {
            // 模拟获取增量数据
            List<?> dataList = mockFetchIncrementalData(tableName, lastSyncTime);
            
            if (dataList == null || dataList.isEmpty()) {
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.NO_DATA);
                result.setCount(0);
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            } else {
                // 将数据存入Redis
                saveDataToRedis(hospitalId, tableName, dataList);
                
                // 更新结果
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.SUCCESS);
                result.setCount(dataList.size());
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                
                // 更新最后同步时间
                saveLastSyncTimeToRedis(hospitalId, tableName, result.getEndTime());
            }
            
            log.info("医院[{}]的[{}]数据增量同步完成，数据量: {}", hospitalId, tableName, result.getCount());
        } catch (Exception e) {
            log.error("医院[{}]的[{}]数据增量同步失败", hospitalId, tableName, e);
            
            // 尝试重试
            AtomicInteger retryCount = RETRY_COUNT.computeIfAbsent(taskKey, k -> new AtomicInteger(0));
            int currentRetryCount = retryCount.incrementAndGet();
            
            if (currentRetryCount <= MAX_RETRY_COUNT) {
                log.info("医院[{}]的[{}]数据增量同步失败，第{}次重试", hospitalId, tableName, currentRetryCount);
                // 递归重试
                return syncIncrementalAsync(hospitalId, tableName, lastSyncTime);
            }
            
            // 重试次数用完，记录失败
            result.setStatus(SyncConstants.SyncStatus.FAILED);
            result.setMessage(SyncConstants.SyncMessage.FAILED);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
            result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            result.setRetryCount(currentRetryCount);
            
            // 清除重试记录
            RETRY_COUNT.remove(taskKey);
        } finally {
            // 更新同步状态到Redis
            saveSyncStatusToRedis(hospitalId, tableName, result);
            
            // 移除运行中的任务
            RUNNING_TASKS.remove(taskKey);
        }
        
        return CompletableFuture.completedFuture(result);
    }
    
    @Override
    public ResponseResult<SyncResultDTO> syncPatientByType(String hospitalId, String patientType) {
        log.info("开始同步医院[{}]的[{}]类型患者数据", hospitalId, patientType);
        
        // 验证患者类型是否有效
        if (!SyncConstants.PatientType.isValid(patientType)) {
            log.error("无效的患者类型: {}", patientType);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的患者类型: " + patientType);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, patientType, "patient");
        
        // 检查是否有正在运行的任务
        if (RUNNING_TASKS.containsKey(taskKey)) {
            log.info("医院[{}]的[{}]类型患者同步任务正在进行中", hospitalId, patientType);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, "patient:" + patientType);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType("patient:" + patientType)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return ResponseResult.success(result);
        }
        
        // 异步执行患者分类同步任务
        CompletableFuture<SyncResultDTO> future = syncPatientByTypeAsync(hospitalId, patientType);
        RUNNING_TASKS.put(taskKey, future);
        
        // 返回初始状态
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:" + patientType)
                .startTime(LocalDateTime.now())
                .build();
                
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, initialResult);
                
        return ResponseResult.success(initialResult);
    }
    
    @Override
    public ResponseResult<SyncResultDTO> syncPatientByType(String hospitalId, String patientType, List<Patient> patients) {
        log.info("开始同步医院[{}]的[{}]类型患者数据，传入数据量: {}", hospitalId, patientType, 
                (patients != null ? patients.size() : 0));
        
        // 验证患者类型是否有效
        if (!SyncConstants.PatientType.isValid(patientType)) {
            log.error("无效的患者类型: {}", patientType);
            throw new BusinessException(ResultCode.PARAM_ERROR, "无效的患者类型: " + patientType);
        }
        
        // 构建任务键
        String taskKey = buildTaskKey(hospitalId, patientType, "patient");
        
        // 检查是否有正在运行的任务
        if (RUNNING_TASKS.containsKey(taskKey)) {
            log.info("医院[{}]的[{}]类型患者同步任务正在进行中", hospitalId, patientType);
            
            // 获取运行中任务的状态
            SyncResultDTO result = getSyncStatusFromRedis(hospitalId, "patient:" + patientType);
            if (result == null) {
                result = SyncResultDTO.builder()
                        .status(SyncConstants.SyncStatus.IN_PROGRESS)
                        .message(SyncConstants.SyncMessage.IN_PROGRESS)
                        .hospitalId(hospitalId)
                        .tableNameOrPatientType("patient:" + patientType)
                        .startTime(LocalDateTime.now())
                        .build();
            }
            
            return ResponseResult.success(result);
        }
        
        // 添加样本患者数据日志输出
        if (patients != null && !patients.isEmpty()) {
            int sampleSize = Math.min(5, patients.size());
            log.info("样本患者数据（前{}条）:", sampleSize);
            for (int i = 0; i < sampleSize; i++) {
                Patient patient = patients.get(i);
                log.info("患者[{}] - ID: {}, 姓名: {}, 状态: {}, 入院时间: {}, 出院时间: {}",
                        i + 1, 
                        patient.getInpatientInfoId(),
                        patient.getName(),
                        patient.getStatus(),
                        patient.getInhospitalTime(),
                        patient.getOuthospitalTime());
            }
        }
        
        // 异步执行患者分类同步任务，传入患者数据
        CompletableFuture<SyncResultDTO> future = syncPatientByTypeAsync(hospitalId, patientType, patients);
        RUNNING_TASKS.put(taskKey, future);
        
        // 返回初始状态
        SyncResultDTO initialResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:" + patientType)
                .startTime(LocalDateTime.now())
                .build();
                
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, initialResult);
                
        return ResponseResult.success(initialResult);
    }
    
    /**
     * 异步执行患者分类同步
     */
    @Async
    public CompletableFuture<SyncResultDTO> syncPatientByTypeAsync(String hospitalId, String patientType) {
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:" + patientType)
                .startTime(LocalDateTime.now())
                .build();
        
        // 保存同步状态到Redis
        saveSyncStatusToRedis(hospitalId, "patient:" + patientType, result);
        
        String taskKey = buildTaskKey(hospitalId, patientType, "patient");
        
        // 定义变量，用于记录不符合条件的患者数据和失败原因
        List<Patient> invalidPatients = new ArrayList<>();
        Map<String, Integer> failureReasons = new HashMap<>();
        int validPatientCount = 0;
        int skippedPatientCount = 0;
        
        try {
            // 模拟获取指定类型的患者数据
            List<Patient> patients = mockFetchPatientByType(patientType);
            
            log.info("开始处理患者数据，医院ID: {}, 患者类型: {}, 传入患者数量: {}", 
                    hospitalId, patientType, (patients != null ? patients.size() : 0));
            
            // 输出传入的患者数据详情（仅在TRACE级别输出）
            if (log.isTraceEnabled() && patients != null && !patients.isEmpty()) {
                for (int i = 0; i < Math.min(5, patients.size()); i++) {
                    Patient patient = patients.get(i);
                    log.trace("患者[{}]数据示例: inpatientInfoId={}, name={}, status={}", 
                            i, patient.getInpatientInfoId(), patient.getName(), patient.getStatus());
                }
                if (patients.size() > 5) {
                    log.trace("... 及其他 {} 条患者记录", patients.size() - 5);
                }
            }
            
            // 使用传入的患者数据，而不是模拟数据
            List<Patient> filteredPatients = new ArrayList<>();
            
            // 记录过滤开始时间
            long filterStartTime = System.currentTimeMillis();
            int totalPatients = patients != null ? patients.size() : 0;
            
            // 添加数据去重步骤
            Map<String, Patient> uniquePatientsMap = new HashMap<>();
            int duplicateCount = 0;
            
            if (patients != null && !patients.isEmpty()) {
                log.debug("开始患者数据去重处理");
                
                for (Patient patient : patients) {
                    // 首先检查患者是否具有必要的字段
                    if (patient.getHospitalizationNo() == null || patient.getInhospitalTime() == null) {
                        // 记录缺少必要字段的患者
                        invalidPatients.add(patient);
                        
                        // 统计失败原因
                        String reason;
                        if (patient.getHospitalizationNo() == null && patient.getInhospitalTime() == null) {
                            reason = "缺少住院号和入院时间";
                        } else if (patient.getHospitalizationNo() == null) {
                            reason = "缺少住院号";
                        } else {
                            reason = "缺少入院时间";
                        }
                        
                        failureReasons.put(reason, failureReasons.getOrDefault(reason, 0) + 1);
                        log.warn("患者数据缺少必要字段: {}, 患者ID: {}, 患者姓名: {}", 
                                reason, patient.getInpatientInfoId(), patient.getName());
                        continue;
                    }
                    
                    // 生成患者的业务唯一键
                    String businessKey = generatePatientBusinessKey(patient);
                    
                    // 如果无法生成业务唯一键，跳过该患者
                    if (businessKey == null) {
                        invalidPatients.add(patient);
                        String reason = "无法生成业务唯一键";
                        failureReasons.put(reason, failureReasons.getOrDefault(reason, 0) + 1);
                        log.warn("无法为患者生成业务唯一键: 患者ID: {}, 患者姓名: {}", 
                                patient.getInpatientInfoId(), patient.getName());
                        continue;
                    }
                    
                    // 如果已存在相同业务键的患者，则进行合并或保留最新的
                    if (uniquePatientsMap.containsKey(businessKey)) {
                        duplicateCount++;
                        
                        // 获取已存在的患者数据
                        Patient existingPatient = uniquePatientsMap.get(businessKey);
                        
                        // 如果新患者的更新时间更新，则替换旧患者
                        if (patient.getUpdatedAt() != null && existingPatient.getUpdatedAt() != null && 
                            patient.getUpdatedAt().isAfter(existingPatient.getUpdatedAt())) {
                            uniquePatientsMap.put(businessKey, patient);
                            log.trace("替换重复患者数据，业务键: {}, 新更新时间: {}, 旧更新时间: {}", 
                                     businessKey, patient.getUpdatedAt(), existingPatient.getUpdatedAt());
                        } else {
                            log.trace("保留现有患者数据，业务键: {}", businessKey);
                        }
                    } else {
                        // 添加新患者到去重Map
                        uniquePatientsMap.put(businessKey, patient);
                    }
                }
                
                log.info("患者数据去重完成，原始数据: {}, 去重后: {}, 重复数据: {}, 无效数据: {}", 
                        patients.size(), uniquePatientsMap.size(), duplicateCount, invalidPatients.size());
                
                // 将去重后的患者数据转换为列表，用于后续过滤
                List<Patient> uniquePatients = new ArrayList<>(uniquePatientsMap.values());
                
                // 根据患者类型过滤数据
                log.debug("开始按类型[{}]过滤患者数据", patientType);
                
                for (Patient patient : uniquePatients) {
                    // 根据患者类型进行过滤
                    boolean shouldInclude = false;
                    
                    switch (patientType) {
                        case SyncConstants.PatientType.IN:
                            // 修改入院患者过滤条件：只检查患者状态是否为在院(1)，不再检查入院时间
                            shouldInclude = (patient.getStatus() != null && patient.getStatus() == 1);
                            log.debug("患者[{}]入院条件判断: 状态={}, 是否符合={}", 
                                    patient.getInpatientInfoId(), patient.getStatus(), shouldInclude);
                            break;
                        case SyncConstants.PatientType.UP:
                            // 在院患者：状态为在院
                            shouldInclude = (patient.getStatus() != null && patient.getStatus() == 1);
                            break;
                        case SyncConstants.PatientType.OUT:
                            // 出院患者：状态为出院
                            shouldInclude = (patient.getStatus() != null && patient.getStatus() == 2);
                            break;
                        default:
                            log.warn("未知的患者类型: {}", patientType);
                    }
                    
                    if (shouldInclude) {
                        filteredPatients.add(patient);
                    } else {
                        // 记录不符合患者类型条件的患者
                        invalidPatients.add(patient);
                        String reason = "不符合患者类型[" + patientType + "]要求";
                        failureReasons.put(reason, failureReasons.getOrDefault(reason, 0) + 1);
                        log.debug("患者不符合类型要求: 类型={}, 患者ID={}, 患者状态={}", 
                                patientType, patient.getInpatientInfoId(), patient.getStatus());
                    }
                }
                
                log.debug("患者数据过滤完成，去重后数据: {}, 过滤后: {}, 无效数据: {}", 
                        uniquePatients.size(), filteredPatients.size(), invalidPatients.size());
            }
            
            // 记录过滤结束时间
            long filterEndTime = System.currentTimeMillis();
            
            // 添加详细的过滤结果日志
            log.info("患者数据过滤结果 - 医院ID: {}, 患者类型: {}, 原始数据量: {}, 过滤后数据量: {}, 无效数据量: {}, 过滤耗时: {}ms",
                    hospitalId, patientType, 
                    (patients != null ? patients.size() : 0), 
                    filteredPatients.size(),
                    invalidPatients.size(),
                    (filterEndTime - filterStartTime));
            
            // 记录失败原因统计
            for (Map.Entry<String, Integer> entry : failureReasons.entrySet()) {
                log.info("患者数据失败原因: {} - 数量: {}", entry.getKey(), entry.getValue());
            }
            
            // 处理过滤后的患者数据
            List<Patient> patientsToStore = filteredPatients;
            
            if (patientsToStore.isEmpty()) {
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(invalidPatients.isEmpty() ? SyncConstants.SyncMessage.NO_DATA : SyncConstants.SyncMessage.NO_VALID_DATA);
                result.setCount(0);
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                
                // 添加失败患者信息
                if (!invalidPatients.isEmpty()) {
                    result.setFailedPatients(invalidPatients);
                    result.setFailedCount(invalidPatients.size());
                    result.setFailureReasons(failureReasons);
                    
                    // 如果有失败数据，但是成功数据为0，则设置状态为失败
                    if (validPatientCount == 0) {
                        result.setStatus(SyncConstants.SyncStatus.FAILED);
                        result.setMessage(SyncConstants.SyncMessage.NO_VALID_DATA);
                        result.setErrorMessage("没有符合条件的有效数据，所有数据均不符合住院号+入院时间的要求");
                    }
                }
                
                // 更新最后同步时间
                saveLastSyncTimeToRedis(hospitalId, "patient:" + patientType, result.getEndTime());
            } else {
                // 记录合并开始时间
                long mergeStartTime = System.currentTimeMillis();
                
                // 将数据存入Redis (使用Hash结构)
                String redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT_TYPE + hospitalId + ":" + patientType;
                log.info("准备将{}个患者数据存入Redis，键: {}", patientsToStore.size(), redisKey);
                
                // 创建一个Map来存储患者数据
                Map<String, Object> patientMap = new HashMap<>();
                
                for (Patient patient : patientsToStore) {
                    processedPatients++;
                    
                    // 使用住院号+入院时间作为唯一标识
                    String fieldName = null;
                    try {
                        // 此处所有患者都应该具有住院号和入院时间（已在前面过滤过）
                        if (patient.getHospitalizationNo() != null && patient.getInhospitalTime() != null) {
                            String formattedTime = patient.getInhospitalTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                            fieldName = "hosptime_" + patient.getHospitalizationNo() + "_" + formattedTime;
                            log.trace("使用hospitalizationNo+inhospitalTime作为字段名: {}", fieldName);
                        } else {
                            // 理论上不应该进入这个分支，因为前面已经过滤过
                            log.error("严重错误：遇到缺少必要字段的患者数据，住院号为空: {}, 入院时间为空: {}", 
                                   patient.getHospitalizationNo() == null, patient.getInhospitalTime() == null);
                            continue;
                        }
                    } catch (Exception e) {
                        // 捕获任何可能的异常，确保字段名生成不会失败
                        log.error("生成字段名时发生异常: {}", e.getMessage(), e);
                        continue;
                    }
                    
                    if (fieldName == null) {
                        log.error("无法为患者生成字段名，跳过该患者: ID={}, 姓名={}", 
                               patient.getInpatientInfoId(), patient.getName());
                        continue;
                    }
                    
                    try {
                        // 检查Redis中是否已存在该患者数据，如果存在则合并
                        Patient mergedPatient = patient;
                        if (redisUtils.hasKey(redisKey) && redisUtils.hExists(redisKey, fieldName)) {
                            Object existingData = redisUtils.hGet(redisKey, fieldName);
                            if (existingData != null) {
                                // 将现有数据转换为Patient对象
                                Patient existingPatient = JSON.parseObject(existingData.toString(), Patient.class);
                                // 合并数据，保留新数据中的非null字段，其他字段使用现有数据
                                Map<String, Object> mergeResult = mergePatientData(existingPatient, patient);
                                mergedPatient = (Patient) mergeResult.get("patient");
                                int updatedFieldCount = (int) mergeResult.get("updatedFieldCount");
                                
                                // 只在有字段实际更新时才输出合并日志
                                if (updatedFieldCount > 0) {
                                    log.debug("合并患者数据: ID={}, 更新字段数={}", 
                                            fieldName, updatedFieldCount);
                                } else {
                                    log.trace("患者数据无变化，跳过合并日志: {}", fieldName);
                                }
                            }
                        }
                        
                        // 将患者对象转换为JSON字符串作为值
                        // 使用FastJSON的Feature.WriteNulls确保null值也被序列化
                        String patientJson = JSON.toJSONString(mergedPatient, Feature.WriteNulls, 
                                                                  Feature.WriteNullStringAsEmpty);
                        
                        // 添加详细日志，显示JSON内容（仅在调试模式下）
                        if (log.isTraceEnabled()) {
                            log.trace("患者JSON数据内容: {}", patientJson);
                        }
                        
                        patientMap.put(fieldName, patientJson);
                        log.trace("添加患者到Hash: fieldName={}, jsonLength={}", fieldName, patientJson.length());
                        validPatientCount++;
                    } catch (Exception e) {
                        log.error("将患者数据转换为JSON时发生异常: {}", e.getMessage(), e);
                        skippedPatientCount++;
                    }
                }
                
                log.info("有效患者数据: {}, 跳过的患者数据: {}", validPatientCount, skippedPatientCount);
                
                // 记录合并结束时间
                long mergeEndTime = System.currentTimeMillis();
                
                // 记录Redis操作开始时间
                long redisStartTime = System.currentTimeMillis();
                
                if (!patientMap.isEmpty()) {
                    // 使用更安全的方式更新Redis数据
                    // 注意：redisKey已在之前定义，无需重复定义
                    log.info("开始将患者数据存入Redis Hash结构: {}", redisKey);
                    
                    try {
                        // 确定更新模式：增量更新还是全量替换
                        boolean isFullReplace = false; // 默认使用增量更新模式
                        
                        // 如果是全量替换模式，先清理旧数据
                        if (isFullReplace && redisUtils.hasKey(redisKey)) {
                            log.info("全量替换模式：清理Redis中的旧数据，键: {}", redisKey);
                            redisUtils.delete(redisKey);
                            log.debug("已清理Redis中的旧数据，键: {}", redisKey);
                        }
                        
                        // 优化Redis操作：直接使用hSetAll进行增量更新，无需先获取所有现有数据
                        // 直接使用hSetAll方法批量添加/更新字段，避免删除再重写的操作
                        redisUtils.hSetAll(redisKey, patientMap);
                        log.debug("患者数据已批量更新到Redis Hash结构，共{}个字段", patientMap.size());
                        
                        // 设置过期时间
                        redisUtils.expire(redisKey, SyncConstants.ExpireTime.DEFAULT);
                        log.debug("已设置Redis键的过期时间: {}秒", SyncConstants.ExpireTime.DEFAULT);
                        
                        // 增强数据验证逻辑
                        boolean validationSuccess = validateRedisData(redisKey, patientMap);
                        if (validationSuccess) {
                            log.info("Redis数据验证成功，所有数据已正确写入");
                        } else {
                            log.warn("Redis数据验证失败，部分数据可能未正确写入");
                        }
                    } catch (Exception e) {
                        log.error("存储患者数据到Redis时发生异常: {}", e.getMessage(), e);
                    }
                } else {
                    log.warn("没有有效的患者数据可存储");
                }
                
                // 记录Redis操作结束时间
                long redisEndTime = System.currentTimeMillis();
                
                // 更新结果
                result.setStatus(SyncConstants.SyncStatus.SUCCESS);
                result.setMessage(SyncConstants.SyncMessage.SUCCESS);
                result.setCount(validPatientCount);
                result.setEndTime(LocalDateTime.now());
                result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                
                // 添加失败患者信息
                if (!invalidPatients.isEmpty()) {
                    result.setFailedPatients(invalidPatients);
                    result.setFailedCount(invalidPatients.size());
                    result.setFailureReasons(failureReasons);
                    
                    // 如果有失败数据，但是成功数据为0，则设置状态为失败
                    if (validPatientCount == 0) {
                        result.setStatus(SyncConstants.SyncStatus.FAILED);
                        result.setMessage(SyncConstants.SyncMessage.NO_VALID_DATA);
                        result.setErrorMessage("没有符合条件的有效数据，所有数据均不符合住院号+入院时间的要求");
                    }
                }
                
                // 更新最后同步时间
                saveLastSyncTimeToRedis(hospitalId, "patient:" + patientType, result.getEndTime());
            }
            
            // 输出性能统计信息
            long totalTime = System.currentTimeMillis() - startTime;
            long filterTime = filterEndTime - filterStartTime;
            long mergeTime = mergeEndTime - mergeStartTime;
            long redisTime = redisEndTime - redisStartTime;
            
            log.info("患者数据同步性能统计 - 医院ID: {}, 类型: {}, 总时间: {}ms, 过滤耗时: {}ms, 合并耗时: {}ms, Redis存储耗时: {}ms, 总患者数: {}, 处理患者数: {}", 
                    hospitalId, patientType, totalTime, filterTime, mergeTime, redisTime, totalPatients, processedPatients);
            
            log.info("医院[{}]的[{}]类型患者数据同步完成，数据量: {}", hospitalId, patientType, result.getCount());
        } catch (Exception e) {
            log.error("医院[{}]的[{}]类型患者数据同步失败: {}", hospitalId, patientType, e.getMessage(), e);
            
            // 尝试重试
            AtomicInteger retryCount = RETRY_COUNT.computeIfAbsent(taskKey, k -> new AtomicInteger(0));
            int currentRetryCount = retryCount.incrementAndGet();
            
            if (currentRetryCount <= MAX_RETRY_COUNT) {
                log.info("医院[{}]的[{}]类型患者数据同步失败，第{}次重试", hospitalId, patientType, currentRetryCount);
                // 递归重试
                return syncPatientByTypeAsync(hospitalId, patientType);
            }
            
            // 重试次数用完，记录失败
            result.setStatus(SyncConstants.SyncStatus.FAILED);
            result.setMessage(SyncConstants.SyncMessage.FAILED);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
            result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
            result.setRetryCount(currentRetryCount);
            
            // 清除重试记录
            RETRY_COUNT.remove(taskKey);
        } finally {
            // 添加失败患者信息
            if (invalidPatients != null && !invalidPatients.isEmpty()) {
                result.setFailedPatients(invalidPatients);
                result.setFailedCount(invalidPatients.size());
                result.setFailureReasons(failureReasons);
                
                // 如果有失败数据，但是成功数据为0，则设置状态为失败
                if (validPatientCount == 0) {
                    result.setStatus(SyncConstants.SyncStatus.FAILED);
                    result.setMessage(SyncConstants.SyncMessage.NO_VALID_DATA);
                    result.setErrorMessage("没有符合条件的有效数据，所有数据均不符合住院号+入院时间的要求");
                }
            }
            
            // 更新同步状态到Redis
            try {
                saveSyncStatusToRedis(hospitalId, "patient:" + patientType, result);
            } catch (Exception e) {
                log.error("保存同步状态到Redis失败: {}", e.getMessage(), e);
            }
            
            // 移除运行中的任务
            RUNNING_TASKS.remove(taskKey);
            
            // 确保重试计数器被清理
            RETRY_COUNT.remove(taskKey);
            
            log.info("任务[{}]状态已清理", taskKey);
        }
        
        return CompletableFuture.completedFuture(result);
    }
    
    @Override
    public ResponseResult<SyncResultDTO> getSyncStatus(String hospitalId, String tableName) {
        log.info("获取医院[{}]的[{}]数据同步状态", hospitalId, tableName);
        
        SyncResultDTO result = getSyncStatusFromRedis(hospitalId, tableName);
        if (result == null) {
            result = SyncResultDTO.builder()
                    .status(SyncConstants.SyncStatus.SUCCESS)
                    .message("没有同步记录")
                    .hospitalId(hospitalId)
                    .tableNameOrPatientType(tableName)
                    .build();
        }
        
        return ResponseResult.success(result);
    }
    
    @Override
    public ResponseResult<String> getLastSyncTime(String hospitalId, String tableName) {
        log.info("获取医院[{}]的[{}]数据最后同步时间", hospitalId, tableName);
        
        LocalDateTime lastSyncTime = getLastSyncTimeFromRedis(hospitalId, tableName);
        if (lastSyncTime == null) {
            return ResponseResult.success("未找到最后同步时间记录");
        }
        
        return ResponseResult.success(lastSyncTime.format(DATE_TIME_FORMATTER));
    }
    
    @Override
    public ResponseResult<String> clearTaskStatus(String hospitalId, String tableNameOrType, String syncType) {
        log.info("清理任务状态 - 医院ID: {}, 表名或患者类型: {}, 同步类型: {}", hospitalId, tableNameOrType, syncType);
        
        String taskKey = buildTaskKey(hospitalId, tableNameOrType, syncType);
        boolean removed = false;
        
        if (RUNNING_TASKS.containsKey(taskKey)) {
            RUNNING_TASKS.remove(taskKey);
            removed = true;
            log.info("已清理任务状态: {}", taskKey);
        }
        
        if (RETRY_COUNT.containsKey(taskKey)) {
            RETRY_COUNT.remove(taskKey);
            removed = true;
            log.info("已清理重试计数: {}", taskKey);
        }
        
        // 清理Redis中的同步状态
        try {
            String redisKey = String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableNameOrType);
            if (redisUtils.hasKey(redisKey)) {
                redisUtils.delete(redisKey);
                removed = true;
                log.info("已清理Redis中的同步状态: {}", redisKey);
            }
        } catch (Exception e) {
            log.error("清理Redis中的同步状态时发生异常: {}", e.getMessage(), e);
        }
        
        if (removed) {
            return ResponseResult.success("任务状态清理成功: " + taskKey);
        } else {
            return ResponseResult.success("未找到需要清理的任务状态: " + taskKey);
        }
    }
    
    @Override
    public ResponseResult<String> clearAllTaskStatus() {
        log.info("清理所有任务状态");
        
        int count = RUNNING_TASKS.size();
        RUNNING_TASKS.clear();
        RETRY_COUNT.clear();
        
        log.info("已清理{}个任务状态", count);
        
        return ResponseResult.success("已清理所有任务状态，共" + count + "个");
    }
    
    /**
     * 将数据存入Redis
     *
     * @param hospitalId 医院ID
     * @param tableName 表名
     * @param dataList 数据列表
     */
    private void saveDataToRedis(String hospitalId, String tableName, List<?> dataList) {
        String redisKey;
        
        switch (tableName) {
            case SyncConstants.TableName.DEPARTMENT:
                redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_DEPARTMENT + hospitalId;
                break;
            case SyncConstants.TableName.USER:
                redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_USER + hospitalId;
                break;
            case SyncConstants.TableName.PATIENT:
                redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT + hospitalId;
                break;
            default:
                log.error("未知的表名: {}", tableName);
                throw new BusinessException(ResultCode.PARAM_ERROR, "未知的表名: " + tableName);
        }
        
        // 存入Redis，过期时间设置为24小时
        redisUtils.set(redisKey, JSON.toJSONString(dataList), SyncConstants.ExpireTime.DEFAULT);
    }
    
    /**
     * 模拟获取数据（在实际应用中会从数据库或外部系统获取）
     *
     * @param tableName 表名
     * @return 数据列表
     */
    private List<?> mockFetchData(String tableName) {
        // 模拟数据获取延迟
        try {
            // 随机延迟1-3秒，模拟网络延迟或数据库查询耗时
            Thread.sleep((long) (Math.random() * 2000) + 1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        switch (tableName) {
            case SyncConstants.TableName.DEPARTMENT:
                return mockDepartmentList();
            case SyncConstants.TableName.USER:
                return mockUserList();
            case SyncConstants.TableName.PATIENT:
                return mockPatientList();
            default:
                return new ArrayList<>();
        }
    }
    
    /**
     * 模拟获取增量数据（在实际应用中会从数据库或外部系统获取）
     *
     * @param tableName 表名
     * @param lastSyncTime 最后同步时间
     * @return 增量数据列表
     */
    private List<?> mockFetchIncrementalData(String tableName, LocalDateTime lastSyncTime) {
        // 模拟数据获取延迟
        try {
            // 随机延迟0.5-1.5秒，增量同步通常比全量同步快
            Thread.sleep((long) (Math.random() * 1000) + 500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 在实际应用中，会根据lastSyncTime过滤数据
        // 这里简化实现，随机返回部分数据作为增量
        List<?> allData = mockFetchData(tableName);
        List<Object> incrementalData = new ArrayList<>();
        
        // 随机选择30%的数据作为增量数据
        for (Object item : allData) {
            if (Math.random() < 0.3) {
                incrementalData.add(item);
            }
        }
        
        return incrementalData;
    }
    
    /**
     * 模拟获取指定类型的患者数据
     *
     * @param patientType 患者类型
     * @return 患者列表
     */
    private List<Patient> mockFetchPatientByType(String patientType) {
        // 模拟数据获取延迟
        try {
            Thread.sleep((long) (Math.random() * 1000) + 500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        List<Patient> allPatients = mockPatientList();
        List<Patient> filteredPatients = new ArrayList<>();
        
        // 根据患者类型过滤
        for (Patient patient : allPatients) {
            boolean match = false;
            
            try {
                if (SyncConstants.PatientType.IN.equals(patientType)) {
                    // 入院患者：有入院时间，没有出院时间
                    match = patient.getInhospitalTime() != null && patient.getOuthospitalTime() == null;
                } else if (SyncConstants.PatientType.UP.equals(patientType)) {
                    // 在院患者：状态为在院（1）
                    match = patient.getStatus() != null && patient.getStatus() == 1;
                } else if (SyncConstants.PatientType.OUT.equals(patientType)) {
                    // 出院患者：状态为出院（2）
                    match = patient.getStatus() != null && patient.getStatus() == 2;
                }
            } catch (Exception e) {
                // 处理异常场景，例如数据缺失或格式问题
                log.error("处理患者数据时发生异常", e);
                // 在实际应用中，可能需要记录错误数据并继续处理其他数据
                continue;
            }
            
            if (match) {
                filteredPatients.add(patient);
            }
        }
        
        return filteredPatients;
    }
    
    /**
     * 模拟科室列表
     */
    private List<Department> mockDepartmentList() {
        List<Department> departments = new ArrayList<>();
        
        // 模拟科室数据
        Department dept1 = new Department();
        dept1.setDepartmentId(1);
        dept1.setCode("NEU");
        dept1.setName("神经科");
        dept1.setUpdatedAt(LocalDateTime.now());
        departments.add(dept1);
        
        Department dept2 = new Department();
        dept2.setDepartmentId(2);
        dept2.setCode("CAR");
        dept2.setName("心血管科");
        dept2.setUpdatedAt(LocalDateTime.now().minusDays(1));
        departments.add(dept2);
        
        Department dept3 = new Department();
        dept3.setDepartmentId(3);
        dept3.setCode("PED");
        dept3.setName("儿科");
        dept3.setUpdatedAt(LocalDateTime.now().minusDays(2));
        departments.add(dept3);
        
        Department dept4 = new Department();
        dept4.setDepartmentId(4);
        dept4.setCode("ORT");
        dept4.setName("骨科");
        dept4.setUpdatedAt(LocalDateTime.now().minusHours(12));
        departments.add(dept4);
        
        Department dept5 = new Department();
        dept5.setDepartmentId(5);
        dept5.setCode("DER");
        dept5.setName("皮肤科");
        dept5.setUpdatedAt(LocalDateTime.now().minusHours(6));
        departments.add(dept5);
        
        return departments;
    }
    
    /**
     * 模拟医护人员列表
     */
    private List<User> mockUserList() {
        List<User> users = new ArrayList<>();
        
        // 模拟医护人员数据
        User user1 = new User();
        user1.setUserName("D001");
        user1.setName("张三");
        user1.setSex(1);
        user1.setRoleId(1);  // 医生
        user1.setDeptId(1);
        user1.setInpatientWard("1病区");
        user1.setMobile("13800138001");
        user1.setUpdatedAt(LocalDateTime.now());
        users.add(user1);
        
        User user2 = new User();
        user2.setUserName("N001");
        user2.setName("李四");
        user2.setSex(2);
        user2.setRoleId(2);  // 护士
        user2.setDeptId(1);
        user2.setInpatientWard("1病区,2病区");
        user2.setMobile("13800138002");
        user2.setUpdatedAt(LocalDateTime.now().minusHours(2));
        users.add(user2);
        
        User user3 = new User();
        user3.setUserName("D002");
        user3.setName("王五");
        user3.setSex(1);
        user3.setRoleId(1);  // 医生
        user3.setDeptId(2);
        user3.setInpatientWard("3病区");
        user3.setMobile("13800138003");
        user3.setUpdatedAt(LocalDateTime.now().minusDays(1));
        users.add(user3);
        
        User user4 = new User();
        user4.setUserName("N002");
        user4.setName("赵六");
        user4.setSex(2);
        user4.setRoleId(2);  // 护士
        user4.setDeptId(2);
        user4.setInpatientWard("3病区,4病区");
        user4.setMobile("13800138004");
        user4.setUpdatedAt(LocalDateTime.now().minusDays(2));
        users.add(user4);
        
        User user5 = new User();
        user5.setUserName("D003");
        user5.setName("钱七");
        user5.setSex(1);
        user5.setRoleId(1);  // 医生
        user5.setDeptId(3);
        user5.setInpatientWard("5病区");
        user5.setMobile("13800138005");
        user5.setUpdatedAt(LocalDateTime.now().minusHours(12));
        users.add(user5);
        
        return users;
    }
    
    /**
     * 模拟患者列表
     */
    private List<Patient> mockPatientList() {
        List<Patient> patients = new ArrayList<>();
        
        // 患者1：在院患者（入院未出院）
        Patient patient1 = new Patient();
        patient1.setInpatientInfoId("P001");
        patient1.setName("孙八");
        patient1.setIdCard("110101199001011234");
        patient1.setMobile("13800138006");
        patient1.setSex(1);
        patient1.setAge(30);
        patient1.setBirthday(LocalDate.of(1990, 1, 1));
        patient1.setHospitalizationNo("H001");
        patient1.setInhospitalDiagnose("高血压");
        patient1.setDeptId(2);  // 心血管科
        patient1.setSickbedNo("2-101");
        patient1.setDoctorId("D002");
        patient1.setNurseId("N002");
        patient1.setNurseLevel(2);
        patient1.setInhospitalTime(LocalDateTime.now().minusDays(5));
        patient1.setOuthospitalTime(null);  // 未出院
        patient1.setStatus(1);  // 在院
        patient1.setCategory("医保");
        patient1.setInpatientWard("3病区");
        patient1.setUpdatedAt(LocalDateTime.now().minusHours(2));
        patients.add(patient1);
        
        // 患者2：出院患者
        Patient patient2 = new Patient();
        patient2.setInpatientInfoId("P002");
        patient2.setName("周九");
        patient2.setIdCard("110101199101022345");
        patient2.setMobile("13800138007");
        patient2.setSex(2);
        patient2.setAge(29);
        patient2.setBirthday(LocalDate.of(1991, 1, 2));
        patient2.setHospitalizationNo("H002");
        patient2.setInhospitalDiagnose("头痛");
        patient2.setDeptId(1);  // 神经科
        patient2.setSickbedNo("1-102");
        patient2.setDoctorId("D001");
        patient2.setNurseId("N001");
        patient2.setNurseLevel(3);
        patient2.setInhospitalTime(LocalDateTime.now().minusDays(10));
        patient2.setOuthospitalTime(LocalDateTime.now().minusDays(2));  // 已出院
        patient2.setStatus(2);  // 出院
        patient2.setCategory("自费");
        patient2.setInpatientWard("1病区");
        patient2.setUpdatedAt(LocalDateTime.now().minusDays(2));
        patients.add(patient2);
        
        // 患者3：入院患者（刚入院）
        Patient patient3 = new Patient();
        patient3.setInpatientInfoId("P003");
        patient3.setName("吴十");
        patient3.setIdCard("110101199202033456");
        patient3.setMobile("13800138008");
        patient3.setSex(1);
        patient3.setAge(28);
        patient3.setBirthday(LocalDate.of(1992, 2, 3));
        patient3.setHospitalizationNo("H003");
        patient3.setInhospitalDiagnose("肺炎");
        patient3.setDeptId(4);  // 呼吸科
        patient3.setSickbedNo("4-103");
        patient3.setDoctorId("D003");
        patient3.setNurseId("N002");
        patient3.setNurseLevel(2);
        patient3.setInhospitalTime(LocalDateTime.now().minusHours(6));  // 刚入院
        patient3.setOuthospitalTime(null);  // 未出院
        patient3.setStatus(1);  // 在院
        patient3.setCategory("医保");
        patient3.setInpatientWard("5病区");
        patient3.setUpdatedAt(LocalDateTime.now().minusHours(6));
        patients.add(patient3);
        
        // 患者4：异常情况 - 状态与出院时间不一致（数据异常）
        Patient patient4 = new Patient();
        patient4.setInpatientInfoId("P004");
        patient4.setName("郑十一");
        patient4.setIdCard("110101199303044567");
        patient4.setMobile("13800138009");
        patient4.setSex(2);
        patient4.setAge(27);
        patient4.setBirthday(LocalDate.of(1993, 3, 4));
        patient4.setHospitalizationNo("H004");
        patient4.setInhospitalDiagnose("骨折");
        patient4.setDeptId(3);  // 骨科
        patient4.setSickbedNo("3-104");
        patient4.setDoctorId("D002");
        patient4.setNurseId("N001");
        patient4.setNurseLevel(1);
        patient4.setInhospitalTime(LocalDateTime.now().minusDays(8));
        patient4.setOuthospitalTime(LocalDateTime.now().minusDays(1));  // 有出院时间
        patient4.setStatus(1);  // 但状态是在院（不一致）
        patient4.setCategory("自费");
        patient4.setInpatientWard("3病区");
        patient4.setUpdatedAt(LocalDateTime.now().minusDays(1));
        patients.add(patient4);
        
        // 患者5：异常情况 - 出院时间早于入院时间
        Patient patient5 = new Patient();
        patient5.setInpatientInfoId("P005");
        patient5.setName("王十二");
        patient5.setIdCard("110101199404055678");
        patient5.setMobile("13800138010");
        patient5.setSex(1);
        patient5.setAge(26);
        patient5.setBirthday(LocalDate.of(1994, 4, 5));
        patient5.setHospitalizationNo("H005");
        patient5.setInhospitalDiagnose("胃炎");
        patient5.setDeptId(5);  // 消化科
        patient5.setSickbedNo("5-105");
        patient5.setDoctorId("D003");
        patient5.setNurseId("N002");
        patient5.setNurseLevel(2);
        patient5.setInhospitalTime(LocalDateTime.now().minusDays(3));
        patient5.setOuthospitalTime(LocalDateTime.now().minusDays(4));  // 出院时间早于入院时间
        patient5.setStatus(2);  // 出院
        patient5.setCategory("医保");
        patient5.setInpatientWard("5病区");
        patient5.setUpdatedAt(LocalDateTime.now().minusDays(3));
        patients.add(patient5);
        
        return patients;
    }

    /**
     * 构建任务键
     */
    private String buildTaskKey(String hospitalId, String tableNameOrType, String syncType) {
        return hospitalId + ":" + tableNameOrType + ":" + syncType;
    }
    
    /**
     * 从Redis获取同步状态
     */
    private SyncResultDTO getSyncStatusFromRedis(String hospitalId, String tableName) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableName);
        Object obj = redisUtils.get(key);
        if (obj != null) {
            SyncResultDTO result = JSON.parseObject(obj.toString(), SyncResultDTO.class);
            
            // 检查是否是"幽灵任务"（Redis中存在但内存中不存在）
            String taskKey = buildTaskKey(hospitalId, 
                                         tableName.startsWith("patient:") ? tableName.substring(8) : tableName, 
                                         tableName.startsWith("patient:") ? "patient" : "full");
            
            // 如果Redis中的状态是"进行中"，但内存中没有对应的任务，则认为是幽灵任务
            if (result.getStatus() == SyncConstants.SyncStatus.IN_PROGRESS && !RUNNING_TASKS.containsKey(taskKey)) {
                log.warn("检测到幽灵任务: {}, Redis状态为'进行中'但内存中不存在此任务，将自动清理", taskKey);
                
                // 更新状态为失败
                result.setStatus(SyncConstants.SyncStatus.FAILED);
                result.setMessage("任务异常终止");
                result.setEndTime(LocalDateTime.now());
                if (result.getStartTime() != null) {
                    result.setCostTime(Duration.between(result.getStartTime(), result.getEndTime()).toMillis());
                }
                
                // 保存更新后的状态到Redis
                saveSyncStatusToRedis(hospitalId, tableName, result);
                
                log.info("已清理幽灵任务状态: {}", taskKey);
            }
            
            return result;
        }
        return null;
    }
    
    /**
     * 保存同步状态到Redis
     */
    private void saveSyncStatusToRedis(String hospitalId, String tableName, SyncResultDTO result) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableName);
        redisUtils.set(key, JSON.toJSONString(result), SyncConstants.ExpireTime.SYNC_STATUS);
    }
    
    /**
     * 保存最后同步时间到Redis
     */
    private void saveLastSyncTimeToRedis(String hospitalId, String tableName, LocalDateTime time) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_LAST_TIME, hospitalId, tableName);
        long expireTime = SyncConstants.ExpireTime.LAST_SYNC_TIME;
        String timeStr = time.format(DATE_TIME_FORMATTER);
        
        // 处理过期时间为0的情况（永不过期）
        if (expireTime <= 0) {
            // 使用不带过期时间的set方法
            redisUtils.set(key, timeStr);
            log.debug("保存最后同步时间到Redis（永不过期）: {}", key);
        } else {
            // 确保过期时间是正整数
            redisUtils.set(key, timeStr, Math.max(1, expireTime));
            log.debug("保存最后同步时间到Redis（过期时间: {}秒）: {}", expireTime, key);
        }
        
        // 验证数据是否成功存储（仅在DEBUG级别输出）
        if (log.isDebugEnabled() && redisUtils.hasKey(key)) {
            Object storedValue = redisUtils.get(key);
            log.debug("验证Redis存储结果: 键={}, 值={}", key, storedValue);
        }
    }
    
    /**
     * 从Redis获取最后同步时间
     */
    private LocalDateTime getLastSyncTimeFromRedis(String hospitalId, String tableName) {
        String key = String.format(SyncConstants.RedisKeyPrefix.SYNC_LAST_TIME, hospitalId, tableName);
        Object obj = redisUtils.get(key);
        if (obj != null) {
            return LocalDateTime.parse(obj.toString(), DATE_TIME_FORMATTER);
        }
        return null;
    }

    /**
     * 测试直接将患者数据存入Redis Hash结构
     * 
     * @param hospitalId 医院ID
     * @param patientType 患者类型
     * @param patient 患者数据
     * @return 存储结果
     */
    public boolean testSavePatientToRedisHash(String hospitalId, String patientType, Patient patient) {
        if (patient == null || patient.getInpatientInfoId() == null) {
            log.error("患者数据为空或缺少inpatientInfoId");
            return false;
        }
        
        try {
            // 构建Redis键
            String redisKey = SyncConstants.RedisKeyPrefix.HOSPITAL_PATIENT_TYPE + hospitalId + ":" + patientType;
            log.info("测试存储患者数据到Redis，键: {}, 患者ID: {}", redisKey, patient.getInpatientInfoId());
            
            // 将患者对象转换为JSON字符串
            String patientJson = JSON.toJSONString(patient);
            log.info("患者JSON数据: {}", patientJson);
            
            // 使用Hash结构存储患者数据
            redisUtils.hSet(redisKey, patient.getInpatientInfoId(), patientJson);
            log.info("患者数据已存入Redis Hash结构");
            
            // 设置过期时间
            redisUtils.expire(redisKey, SyncConstants.ExpireTime.DEFAULT);
            
            // 验证数据是否成功存储
            if (redisUtils.hasKey(redisKey)) {
                Object storedData = redisUtils.hGet(redisKey, patient.getInpatientInfoId());
                if (storedData != null) {
                    log.info("验证Redis存储成功: 键={}, 字段={}, 值长度={}", 
                            redisKey, patient.getInpatientInfoId(), storedData.toString().length());
                    return true;
                } else {
                    log.warn("验证Redis存储失败: 字段{}不存在", patient.getInpatientInfoId());
                }
            } else {
                log.warn("验证Redis存储失败: 键{}不存在", redisKey);
            }
            
            return false;
        } catch (Exception e) {
            log.error("测试存储患者数据到Redis失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 合并患者数据，保留新数据中的非null字段，其他字段使用现有数据
     * 使用直接的getter/setter方法代替反射，提高性能
     * 
     * @param existingPatient 现有患者数据
     * @param newPatient 新患者数据
     * @return 合并后的患者数据以及是否有字段更新的信息
     */
    private Map<String, Object> mergePatientData(Patient existingPatient, Patient newPatient) {
        Patient mergedPatient = new Patient();
        int updatedFieldCount = 0;
        
        // 复制现有患者的所有字段
        BeanUtils.copyProperties(existingPatient, mergedPatient);
        
        // 使用直接的getter/setter方法代替反射，提高性能
        
        // 处理inpatientInfoId字段
        if (newPatient.getInpatientInfoId() != null && !Objects.equals(newPatient.getInpatientInfoId(), existingPatient.getInpatientInfoId())) {
            mergedPatient.setInpatientInfoId(newPatient.getInpatientInfoId());
            updatedFieldCount++;
        }
        
        // 处理name字段
        if (newPatient.getName() != null && !Objects.equals(newPatient.getName(), existingPatient.getName())) {
            mergedPatient.setName(newPatient.getName());
            updatedFieldCount++;
        }
        
        // 处理idCard字段
        if (newPatient.getIdCard() != null && !Objects.equals(newPatient.getIdCard(), existingPatient.getIdCard())) {
            mergedPatient.setIdCard(newPatient.getIdCard());
            updatedFieldCount++;
        }
        
        // 处理mobile字段
        if (newPatient.getMobile() != null && !Objects.equals(newPatient.getMobile(), existingPatient.getMobile())) {
            mergedPatient.setMobile(newPatient.getMobile());
            updatedFieldCount++;
        }
        
        // 处理sex字段
        if (newPatient.getSex() != null && !Objects.equals(newPatient.getSex(), existingPatient.getSex())) {
            mergedPatient.setSex(newPatient.getSex());
            updatedFieldCount++;
        }
        
        // 处理age字段
        if (newPatient.getAge() != null && !Objects.equals(newPatient.getAge(), existingPatient.getAge())) {
            mergedPatient.setAge(newPatient.getAge());
            updatedFieldCount++;
        }
        
        // 处理birthday字段
        if (newPatient.getBirthday() != null && !Objects.equals(newPatient.getBirthday(), existingPatient.getBirthday())) {
            mergedPatient.setBirthday(newPatient.getBirthday());
            updatedFieldCount++;
        }
        
        // 处理hospitalizationNo字段
        if (newPatient.getHospitalizationNo() != null && !Objects.equals(newPatient.getHospitalizationNo(), existingPatient.getHospitalizationNo())) {
            mergedPatient.setHospitalizationNo(newPatient.getHospitalizationNo());
            updatedFieldCount++;
        }
        
        // 处理inhospitalDiagnose字段
        if (newPatient.getInhospitalDiagnose() != null && !Objects.equals(newPatient.getInhospitalDiagnose(), existingPatient.getInhospitalDiagnose())) {
            mergedPatient.setInhospitalDiagnose(newPatient.getInhospitalDiagnose());
            updatedFieldCount++;
        }
        
        // 处理deptId字段
        if (newPatient.getDeptId() != null && !Objects.equals(newPatient.getDeptId(), existingPatient.getDeptId())) {
            mergedPatient.setDeptId(newPatient.getDeptId());
            updatedFieldCount++;
        }
        
        // 处理sickbedNo字段
        if (newPatient.getSickbedNo() != null && !Objects.equals(newPatient.getSickbedNo(), existingPatient.getSickbedNo())) {
            mergedPatient.setSickbedNo(newPatient.getSickbedNo());
            updatedFieldCount++;
        }
        
        // 处理doctorId字段
        if (newPatient.getDoctorId() != null && !Objects.equals(newPatient.getDoctorId(), existingPatient.getDoctorId())) {
            mergedPatient.setDoctorId(newPatient.getDoctorId());
            updatedFieldCount++;
        }
        
        // 处理nurseId字段
        if (newPatient.getNurseId() != null && !Objects.equals(newPatient.getNurseId(), existingPatient.getNurseId())) {
            mergedPatient.setNurseId(newPatient.getNurseId());
            updatedFieldCount++;
        }
        
        // 处理nurseLevel字段
        if (newPatient.getNurseLevel() != null && !Objects.equals(newPatient.getNurseLevel(), existingPatient.getNurseLevel())) {
            mergedPatient.setNurseLevel(newPatient.getNurseLevel());
            updatedFieldCount++;
        }
        
        // 处理inhospitalTime字段
        if (newPatient.getInhospitalTime() != null && !Objects.equals(newPatient.getInhospitalTime(), existingPatient.getInhospitalTime())) {
            mergedPatient.setInhospitalTime(newPatient.getInhospitalTime());
            updatedFieldCount++;
        }
        
        // 处理outhospitalTime字段
        if (newPatient.getOuthospitalTime() != null && !Objects.equals(newPatient.getOuthospitalTime(), existingPatient.getOuthospitalTime())) {
            mergedPatient.setOuthospitalTime(newPatient.getOuthospitalTime());
            updatedFieldCount++;
        }
        
        // 处理status字段
        if (newPatient.getStatus() != null && !Objects.equals(newPatient.getStatus(), existingPatient.getStatus())) {
            mergedPatient.setStatus(newPatient.getStatus());
            updatedFieldCount++;
        }
        
        // 处理category字段
        if (newPatient.getCategory() != null && !Objects.equals(newPatient.getCategory(), existingPatient.getCategory())) {
            mergedPatient.setCategory(newPatient.getCategory());
            updatedFieldCount++;
        }
        
        // 处理inpatientWard字段
        if (newPatient.getInpatientWard() != null && !Objects.equals(newPatient.getInpatientWard(), existingPatient.getInpatientWard())) {
            mergedPatient.setInpatientWard(newPatient.getInpatientWard());
            updatedFieldCount++;
        }
        
        // 如果有字段更新，则更新时间戳为当前时间
        if (updatedFieldCount > 0) {
            mergedPatient.setUpdatedAt(LocalDateTime.now());
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("patient", mergedPatient);
        result.put("updatedFieldCount", updatedFieldCount);
        
        return result;
    }
    
    /**
     * 计算Patient对象中非null字段的数量
     * 
     * @param patient 患者对象
     * @return 非null字段数量
     */
    private int countNonNullFields(Patient patient) {
        if (patient == null) {
            return 0;
        }
        
        int count = 0;
        Field[] fields = Patient.class.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                if (field.get(patient) != null) {
                    count++;
                }
            } catch (IllegalAccessException e) {
                // 忽略访问异常
            }
        }
        
        return count;
    }
    
    /**
     * 生成患者的业务唯一键，用于数据去重
     * 仅使用住院号+入院时间 (hospitalizationNo+inhospitalTime)作为唯一标识
     * 
     * @param patient 患者对象
     * @return 业务唯一键，如果缺少必要字段则返回null
     */
    private String generatePatientBusinessKey(Patient patient) {
        if (patient == null) {
            return null;
        }
        
        // 只使用住院号+入院时间作为唯一标识
        if (patient.getHospitalizationNo() != null && patient.getInhospitalTime() != null) {
            String formattedTime = patient.getInhospitalTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            return "hosptime_" + patient.getHospitalizationNo() + "_" + formattedTime;
        }
        
        // 如果缺少必要字段，返回null表示无法生成有效的业务唯一键
        return null;
    }
    
    /**
     * 验证Redis数据是否成功写入
     * 
     * @param redisKey Redis键
     * @param dataMap 要验证的数据Map
     * @return 验证是否成功
     */
    private boolean validateRedisData(String redisKey, Map<String, Object> dataMap) {
        if (dataMap == null || dataMap.isEmpty()) {
            log.warn("无数据需要验证");
            return false;
        }
        
        if (!redisUtils.hasKey(redisKey)) {
            log.error("Redis键不存在: {}", redisKey);
            return false;
        }
        
        // 随机抽样验证
        int sampleSize = Math.min(5, dataMap.size());
        List<String> sampleKeys = new ArrayList<>(dataMap.keySet());
        Collections.shuffle(sampleKeys);
        
        boolean allValid = true;
        int validCount = 0;
        
        for (int i = 0; i < sampleSize; i++) {
            String field = sampleKeys.get(i);
            boolean exists = redisUtils.hExists(redisKey, field);
            
            if (exists) {
                validCount++;
                log.debug("验证Redis字段存在: 键={}, 字段={}, 结果=成功", redisKey, field);
            } else {
                allValid = false;
                log.warn("验证Redis字段存在: 键={}, 字段={}, 结果=失败", redisKey, field);
            }
        }
        
        log.info("Redis数据验证结果: 键={}, 抽样数量={}, 验证成功数量={}, 全部验证通过={}", 
                redisKey, sampleSize, validCount, allValid);
        
        return allValid;
    }
} 