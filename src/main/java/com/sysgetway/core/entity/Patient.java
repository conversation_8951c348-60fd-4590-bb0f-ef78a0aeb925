package com.sysgetway.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 患者实体类
 */
@Data
@TableName("patient")
@ApiModel(description = "患者信息实体")
public class Patient implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 信息唯一ID,每次出入院一个ID编号
     */
    @TableId(value = "inpatient_info_id")
    @ApiModelProperty(value = "患者信息唯一ID，每次出入院一个ID编号", required = true, example = "P001")
    private String inpatientInfoId;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名", required = true, example = "张三")
    private String name;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号", example = "110101199001011234")
    private String idCard;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", example = "13800138000")
    private String mobile;

    /**
     * 性别 1.男 2.女
     */
    @ApiModelProperty(value = "性别", notes = "1-男，2-女", allowableValues = "1,2", example = "1")
    private Integer sex;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄", example = "30")
    private Integer age;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日", example = "1990-01-01")
    private LocalDate birthday;

    /**
     * 住院号
     */
    @ApiModelProperty(value = "住院号", example = "H001")
    private String hospitalizationNo;

    /**
     * 入院诊断
     */
    @ApiModelProperty(value = "入院诊断", example = "高血压")
    private String inhospitalDiagnose;

    /**
     * 科室ID
     */
    @ApiModelProperty(value = "科室ID", example = "1")
    private Integer deptId;

    /**
     * 床位号
     */
    @ApiModelProperty(value = "床位号", example = "1-101")
    private String sickbedNo;

    /**
     * 责任医生ID
     */
    @ApiModelProperty(value = "责任医生ID", example = "D001")
    private String doctorId;

    /**
     * 责任护士ID
     */
    @ApiModelProperty(value = "责任护士ID", example = "N001")
    private String nurseId;

    /**
     * 护理级别 1.一级 2.二级 3.三级 4.特级护理
     */
    @ApiModelProperty(value = "护理级别", notes = "1-一级护理，2-二级护理，3-三级护理，4-特级护理", allowableValues = "1,2,3,4", example = "2")
    private Integer nurseLevel;

    /**
     * 入院时间
     */
    @ApiModelProperty(value = "入院时间", example = "2023-01-01T10:00:00")
    private LocalDateTime inhospitalTime;

    /**
     * 出院时间
     */
    @ApiModelProperty(value = "出院时间", example = "2023-01-10T10:00:00")
    private LocalDateTime outhospitalTime;

    /**
     * 患者当前状态 1.在院 2.出院
     */
    @ApiModelProperty(value = "患者当前状态", notes = "1-在院，2-出院", allowableValues = "1,2", example = "1")
    private Integer status;

    /**
     * 患者类别，如医保、自费等
     */
    @ApiModelProperty(value = "患者类别", notes = "如医保、自费等", example = "医保")
    private String category;

    /**
     * 病区
     */
    @ApiModelProperty(value = "病区", example = "内科一病区")
    private String inpatientWard;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间", example = "2023-01-01T10:00:00")
    private LocalDateTime updatedAt;
}