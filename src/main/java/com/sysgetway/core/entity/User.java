package com.sysgetway.core.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 医护人员实体类
 */
@Data
@TableName("user")
@ApiModel(description = "医护人员信息实体")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 医护人员账号(可以是工号)
     */
    @TableId(value = "user_name")
    @ApiModelProperty(value = "医护人员账号（可以是工号）", required = true, example = "D001")
    private String userName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "医护人员姓名", required = true, example = "李医生")
    private String name;

    /**
     * 性别 1.男 2.女
     */
    @ApiModelProperty(value = "性别", notes = "1-男，2-女", allowableValues = "1,2", example = "1")
    private Integer sex;

    /**
     * 角色ID 1.医生 2.护士
     */
    @ApiModelProperty(value = "角色ID", notes = "1-医生，2-护士", allowableValues = "1,2", example = "1")
    private Integer roleId;

    /**
     * 科室ID
     */
    @ApiModelProperty(value = "科室ID", example = "1")
    private Integer deptId;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间", example = "2023-01-01T10:00:00")
    private LocalDateTime updatedAt;

    /**
     * 所管理的病区,多个使用半角逗号分隔
     */
    @ApiModelProperty(value = "所管理的病区", notes = "多个病区使用半角逗号分隔", example = "内科一病区,内科二病区")
    private String inpatientWard;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String mobile;
}