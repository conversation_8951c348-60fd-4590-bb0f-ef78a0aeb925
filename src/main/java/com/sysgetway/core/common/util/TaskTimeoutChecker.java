package com.sysgetway.core.common.util;

import com.sysgetway.core.common.constant.SyncConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 任务超时检测器
 * 定期检查任务是否超时，如果超时则自动清理
 */
@Component
@Slf4j
public class TaskTimeoutChecker {

    @Resource
    private AsyncTaskManager asyncTaskManager;
    
    @Resource
    private RedisUtils redisUtils;
    
    /**
     * 每分钟检查一次任务是否超时
     */
    @Scheduled(fixedRate = 60000)
    public void checkTaskTimeout() {
        int taskCount = asyncTaskManager.getTaskCount();
        if (taskCount == 0) {
            return; // 没有任务，直接返回
        }
        
        log.debug("开始检查任务超时，当前任务数量: {}", taskCount);
        
        for (String taskKey : asyncTaskManager.getAllTaskKeys()) {
            if (asyncTaskManager.isTaskTimedOut(taskKey)) {
                log.warn("检测到超时任务: {}, 将自动清理", taskKey);
                
                // 解析任务键，获取医院ID和表名/患者类型
                String[] parts = taskKey.split(":");
                if (parts.length >= 3) {
                    String hospitalId = parts[0];
                    String tableNameOrType = parts[1];
                    String syncType = parts[2];
                    
                    // 更新Redis中的状态为超时
                    String redisKey = String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, 
                            syncType.equals("patient") ? "patient:" + tableNameOrType : tableNameOrType);
                    
                    try {
                        Object obj = redisUtils.get(redisKey);
                        if (obj != null) {
                            // 更新状态为失败，原因为超时
                            redisUtils.set(redisKey, obj.toString().replace("\"status\":0", "\"status\":2")
                                    .replace("\"message\":\"正在进行中\"", "\"message\":\"任务超时自动终止\""), 
                                    SyncConstants.ExpireTime.SYNC_STATUS);
                            
                            log.info("已更新超时任务的Redis状态: {}", redisKey);
                        }
                    } catch (Exception e) {
                        log.error("更新超时任务的Redis状态失败: {}", e.getMessage(), e);
                    }
                }
                
                // 移除超时任务
                asyncTaskManager.removeTask(taskKey);
                log.info("已清理超时任务: {}", taskKey);
            }
        }
    }
} 