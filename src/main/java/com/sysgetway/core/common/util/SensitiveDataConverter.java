package com.sysgetway.core.common.util;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 敏感数据脱敏转换器
 * 用于在日志输出前自动对敏感信息进行脱敏处理
 */
public class SensitiveDataConverter extends MessageConverter {

    /**
     * 身份证号正则表达式
     */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("(\\d{6})\\d{8}(\\w{4})");
    
    /**
     * 手机号正则表达式
     */
    private static final Pattern MOBILE_PATTERN = Pattern.compile("(\\d{3})\\d{4}(\\d{4})");
    
    /**
     * 银行卡号正则表达式
     */
    private static final Pattern BANK_CARD_PATTERN = Pattern.compile("(\\d{4})\\d+(\\d{4})");
    
    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("(\\w{2})\\w+@(\\w+)(\\.\\w+)+");
    
    /**
     * 转换日志消息，对敏感信息进行脱敏
     */
    @Override
    public String convert(ILoggingEvent event) {
        // 获取原始日志消息
        String originalMessage = event.getFormattedMessage();
        if (StringUtils.isBlank(originalMessage)) {
            return originalMessage;
        }
        
        // 依次进行各类敏感信息脱敏
        String result = originalMessage;
        result = maskIdCard(result);
        result = maskMobile(result);
        result = maskBankCard(result);
        result = maskEmail(result);
        
        return result;
    }
    
    /**
     * 对身份证号进行脱敏处理
     * 脱敏规则：保留前6位和后4位，中间用*代替
     */
    private String maskIdCard(String message) {
        if (StringUtils.isBlank(message)) {
            return message;
        }
        
        Matcher matcher = ID_CARD_PATTERN.matcher(message);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1) + "********" + matcher.group(2));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    
    /**
     * 对手机号进行脱敏处理
     * 脱敏规则：保留前3位和后4位，中间用*代替
     */
    private String maskMobile(String message) {
        if (StringUtils.isBlank(message)) {
            return message;
        }
        
        Matcher matcher = MOBILE_PATTERN.matcher(message);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1) + "****" + matcher.group(2));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    
    /**
     * 对银行卡号进行脱敏处理
     * 脱敏规则：保留前4位和后4位，中间用*代替
     */
    private String maskBankCard(String message) {
        if (StringUtils.isBlank(message)) {
            return message;
        }
        
        Matcher matcher = BANK_CARD_PATTERN.matcher(message);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1) + "********" + matcher.group(2));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    
    /**
     * 对邮箱进行脱敏处理
     * 脱敏规则：邮箱前缀仅显示前两位，其他用*代替，@及后面的地址正常显示
     */
    private String maskEmail(String message) {
        if (StringUtils.isBlank(message)) {
            return message;
        }
        
        Matcher matcher = EMAIL_PATTERN.matcher(message);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1) + "***@" + matcher.group(2) + matcher.group(3));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
} 