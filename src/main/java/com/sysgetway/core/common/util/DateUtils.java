package com.sysgetway.core.common.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * 日期工具类
 */
public class DateUtils {

    /**
     * 标准日期格式
     */
    public static final String PATTERN_DATE = "yyyy-MM-dd";
    
    /**
     * 标准日期时间格式
     */
    public static final String PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 标准时间格式
     */
    public static final String PATTERN_TIME = "HH:mm:ss";
    
    /**
     * 紧凑型日期格式
     */
    public static final String PATTERN_DATE_COMPACT = "yyyyMMdd";
    
    /**
     * 紧凑型日期时间格式
     */
    public static final String PATTERN_DATETIME_COMPACT = "yyyyMMddHHmmss";
    
    /**
     * 中文日期格式
     */
    public static final String PATTERN_DATE_CN = "yyyy年MM月dd日";
    
    /**
     * 中文日期时间格式
     */
    public static final String PATTERN_DATETIME_CN = "yyyy年MM月dd日 HH时mm分ss秒";

    /**
     * 日期格式化器
     */
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(PATTERN_DATE);
    
    /**
     * 日期时间格式化器
     */
    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(PATTERN_DATETIME);
    
    /**
     * 时间格式化器
     */
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(PATTERN_TIME);

    /**
     * 获取当前日期
     * @return 当前日期
     */
    public static LocalDate getCurrentDate() {
        return LocalDate.now();
    }

    /**
     * 获取当前时间
     * @return 当前时间
     */
    public static LocalTime getCurrentTime() {
        return LocalTime.now();
    }

    /**
     * 获取当前日期时间
     * @return 当前日期时间
     */
    public static LocalDateTime getCurrentDateTime() {
        return LocalDateTime.now();
    }

    /**
     * 获取当前时间戳
     * @return 当前时间戳
     */
    public static long getCurrentTimestamp() {
        return Instant.now().toEpochMilli();
    }

    /**
     * 日期转字符串
     * @param date 日期
     * @param pattern 格式
     * @return 日期字符串
     */
    public static String format(LocalDate date, String pattern) {
        return date.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 时间转字符串
     * @param time 时间
     * @param pattern 格式
     * @return 时间字符串
     */
    public static String format(LocalTime time, String pattern) {
        return time.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 日期时间转字符串
     * @param dateTime 日期时间
     * @param pattern 格式
     * @return 日期时间字符串
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 字符串转日期
     * @param dateStr 日期字符串
     * @param pattern 格式
     * @return 日期
     */
    public static LocalDate parseDate(String dateStr, String pattern) {
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 字符串转时间
     * @param timeStr 时间字符串
     * @param pattern 格式
     * @return 时间
     */
    public static LocalTime parseTime(String timeStr, String pattern) {
        return LocalTime.parse(timeStr, DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 字符串转日期时间
     * @param dateTimeStr 日期时间字符串
     * @param pattern 格式
     * @return 日期时间
     */
    public static LocalDateTime parseDateTime(String dateTimeStr, String pattern) {
        return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * Date转LocalDateTime
     * @param date Date
     * @return LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * LocalDateTime转Date
     * @param localDateTime LocalDateTime
     * @return Date
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 计算两个日期之间的天数差
     * @param start 开始日期
     * @param end 结束日期
     * @return 天数差
     */
    public static long daysBetween(LocalDate start, LocalDate end) {
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 计算两个日期时间之间的小时差
     * @param start 开始日期时间
     * @param end 结束日期时间
     * @return 小时差
     */
    public static long hoursBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.HOURS.between(start, end);
    }

    /**
     * 计算两个日期时间之间的分钟差
     * @param start 开始日期时间
     * @param end 结束日期时间
     * @return 分钟差
     */
    public static long minutesBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.MINUTES.between(start, end);
    }

    /**
     * 计算两个日期时间之间的秒数差
     * @param start 开始日期时间
     * @param end 结束日期时间
     * @return 秒数差
     */
    public static long secondsBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.SECONDS.between(start, end);
    }

    /**
     * 日期增加天数
     * @param date 日期
     * @param days 天数
     * @return 增加后的日期
     */
    public static LocalDate plusDays(LocalDate date, long days) {
        return date.plusDays(days);
    }

    /**
     * 日期时间增加小时
     * @param dateTime 日期时间
     * @param hours 小时
     * @return 增加后的日期时间
     */
    public static LocalDateTime plusHours(LocalDateTime dateTime, long hours) {
        return dateTime.plusHours(hours);
    }

    /**
     * 日期时间增加分钟
     * @param dateTime 日期时间
     * @param minutes 分钟
     * @return 增加后的日期时间
     */
    public static LocalDateTime plusMinutes(LocalDateTime dateTime, long minutes) {
        return dateTime.plusMinutes(minutes);
    }

    /**
     * 获取当前日期是星期几
     * @param date 日期
     * @return 星期几（1-7，1代表星期一）
     */
    public static int getDayOfWeek(LocalDate date) {
        return date.getDayOfWeek().getValue();
    }

    /**
     * 获取当前月的天数
     * @param date 日期
     * @return 当前月的天数
     */
    public static int getDaysOfMonth(LocalDate date) {
        return date.getMonth().length(date.isLeapYear());
    }

    /**
     * 判断是否是闰年
     * @param year 年份
     * @return 是否是闰年
     */
    public static boolean isLeapYear(int year) {
        return Year.isLeap(year);
    }

    /**
     * 获取一天的开始时间
     * @param date 日期
     * @return 一天的开始时间
     */
    public static LocalDateTime startOfDay(LocalDate date) {
        return date.atStartOfDay();
    }

    /**
     * 获取一天的结束时间
     * @param date 日期
     * @return 一天的结束时间
     */
    public static LocalDateTime endOfDay(LocalDate date) {
        return date.atTime(23, 59, 59, 999999999);
    }
} 