package com.sysgetway.core.common.util;

import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$");
    
    /**
     * 手机号正则表达式
     */
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    
    /**
     * 身份证号正则表达式
     */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$");
    
    /**
     * 生成UUID（不带横线）
     * @return UUID
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成指定长度的随机字符串
     * @param length 长度
     * @return 随机字符串
     */
    public static String generateRandomString(int length) {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = (int) (Math.random() * chars.length());
            sb.append(chars.charAt(index));
        }
        return sb.toString();
    }
    
    /**
     * 字符串脱敏
     * @param str 原始字符串
     * @param startInclude 前几位不脱敏
     * @param endInclude 后几位不脱敏
     * @param maskChar 脱敏字符
     * @return 脱敏后的字符串
     */
    public static String desensitize(String str, int startInclude, int endInclude, char maskChar) {
        if (isBlank(str)) {
            return str;
        }
        
        int length = str.length();
        if (startInclude >= length || startInclude < 0) {
            return str;
        }
        
        if (endInclude >= length || endInclude < 0) {
            return str;
        }
        
        if (startInclude + endInclude >= length) {
            return str;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(str.substring(0, startInclude));
        for (int i = 0; i < length - startInclude - endInclude; i++) {
            sb.append(maskChar);
        }
        sb.append(str.substring(length - endInclude));
        
        return sb.toString();
    }
    
    /**
     * 字符串驼峰转下划线
     * @param str 驼峰字符串
     * @return 下划线字符串
     */
    public static String camelToUnderscore(String str) {
        if (isBlank(str)) {
            return str;
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (Character.isUpperCase(c)) {
                sb.append("_").append(Character.toLowerCase(c));
            } else {
                sb.append(c);
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 字符串下划线转驼峰
     * @param str 下划线字符串
     * @return 驼峰字符串
     */
    public static String underscoreToCamel(String str) {
        if (isBlank(str)) {
            return str;
        }
        
        StringBuilder sb = new StringBuilder();
        boolean upperCase = false;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '_') {
                upperCase = true;
            } else {
                if (upperCase) {
                    sb.append(Character.toUpperCase(c));
                    upperCase = false;
                } else {
                    sb.append(c);
                }
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 字符串下划线转驼峰（首字母大写）
     * @param str 下划线字符串
     * @return 驼峰字符串（首字母大写）
     */
    public static String underscoreToPascal(String str) {
        if (isBlank(str)) {
            return str;
        }
        
        String camel = underscoreToCamel(str);
        return camel.substring(0, 1).toUpperCase() + camel.substring(1);
    }
    
    /**
     * 验证邮箱
     * @param email 邮箱
     * @return 是否合法
     */
    public static boolean isEmail(String email) {
        if (isBlank(email)) {
            return false;
        }
        
        Matcher matcher = EMAIL_PATTERN.matcher(email);
        return matcher.matches();
    }
    
    /**
     * 验证手机号
     * @param mobile 手机号
     * @return 是否合法
     */
    public static boolean isMobile(String mobile) {
        if (isBlank(mobile)) {
            return false;
        }
        
        Matcher matcher = MOBILE_PATTERN.matcher(mobile);
        return matcher.matches();
    }
    
    /**
     * 验证身份证号
     * @param idCard 身份证号
     * @return 是否合法
     */
    public static boolean isIdCard(String idCard) {
        if (isBlank(idCard)) {
            return false;
        }
        
        Matcher matcher = ID_CARD_PATTERN.matcher(idCard);
        return matcher.matches();
    }
    
    /**
     * 字符串转换为整数，转换失败返回默认值
     * @param str 字符串
     * @param defaultValue 默认值
     * @return 整数
     */
    public static int toInt(String str, int defaultValue) {
        if (isBlank(str)) {
            return defaultValue;
        }
        
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 字符串转换为长整数，转换失败返回默认值
     * @param str 字符串
     * @param defaultValue 默认值
     * @return 长整数
     */
    public static long toLong(String str, long defaultValue) {
        if (isBlank(str)) {
            return defaultValue;
        }
        
        try {
            return Long.parseLong(str);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 字符串转换为双精度浮点数，转换失败返回默认值
     * @param str 字符串
     * @param defaultValue 默认值
     * @return 双精度浮点数
     */
    public static double toDouble(String str, double defaultValue) {
        if (isBlank(str)) {
            return defaultValue;
        }
        
        try {
            return Double.parseDouble(str);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 字符串转换为布尔值，转换失败返回默认值
     * @param str 字符串
     * @param defaultValue 默认值
     * @return 布尔值
     */
    public static boolean toBoolean(String str, boolean defaultValue) {
        if (isBlank(str)) {
            return defaultValue;
        }
        
        try {
            return Boolean.parseBoolean(str);
        } catch (Exception e) {
            return defaultValue;
        }
    }
} 