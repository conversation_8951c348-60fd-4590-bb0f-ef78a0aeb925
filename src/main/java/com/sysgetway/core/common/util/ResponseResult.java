package com.sysgetway.core.common.util;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sysgetway.core.common.constant.ResultCode;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一响应结果封装类
 * @param <T> 数据类型
 */
@Data
public class ResponseResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 时间戳
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;

    /**
     * 私有构造函数
     */
    private ResponseResult() {
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 构造函数
     * @param code 状态码
     * @param message 消息
     */
    private ResponseResult(Integer code, String message) {
        this.code = code;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 构造函数
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     */
    private ResponseResult(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 成功
     * @return 响应结果
     */
    public static <T> ResponseResult<T> success() {
        return new ResponseResult<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage());
    }

    /**
     * 成功，带消息
     * @param message 消息
     * @return 响应结果
     */
    public static <T> ResponseResult<T> success(String message) {
        return new ResponseResult<>(ResultCode.SUCCESS.getCode(), message);
    }

    /**
     * 成功，带数据
     * @param data 数据
     * @return 响应结果
     */
    public static <T> ResponseResult<T> success(T data) {
        return new ResponseResult<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }

    /**
     * 成功，带消息和数据
     * @param message 消息
     * @param data 数据
     * @return 响应结果
     */
    public static <T> ResponseResult<T> success(String message, T data) {
        return new ResponseResult<>(ResultCode.SUCCESS.getCode(), message, data);
    }

    /**
     * 失败
     * @return 响应结果
     */
    public static <T> ResponseResult<T> error() {
        return new ResponseResult<>(ResultCode.ERROR.getCode(), ResultCode.ERROR.getMessage());
    }

    /**
     * 失败，带消息
     * @param message 消息
     * @return 响应结果
     */
    public static <T> ResponseResult<T> error(String message) {
        return new ResponseResult<>(ResultCode.ERROR.getCode(), message);
    }

    /**
     * 失败，带状态码和消息
     * @param code 状态码
     * @param message 消息
     * @return 响应结果
     */
    public static <T> ResponseResult<T> error(Integer code, String message) {
        return new ResponseResult<>(code, message);
    }

    /**
     * 失败，带状态码、消息和数据
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     * @return 响应结果
     */
    public static <T> ResponseResult<T> error(Integer code, String message, T data) {
        return new ResponseResult<>(code, message, data);
    }

    /**
     * 根据结果码构建响应结果
     * @param resultCode 结果码
     * @return 响应结果
     */
    public static <T> ResponseResult<T> build(ResultCode resultCode) {
        return new ResponseResult<>(resultCode.getCode(), resultCode.getMessage());
    }

    /**
     * 根据结果码和数据构建响应结果
     * @param resultCode 结果码
     * @param data 数据
     * @return 响应结果
     */
    public static <T> ResponseResult<T> build(ResultCode resultCode, T data) {
        return new ResponseResult<>(resultCode.getCode(), resultCode.getMessage(), data);
    }
} 