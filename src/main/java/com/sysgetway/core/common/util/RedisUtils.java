package com.sysgetway.core.common.util;

import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 */
@Component
public class RedisUtils {
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 设置缓存
     *
     * @param key   键
     * @param value 值
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }
    
    /**
     * 设置缓存并设置过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 过期时间(秒)
     */
    public void set(String key, Object value, long timeout) {
        redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
    }
    
    /**
     * 获取缓存
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }
    
    /**
     * 删除缓存
     *
     * @param key 键
     * @return 是否成功
     */
    public boolean delete(String key) {
        return Boolean.TRUE.equals(redisTemplate.delete(key));
    }
    
    /**
     * 批量删除缓存
     *
     * @param keys 键集合
     * @return 删除的数量
     */
    public long deleteMulti(List<String> keys) {
        return redisTemplate.delete(keys);
    }
    
    /**
     * 设置过期时间
     *
     * @param key     键
     * @param timeout 过期时间(秒)
     * @return 是否成功
     */
    public boolean expire(String key, long timeout) {
        return Boolean.TRUE.equals(redisTemplate.expire(key, timeout, TimeUnit.SECONDS));
    }
    
    /**
     * 判断缓存是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public boolean hasKey(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }
    
    /**
     * 获取符合模式的所有key
     *
     * @param pattern 模式
     * @return key集合
     */
    public Set<String> keys(String pattern) {
        return redisTemplate.keys(pattern);
    }
    
    /**
     * 添加Hash缓存
     *
     * @param key     键
     * @param hashKey hash键
     * @param value   值
     */
    public void hSet(String key, String hashKey, Object value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }
    
    /**
     * 批量添加Hash缓存
     *
     * @param key  键
     * @param map  要添加的hash集合
     */
    public void hSetAll(String key, Map<String, Object> map) {
        redisTemplate.opsForHash().putAll(key, map);
    }
    
    /**
     * 使用管道批量添加Hash缓存（高性能版本）
     * 适用于大量数据写入场景
     *
     * @param key  键
     * @param map  要添加的hash集合
     */
    public void hSetAllPipeline(String key, Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return;
        }
        
        // 获取序列化器
        final RedisSerializer<String> stringSerializer = (RedisSerializer<String>) redisTemplate.getKeySerializer();
        final RedisSerializer<Object> valueSerializer = (RedisSerializer<Object>) redisTemplate.getValueSerializer();
        
        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            try {
                byte[] keyBytes = stringSerializer.serialize(key);
                
                // 使用管道批量执行命令
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    byte[] field = stringSerializer.serialize(entry.getKey());
                    byte[] value = valueSerializer.serialize(entry.getValue());
                    connection.hSet(keyBytes, field, value);
                }
            } catch (Exception e) {
                throw new RuntimeException("Redis管道操作异常", e);
            }
            return null;
        });
    }
    
    /**
     * 获取Hash缓存
     *
     * @param key     键
     * @param hashKey hash键
     * @return 值
     */
    public Object hGet(String key, String hashKey) {
        return redisTemplate.opsForHash().get(key, hashKey);
    }
    
    /**
     * 获取Hash所有键值
     *
     * @param key 键
     * @return hash对象
     */
    public Map<Object, Object> hGetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }
    
    /**
     * 检查Hash中是否存在指定的字段
     *
     * @param key     键
     * @param hashKey hash键
     * @return 是否存在
     */
    public boolean hExists(String key, String hashKey) {
        return Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, hashKey));
    }
    
    /**
     * 使用管道批量检查Hash中字段是否存在
     *
     * @param key      键
     * @param hashKeys hash键列表
     * @return 存在的字段列表
     */
    @SuppressWarnings("unchecked")
    public List<Boolean> hExistsPipeline(String key, List<String> hashKeys) {
        if (hashKeys == null || hashKeys.isEmpty()) {
            return Arrays.asList();
        }
        
        // 获取序列化器
        final RedisSerializer<String> stringSerializer = (RedisSerializer<String>) redisTemplate.getKeySerializer();
        
        List<Object> results = redisTemplate.executePipelined((RedisCallback<Boolean>) connection -> {
            try {
                byte[] keyBytes = stringSerializer.serialize(key);
                
                // 使用管道批量执行命令
                for (String hashKey : hashKeys) {
                    byte[] field = stringSerializer.serialize(hashKey);
                    connection.hExists(keyBytes, field);
                }
            } catch (Exception e) {
                throw new RuntimeException("Redis管道操作异常", e);
            }
            return null;
        });
        
        return (List<Boolean>) (Object) results;
    }
    
    /**
     * 删除Hash中的值
     *
     * @param key      键
     * @param hashKeys hash键集合
     * @return 删除的数量
     */
    public long hDelete(String key, Object... hashKeys) {
        return redisTemplate.opsForHash().delete(key, hashKeys);
    }
    
    /**
     * 执行Lua脚本
     *
     * @param script Lua脚本
     * @param keys   键列表
     * @param args   参数列表
     * @return 执行结果
     */
    public Object executeLuaScript(String script, List<String> keys, Object... args) {
        DefaultRedisScript<Object> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(script);
        return redisTemplate.execute(redisScript, keys, args);
    }
} 