package com.sysgetway.core.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 异步任务管理器
 * 用于管理异步任务的生命周期，提供任务注册、获取、删除等功能
 */
@Component
@Slf4j
public class AsyncTaskManager {

    // 用于存储运行中的任务
    private final Map<String, CompletableFuture<?>> runningTasks = new ConcurrentHashMap<>();
    
    // 用于记录任务的重试次数
    private final Map<String, AtomicInteger> retryCounters = new ConcurrentHashMap<>();
    
    // 用于记录任务的创建时间
    private final Map<String, Long> taskStartTimes = new ConcurrentHashMap<>();
    
    // 默认任务超时时间（毫秒）
    private static final long DEFAULT_TASK_TIMEOUT = 30 * 60 * 1000; // 30分钟
    
    /**
     * 注册任务
     *
     * @param taskKey 任务键
     * @param future 任务Future
     * @param <T> 任务结果类型
     * @return 注册的Future
     */
    public <T> CompletableFuture<T> registerTask(String taskKey, CompletableFuture<T> future) {
        log.debug("注册任务: {}", taskKey);
        runningTasks.put(taskKey, future);
        taskStartTimes.put(taskKey, System.currentTimeMillis());
        
        // 添加任务完成后的清理操作
        future.whenComplete((result, ex) -> {
            if (ex != null) {
                log.error("任务执行异常: {}, 异常: {}", taskKey, ex.getMessage(), ex);
            } else {
                log.debug("任务执行完成: {}", taskKey);
            }
            // 无论成功还是失败，都清理任务
            removeTask(taskKey);
        });
        
        return future;
    }
    
    /**
     * 获取任务
     *
     * @param taskKey 任务键
     * @param <T> 任务结果类型
     * @return 任务Future，如果不存在则返回null
     */
    @SuppressWarnings("unchecked")
    public <T> CompletableFuture<T> getTask(String taskKey) {
        return (CompletableFuture<T>) runningTasks.get(taskKey);
    }
    
    /**
     * 检查任务是否存在
     *
     * @param taskKey 任务键
     * @return 是否存在
     */
    public boolean hasTask(String taskKey) {
        return runningTasks.containsKey(taskKey);
    }
    
    /**
     * 移除任务
     *
     * @param taskKey 任务键
     * @return 被移除的任务Future，如果不存在则返回null
     */
    public CompletableFuture<?> removeTask(String taskKey) {
        log.debug("移除任务: {}", taskKey);
        CompletableFuture<?> future = runningTasks.remove(taskKey);
        retryCounters.remove(taskKey);
        taskStartTimes.remove(taskKey);
        return future;
    }
    
    /**
     * 增加任务重试次数
     *
     * @param taskKey 任务键
     * @return 当前重试次数
     */
    public int incrementRetryCount(String taskKey) {
        return retryCounters.computeIfAbsent(taskKey, k -> new AtomicInteger(0)).incrementAndGet();
    }
    
    /**
     * 获取任务重试次数
     *
     * @param taskKey 任务键
     * @return 当前重试次数
     */
    public int getRetryCount(String taskKey) {
        AtomicInteger counter = retryCounters.get(taskKey);
        return counter != null ? counter.get() : 0;
    }
    
    /**
     * 检查任务是否有重试记录
     *
     * @param taskKey 任务键
     * @return 是否有重试记录
     */
    public boolean hasRetryCount(String taskKey) {
        return retryCounters.containsKey(taskKey);
    }
    
    /**
     * 移除任务的重试记录
     *
     * @param taskKey 任务键
     * @return 被移除的重试计数器，如果不存在则返回null
     */
    public AtomicInteger removeRetryCount(String taskKey) {
        log.debug("移除任务重试记录: {}", taskKey);
        return retryCounters.remove(taskKey);
    }
    
    /**
     * 检查任务是否超时
     *
     * @param taskKey 任务键
     * @return 是否超时
     */
    public boolean isTaskTimedOut(String taskKey) {
        Long startTime = taskStartTimes.get(taskKey);
        if (startTime == null) {
            return false;
        }
        long elapsedTime = System.currentTimeMillis() - startTime;
        return elapsedTime > DEFAULT_TASK_TIMEOUT;
    }
    
    /**
     * 获取任务数量
     *
     * @return 当前任务数量
     */
    public int getTaskCount() {
        return runningTasks.size();
    }
    
    /**
     * 获取所有任务键
     *
     * @return 任务键集合
     */
    public Iterable<String> getAllTaskKeys() {
        return runningTasks.keySet();
    }
    
    /**
     * 清理所有任务
     */
    public void clearAllTasks() {
        log.info("清理所有任务，共{}个", runningTasks.size());
        runningTasks.clear();
        retryCounters.clear();
        taskStartTimes.clear();
    }
    
    /**
     * 清理所有任务（兼容旧方法名）
     */
    public void clearTasks() {
        clearAllTasks();
    }
    
    /**
     * 清理所有重试记录
     */
    public void clearRetryCounts() {
        log.info("清理所有重试记录，共{}个", retryCounters.size());
        retryCounters.clear();
    }
    
    /**
     * 应用关闭前清理资源
     */
    @PreDestroy
    public void destroy() {
        log.info("应用关闭，清理所有任务资源");
        
        // 尝试取消所有未完成的任务
        runningTasks.forEach((key, future) -> {
            if (!future.isDone()) {
                log.warn("取消未完成的任务: {}", key);
                future.cancel(true);
            }
        });
        
        // 等待所有任务完成
        try {
            boolean allDone = false;
            for (int i = 0; i < 10 && !allDone; i++) {
                allDone = true;
                for (CompletableFuture<?> future : runningTasks.values()) {
                    if (!future.isDone()) {
                        allDone = false;
                        break;
                    }
                }
                if (!allDone) {
                    TimeUnit.MILLISECONDS.sleep(500);
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("等待任务完成被中断", e);
        }
        
        // 清理资源
        clearAllTasks();
    }
} 