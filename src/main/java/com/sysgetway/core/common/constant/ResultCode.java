package com.sysgetway.core.common.constant;

import lombok.Getter;

/**
 * 统一响应状态码
 */
@Getter
public enum ResultCode {

    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 系统错误
     */
    ERROR(500, "系统错误"),

    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),

    /**
     * 未认证
     */
    UNAUTHORIZED(401, "未认证"),

    /**
     * 未授权
     */
    FORBIDDEN(403, "未授权"),

    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),

    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),

    /**
     * 请求超时
     */
    REQUEST_TIMEOUT(408, "请求超时"),

    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    /**
     * 数据库操作失败
     */
    DB_ERROR(5001, "数据库操作失败"),

    /**
     * 重复操作
     */
    DUPLICATE_OPERATION(5002, "重复操作"),

    /**
     * 远程调用失败
     */
    REMOTE_CALL_ERROR(5003, "远程调用失败"),

    /**
     * 数据不存在
     */
    DATA_NOT_EXIST(5004, "数据不存在"),

    /**
     * 数据已存在
     */
    DATA_ALREADY_EXIST(5005, "数据已存在"),

    /**
     * 参数验证失败
     */
    VALIDATION_ERROR(5006, "参数验证失败"),

    /**
     * 业务处理失败
     */
    BUSINESS_ERROR(5007, "业务处理失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 消息
     */
    private final String message;

    /**
     * 构造方法
     * @param code 状态码
     * @param message 消息
     */
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
} 