package com.sysgetway.core.common.constant;

/**
 * 同步相关常量定义
 */
public class SyncConstants {

    /**
     * 表名常量
     */
    public static class TableName {
        /**
         * 科室表
         */
        public static final String DEPARTMENT = "department";
        
        /**
         * 医护人员表
         */
        public static final String USER = "user";
        
        /**
         * 患者表
         */
        public static final String PATIENT = "patient";
        
        /**
         * 判断表名是否有效
         */
        public static boolean isValid(String tableName) {
            return DEPARTMENT.equals(tableName) || USER.equals(tableName) || PATIENT.equals(tableName);
        }
    }
    
    /**
     * 患者类型常量
     */
    public static class PatientType {
        /**
         * 入院患者
         */
        public static final String IN = "in";
        
        /**
         * 在院患者
         */
        public static final String UP = "up";
        
        /**
         * 出院患者
         */
        public static final String OUT = "out";
        
        /**
         * 自动分类（根据患者状态自动分类为UP或OUT）
         */
        public static final String AUTO = "auto";
        
        /**
         * 判断患者类型是否有效
         */
        public static boolean isValid(String patientType) {
            return IN.equals(patientType) || UP.equals(patientType) || OUT.equals(patientType) || AUTO.equals(patientType);
        }
    }
    
    /**
     * Redis键前缀常量
     */
    public static class RedisKeyPrefix {
        /**
         * 医院科室数据前缀
         */
        public static final String HOSPITAL_DEPARTMENT = "hospital:department:";

        /**
         * 医院医护人员数据前缀
         */
        public static final String HOSPITAL_USER = "hospital:user:";

        /**
         * 医院患者数据前缀
         */
        public static final String HOSPITAL_PATIENT = "hospital:patient:";

        /**
         * 医院患者类型数据前缀（旧格式，保留兼容性）
         */
        public static final String HOSPITAL_PATIENT_TYPE = "hospital:patient:type:";

        /**
         * 入院患者数据前缀（新格式）
         */
        public static final String IN_PATIENT = "InPatient-";

        /**
         * 在院患者数据前缀（新格式）
         */
        public static final String UP_DATA = "UpData-";

        /**
         * 出院患者数据前缀（新格式）
         */
        public static final String OUT_PATIENT = "OutPatient-";

        /**
         * 最后同步时间
         */
        public static final String SYNC_LAST_TIME = "hospital:%s:sync:last_time:%s";

        /**
         * 同步状态
         */
        public static final String SYNC_STATUS = "hospital:%s:sync:status:%s";

        /**
         * 任务状态，通过任务ID查询
         */
        public static final String SYNC_TASK_STATUS = "sync:task:%s";

        /**
         * 根据患者类型获取Redis键前缀
         * @param patientType 患者类型
         * @return Redis键前缀
         */
        public static String getPatientKeyPrefix(String patientType) {
            switch (patientType) {
                case PatientType.IN:
                    return IN_PATIENT;
                case PatientType.UP:
                    return UP_DATA;
                case PatientType.OUT:
                    return OUT_PATIENT;
                default:
                    return HOSPITAL_PATIENT_TYPE;
            }
        }
    }
    
    /**
     * 同步类型常量
     */
    public static class SyncType {
        /**
         * 全量同步
         */
        public static final String FULL = "full";
        
        /**
         * 增量同步
         */
        public static final String INCREMENTAL = "incremental";
    }
    
    /**
     * 同步状态常量
     */
    public static class SyncStatus {
        /**
         * 同步成功
         */
        public static final int SUCCESS = 1;
        
        /**
         * 同步进行中
         */
        public static final int IN_PROGRESS = 0;
        
        /**
         * 同步失败
         */
        public static final int FAILED = -1;
    }
    
    /**
     * 同步状态消息常量
     */
    public static class SyncMessage {
        /**
         * 同步成功
         */
        public static final String SUCCESS = "同步成功";
        
        /**
         * 同步进行中
         */
        public static final String IN_PROGRESS = "同步进行中";
        
        /**
         * 同步失败
         */
        public static final String FAILED = "同步失败";
        
        /**
         * 数据为空
         */
        public static final String NO_DATA = "没有数据需要同步";
    }
    
    /**
     * Redis过期时间常量(秒)
     */
    public static class ExpireTime {
        /**
         * 默认过期时间(24小时)
         */
        public static final long DEFAULT = 86400;
        
        /**
         * 同步状态过期时间(7天)
         */
        public static final long SYNC_STATUS = 604800;
        
        /**
         * 最后同步时间不过期(0表示永不过期)
         */
        public static final long LAST_SYNC_TIME = 0;
    }
} 