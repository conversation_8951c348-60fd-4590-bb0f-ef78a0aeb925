package com.sysgetway.core.common.exception;

import com.sysgetway.core.common.constant.ResultCode;
import com.sysgetway.core.common.util.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseResult<String> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.error("业务异常: {}, 请求URI: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseResult.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理参数校验异常（@Valid注解）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseResult<List<String>> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        BindingResult bindingResult = e.getBindingResult();
        List<String> errorMessages = bindingResult.getFieldErrors()
                .stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.toList());
        
        log.error("参数校验异常: {}, 请求URI: {}", errorMessages, request.getRequestURI(), e);
        return ResponseResult.error(ResultCode.PARAM_ERROR.getCode(), "参数校验失败", errorMessages);
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseResult<List<String>> handleBindException(BindException e, HttpServletRequest request) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        List<String> errorMessages = new ArrayList<>(fieldErrors.size());
        for (FieldError error : fieldErrors) {
            errorMessages.add(error.getField() + ": " + error.getDefaultMessage());
        }
        log.error("参数绑定异常: {}, 请求URI: {}", errorMessages, request.getRequestURI(), e);
        return ResponseResult.error(ResultCode.PARAM_ERROR.getCode(), "参数绑定失败", errorMessages);
    }

    /**
     * 处理参数校验异常（@Validated注解）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseResult<List<String>> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        List<String> errorMessages = e.getConstraintViolations()
                .stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.toList());
        log.error("参数验证异常: {}, 请求URI: {}", errorMessages, request.getRequestURI(), e);
        return ResponseResult.error(ResultCode.PARAM_ERROR.getCode(), "参数验证失败", errorMessages);
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseResult<String> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        log.error("参数类型不匹配: {}, 请求URI: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseResult.error(ResultCode.PARAM_ERROR.getCode(), "参数类型不匹配: " + e.getName());
    }

    /**
     * 处理请求体解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseResult<String> handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request) {
        log.error("请求体解析异常: {}, 请求URI: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseResult.error(ResultCode.PARAM_ERROR.getCode(), "请求体解析失败");
    }

    /**
     * 处理接口不存在异常
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseResult<String> handleNoHandlerFoundException(NoHandlerFoundException e, HttpServletRequest request) {
        log.error("接口不存在: {}, 请求URI: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseResult.error(ResultCode.NOT_FOUND.getCode(), "接口不存在: " + request.getRequestURI());
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ResponseResult<String> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        log.error("请求方法不支持: {}, 请求URI: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseResult.error(ResultCode.METHOD_NOT_ALLOWED.getCode(), "请求方法不支持: " + e.getMethod());
    }

    /**
     * 处理媒体类型不支持异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public ResponseResult<String> handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e, HttpServletRequest request) {
        log.error("媒体类型不支持: {}, 请求URI: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseResult.error(415, "媒体类型不支持: " + e.getContentType());
    }

    /**
     * 处理数据库相关异常
     */
    @ExceptionHandler({SQLException.class, DataAccessException.class})
    public ResponseResult<String> handleSQLException(Exception e, HttpServletRequest request) {
        log.error("数据库操作异常: {}, 请求URI: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseResult.error(ResultCode.DB_ERROR.getCode(), "数据库操作异常");
    }

    /**
     * 处理未知异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseResult<String> handleException(Exception e, HttpServletRequest request) {
        log.error("系统未知异常: {}, 请求URI: {}", e.getMessage(), request.getRequestURI(), e);
        return ResponseResult.error(ResultCode.ERROR.getCode(), "系统繁忙，请稍后重试");
    }
} 