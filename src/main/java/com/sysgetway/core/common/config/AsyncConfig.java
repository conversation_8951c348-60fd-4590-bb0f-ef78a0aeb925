package com.sysgetway.core.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务线程池配置
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig {

    @Value("${sysgetway.thread-pool.core-pool-size:10}")
    private int corePoolSize;

    @Value("${sysgetway.thread-pool.max-pool-size:20}")
    private int maxPoolSize;

    @Value("${sysgetway.thread-pool.queue-capacity:100}")
    private int queueCapacity;

    @Value("${sysgetway.thread-pool.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    @Value("${sysgetway.thread-pool.thread-name-prefix:sysgetway-task-}")
    private String threadNamePrefix;

    /**
     * 配置异步任务执行器
     *
     * @return 线程池执行器
     */
    @Bean("syncTaskExecutor")
    public Executor syncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 设置核心线程数
        executor.setCorePoolSize(corePoolSize);
        // 设置最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        // 设置队列容量
        executor.setQueueCapacity(queueCapacity);
        // 设置线程活跃时间（秒）
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 设置默认线程名称
        executor.setThreadNamePrefix(threadNamePrefix);
        
        // 设置拒绝策略：当线程池已满且队列已满时，使用调用者所在的线程执行任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 设置线程池关闭的超时时间
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化线程池
        executor.initialize();
        
        log.info("创建自定义线程池 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}, 线程前缀: {}", 
                corePoolSize, maxPoolSize, queueCapacity, threadNamePrefix);
        
        return executor;
    }
} 