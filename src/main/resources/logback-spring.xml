<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- 定义日志文件的存储地址 -->
    <property name="log.path" value="logs"/>
    <!-- 定义日志格式 -->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"/>
    
    <!-- 彩色日志格式（控制台） -->
    <property name="log.pattern.color" value="%yellow(%d{yyyy-MM-dd HH:mm:ss.SSS}) %highlight(%-5level) [%blue(%thread)] %cyan(%logger{50}) - %magenta(%msg%n)"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern.color}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 系统日志文件 -->
    <appender name="SYSTEM_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志文件名 -->
        <file>${log.path}/system.log</file>
        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 日志文件输出的文件名 -->
            <fileNamePattern>${log.path}/system.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个日志文件最大大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 日志文件保留天数 -->
            <maxHistory>15</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 只记录ERROR级别日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 业务日志文件 -->
    <appender name="BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/business.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/business.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 定义日志脱敏处理器 -->
    <conversionRule conversionWord="msg" 
                    converterClass="com.sysgetway.core.common.util.SensitiveDataConverter" />
    
    <!-- 项目包的日志级别 -->
    <logger name="com.sysgetway.core" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="SYSTEM_FILE" />
        <appender-ref ref="ERROR_FILE" />
    </logger>
    
    <!-- 业务日志单独配置 -->
    <logger name="com.sysgetway.core.service" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="BUSINESS_FILE" />
        <appender-ref ref="ERROR_FILE" />
    </logger>
    
    <!-- MyBatis SQL日志（开发环境使用） -->
    <logger name="com.sysgetway.core.mapper" level="DEBUG" />
    
    <!-- Spring框架日志 -->
    <logger name="org.springframework" level="WARN" />
    
    <!-- 设置默认日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="SYSTEM_FILE" />
        <appender-ref ref="ERROR_FILE" />
    </root>
    
    <!-- 多环境日志配置 -->
    <springProfile name="dev">
        <root level="INFO" />
        <logger name="com.sysgetway.core" level="DEBUG" />
        <logger name="com.sysgetway.core.mapper" level="DEBUG" />
    </springProfile>
    
    <springProfile name="test">
        <root level="INFO" />
        <logger name="com.sysgetway.core" level="DEBUG" />
    </springProfile>
    
    <springProfile name="prod">
        <root level="WARN" />
        <logger name="com.sysgetway.core" level="INFO" />
        <logger name="com.sysgetway.core.mapper" level="WARN" />
    </springProfile>
</configuration> 