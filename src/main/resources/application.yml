server:
  port: 9000
  servlet:
    context-path: /sysgetway
    encoding:
      charset: UTF-8
      force: true
      enabled: true

spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  application:
    name: sysgetway
  profiles:
    active: dev
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    static-path-pattern: /static/**


# Knife4j配置
knife4j:
  enable: true
  basic:
    enable: false
  setting:
    language: zh-CN
    enable-swagger-models: true
    swagger-model-name: 实体类列表

# 日志配置
logging:
  config: classpath:logback-spring.xml
