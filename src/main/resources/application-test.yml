spring:
  # Redis配置（测试环境使用本地Redis或嵌入式Redis）
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0

# 自定义配置
sysgetway:
  # 跨域配置
  cors:
    enabled: true
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization,Content-Type"
    allow-credentials: true
    max-age: 3600
  
  # 线程池配置
  thread-pool:
    core-pool-size: 10
    max-pool-size: 20
    queue-capacity: 100
    keep-alive-seconds: 60
    thread-name-prefix: sysgetway-task-

