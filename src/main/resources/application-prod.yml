spring:
  # Redis配置
  redis:
    host: ${REDIS_HOST:prod-redis}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 16
        max-wait: -1
        max-idle: 16
        min-idle: 8

# 自定义配置
sysgetway:
  # 跨域配置
  cors:
    enabled: true
    allowed-origins: "${CORS_ALLOWED_ORIGINS:https://admin.example.com}"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization,Content-Type"
    allow-credentials: true
    max-age: 3600
  
  # 线程池配置
  thread-pool:
    core-pool-size: 20
    max-pool-size: 50
    queue-capacity: 200
    keep-alive-seconds: 60
    thread-name-prefix: sysgetway-task-



# 生产环境禁用Knife4j
knife4j:
  production: true 