package com.sysgetway.core.utils;

import com.sysgetway.core.entity.Department;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.entity.User;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 医院数据生成器
 * 
 * 用于生成符合《数据视图结构说明》的医院测试数据，
 * 包括科室、医护人员和患者数据，并确保数据之间的关联关系正确
 */
public class HospitalDataGenerator {
    
    private static final Random random = new Random();
    
    /**
     * 生成完整的医院数据集，包括科室、医护人员和患者数据
     * 并保证数据之间的引用关系正确
     * 
     * @param departmentCount 科室数量
     * @param userCount 医护人员数量
     * @param patientCount 患者数量
     * @return 医院数据Map，包含departments, users, patients三个key
     */
    public static Map<String, Object> generateCompleteHospitalData(int departmentCount, int userCount, int patientCount) {
        Map<String, Object> result = new HashMap<>();
        
        // 1. 生成科室数据
        List<Department> departments = generateDepartments(departmentCount);
        result.put("departments", departments);
        
        // 2. 生成医护人员数据，关联到科室
        List<User> users = generateUsers(userCount, departments);
        result.put("users", users);
        
        // 3. 生成患者数据，关联到科室和医护人员
        List<Patient> patients = generatePatients(patientCount, departments, users);
        result.put("patients", patients);
        
        return result;
    }
    
    /**
     * 生成科室数据
     * 
     * @param count 科室数量
     * @return 科室列表
     */
    public static List<Department> generateDepartments(int count) {
        List<Department> departments = new ArrayList<>();
        
        // 预设科室列表
        String[][] predefinedDepartments = {
                {"NEU", "神经科"},
                {"CAR", "心血管科"},
                {"PED", "儿科"},
                {"ORT", "骨科"},
                {"DER", "皮肤科"},
                {"RES", "呼吸科"},
                {"GAS", "消化科"},
                {"ENT", "耳鼻喉科"},
                {"OPH", "眼科"},
                {"URO", "泌尿外科"},
                {"GYN", "妇科"},
                {"OBS", "产科"},
                {"PSY", "精神科"},
                {"RAD", "放射科"},
                {"HEM", "血液科"}
        };
        
        // 生成科室数据
        int actualCount = Math.min(count, predefinedDepartments.length);
        for (int i = 0; i < actualCount; i++) {
            Department dept = new Department();
            dept.setDepartmentId(i + 1);
            dept.setCode(predefinedDepartments[i][0]);
            dept.setName(predefinedDepartments[i][1]);
            dept.setUpdatedAt(randomDateTime(30)); // 最近30天内的时间
            departments.add(dept);
        }
        
        return departments;
    }
    
    /**
     * 生成医护人员数据
     * 
     * @param count 医护人员数量
     * @param departments 关联的科室列表
     * @return 医护人员列表
     */
    public static List<User> generateUsers(int count, List<Department> departments) {
        List<User> users = new ArrayList<>();
        
        // 确保每个科室至少有一个医生和一个护士
        if (departments != null && !departments.isEmpty()) {
            for (Department dept : departments) {
                // 为科室添加一个医生
                User doctor = new User();
                doctor.setUserName("D" + String.format("%03d", dept.getDepartmentId()));
                doctor.setName(generateChineseName() + "医");
                doctor.setSex(random.nextInt(2) + 1); // 1或2
                doctor.setRoleId(1); // 医生
                doctor.setDeptId(dept.getDepartmentId());
                doctor.setInpatientWard(dept.getDepartmentId() + "病区");
                doctor.setMobile("138" + String.format("%08d", random.nextInt(100000000)));
                doctor.setUpdatedAt(randomDateTime(15));
                users.add(doctor);
                
                // 为科室添加一个护士
                User nurse = new User();
                nurse.setUserName("N" + String.format("%03d", dept.getDepartmentId()));
                nurse.setName(generateChineseName() + "护");
                nurse.setSex(random.nextInt(2) + 1); // 1或2
                nurse.setRoleId(2); // 护士
                nurse.setDeptId(dept.getDepartmentId());
                nurse.setInpatientWard(dept.getDepartmentId() + "病区," + (dept.getDepartmentId() + 1) + "病区");
                nurse.setMobile("139" + String.format("%08d", random.nextInt(100000000)));
                nurse.setUpdatedAt(randomDateTime(15));
                users.add(nurse);
            }
        }
        
        // 如果需要更多医护人员，继续添加
        int additionalUsersNeeded = count - (departments.size() * 2);
        if (additionalUsersNeeded > 0 && !departments.isEmpty()) {
            for (int i = 0; i < additionalUsersNeeded; i++) {
                User user = new User();
                boolean isDoctor = i % 2 == 0;
                int deptIndex = random.nextInt(departments.size());
                Department dept = departments.get(deptIndex);
                
                user.setUserName((isDoctor ? "D" : "N") + String.format("%03d", 100 + i));
                user.setName(generateChineseName() + (isDoctor ? "医" : "护"));
                user.setSex(random.nextInt(2) + 1); // 1或2
                user.setRoleId(isDoctor ? 1 : 2); // 1:医生, 2:护士
                user.setDeptId(dept.getDepartmentId());
                
                // 设置病区，护士可能管理多个病区
                if (isDoctor) {
                    user.setInpatientWard(dept.getDepartmentId() + "病区");
                } else {
                    user.setInpatientWard(dept.getDepartmentId() + "病区," + 
                            ((dept.getDepartmentId() % departments.size()) + 1) + "病区");
                }
                
                user.setMobile((isDoctor ? "138" : "139") + String.format("%08d", random.nextInt(100000000)));
                user.setUpdatedAt(randomDateTime(15));
                users.add(user);
            }
        }
        
        return users;
    }
    
    /**
     * 生成患者数据
     * 
     * @param count 患者数量
     * @param departments 关联的科室列表
     * @param users 关联的医护人员列表
     * @return 患者列表
     */
    public static List<Patient> generatePatients(int count, List<Department> departments, List<User> users) {
        List<Patient> patients = new ArrayList<>();
        
        if (departments == null || departments.isEmpty() || users == null || users.isEmpty()) {
            return patients;
        }
        
        // 分组医护人员，便于按科室和角色查询
        Map<Integer, List<User>> doctorsByDept = new HashMap<>();
        Map<Integer, List<User>> nursesByDept = new HashMap<>();
        
        for (User user : users) {
            if (user.getRoleId() == 1) { // 医生
                doctorsByDept.computeIfAbsent(user.getDeptId(), k -> new ArrayList<>()).add(user);
            } else if (user.getRoleId() == 2) { // 护士
                nursesByDept.computeIfAbsent(user.getDeptId(), k -> new ArrayList<>()).add(user);
            }
        }
        
        // 生成患者数据
        for (int i = 0; i < count; i++) {
            Patient patient = new Patient();
            
            // 基本信息
            patient.setInpatientInfoId("P" + String.format("%04d", i + 1));
            patient.setName(generateChineseName());
            patient.setIdCard(generateIdCard());
            patient.setMobile("13" + String.format("%09d", random.nextInt(**********)));
            patient.setSex(random.nextInt(2) + 1); // 1:男，2:女
            
            // 年龄和生日
            int age = 10 + random.nextInt(80); // 10-89岁
            patient.setAge(age);
            patient.setBirthday(LocalDate.now().minusYears(age).minusDays(random.nextInt(365)));
            
            // 住院信息
            patient.setHospitalizationNo("H" + String.format("%04d", 1000 + i));
            
            // 随机选择科室和诊断
            int deptIndex = random.nextInt(departments.size());
            Department dept = departments.get(deptIndex);
            patient.setDeptId(dept.getDepartmentId());
            patient.setInhospitalDiagnose(generateDiagnosis(dept.getName()));
            
            // 床位信息
            patient.setSickbedNo(dept.getDepartmentId() + "-" + String.format("%02d", random.nextInt(20) + 1));
            patient.setInpatientWard(dept.getDepartmentId() + "病区");
            
            // 关联医生和护士
            if (doctorsByDept.containsKey(dept.getDepartmentId()) && !doctorsByDept.get(dept.getDepartmentId()).isEmpty()) {
                User doctor = doctorsByDept.get(dept.getDepartmentId()).get(
                        random.nextInt(doctorsByDept.get(dept.getDepartmentId()).size()));
                patient.setDoctorId(doctor.getUserName());
            }
            
            if (nursesByDept.containsKey(dept.getDepartmentId()) && !nursesByDept.get(dept.getDepartmentId()).isEmpty()) {
                User nurse = nursesByDept.get(dept.getDepartmentId()).get(
                        random.nextInt(nursesByDept.get(dept.getDepartmentId()).size()));
                patient.setNurseId(nurse.getUserName());
            }
            
            // 护理级别
            patient.setNurseLevel(random.nextInt(4) + 1); // 1-4级
            
            // 患者状态和时间
            int patientType = random.nextInt(3); // 0:入院患者, 1:在院患者, 2:出院患者
            LocalDateTime now = LocalDateTime.now();
            
            if (patientType == 0) { // 入院患者（有入院时间，无出院时间）
                patient.setInhospitalTime(now.minusHours(random.nextInt(24) + 1)); // 最近24小时内入院
                patient.setOuthospitalTime(null);
                patient.setStatus(1); // 在院
            } else if (patientType == 1) { // 在院患者
                patient.setInhospitalTime(now.minusDays(random.nextInt(30) + 1)); // 1-30天前入院
                patient.setOuthospitalTime(null);
                patient.setStatus(1); // 在院
            } else { // 出院患者
                LocalDateTime inhospitalTime = now.minusDays(random.nextInt(60) + 30); // 30-90天前入院
                LocalDateTime outhospitalTime = inhospitalTime.plusDays(random.nextInt(20) + 5); // 住院5-25天
                patient.setInhospitalTime(inhospitalTime);
                patient.setOuthospitalTime(outhospitalTime);
                patient.setStatus(2); // 出院
            }
            
            // 患者类别
            patient.setCategory(random.nextInt(10) < 7 ? "医保" : "自费"); // 70%是医保患者
            
            // 更新时间
            patient.setUpdatedAt(LocalDateTime.now().minusHours(random.nextInt(48)));
            
            patients.add(patient);
        }
        
        return patients;
    }
    
    /**
     * 生成随机的中文姓名
     */
    private static String generateChineseName() {
        String[] surnames = {"张", "王", "李", "赵", "刘", "陈", "杨", "黄", "周", "吴", "郑", "孙", "马", "林"};
        String[] names = {"伟", "芳", "娜", "秀英", "敏", "静", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰"};
        
        return surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)];
    }
    
    /**
     * 生成随机的身份证号
     */
    private static String generateIdCard() {
        // 简化实现，实际应用中应考虑校验位
        StringBuilder idCard = new StringBuilder();
        
        // 6位地区码
        idCard.append("4").append(random.nextInt(10)).append(random.nextInt(10))
              .append(random.nextInt(10)).append(random.nextInt(10)).append(random.nextInt(10));
        
        // 8位出生日期（1960-2000年）
        int year = 1960 + random.nextInt(40);
        int month = 1 + random.nextInt(12);
        int day = 1 + random.nextInt(28);
        idCard.append(String.format("%04d%02d%02d", year, month, day));
        
        // 3位顺序码
        idCard.append(String.format("%03d", random.nextInt(1000)));
        
        // 1位校验码（简化处理，实际应根据规则计算）
        idCard.append(random.nextInt(10));
        
        return idCard.toString();
    }
    
    /**
     * 根据科室生成诊断信息
     */
    private static String generateDiagnosis(String deptName) {
        Map<String, String[]> diagnosisByDept = new HashMap<>();
        diagnosisByDept.put("神经科", new String[]{"头痛", "偏头痛", "神经炎", "癫痫", "脑梗塞"});
        diagnosisByDept.put("心血管科", new String[]{"高血压", "冠心病", "心肌梗塞", "心律失常", "心力衰竭"});
        diagnosisByDept.put("儿科", new String[]{"肺炎", "支气管炎", "腹泻", "手足口病", "麻疹"});
        diagnosisByDept.put("骨科", new String[]{"骨折", "腰椎间盘突出", "关节炎", "骨质疏松", "肌腱断裂"});
        diagnosisByDept.put("皮肤科", new String[]{"湿疹", "皮炎", "荨麻疹", "带状疱疹", "银屑病"});
        diagnosisByDept.put("呼吸科", new String[]{"肺炎", "支气管炎", "肺结核", "哮喘", "肺气肿"});
        diagnosisByDept.put("消化科", new String[]{"胃炎", "胃溃疡", "结肠炎", "肝炎", "胰腺炎"});
        diagnosisByDept.put("耳鼻喉科", new String[]{"中耳炎", "鼻窦炎", "扁桃体炎", "咽炎", "喉炎"});
        diagnosisByDept.put("眼科", new String[]{"白内障", "青光眼", "结膜炎", "眼底出血", "视网膜病变"});
        diagnosisByDept.put("泌尿外科", new String[]{"肾结石", "尿路感染", "前列腺炎", "肾功能不全", "膀胱炎"});
        
        if (diagnosisByDept.containsKey(deptName)) {
            String[] diagnoses = diagnosisByDept.get(deptName);
            return diagnoses[random.nextInt(diagnoses.length)];
        } else {
            // 默认诊断
            String[] defaultDiagnoses = {"发热", "头痛", "咳嗽", "腹痛", "胸闷"};
            return defaultDiagnoses[random.nextInt(defaultDiagnoses.length)];
        }
    }
    
    /**
     * 生成指定天数内的随机日期时间
     */
    private static LocalDateTime randomDateTime(int daysRange) {
        return LocalDateTime.now().minusDays(random.nextInt(daysRange))
                .minusHours(random.nextInt(24))
                .minusMinutes(random.nextInt(60));
    }
} 