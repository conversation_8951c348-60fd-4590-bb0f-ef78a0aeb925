package com.sysgetway.core.utils;

import com.sysgetway.core.entity.Department;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.entity.User;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试数据工厂
 * 为单元测试和示例提供测试数据
 * 
 * 注：此类已被HospitalDataGenerator扩展和改进，但保留此类用于兼容现有测试
 */
public class TestDataFactory {

    /**
     * 创建科室数据列表
     * @return 科室列表
     */
    public static List<Department> createDepartmentList() {
        // 使用HospitalDataGenerator生成更符合规范的数据
        return HospitalDataGenerator.generateDepartments(5);
    }
    
    /**
     * 创建医护人员数据列表
     * @return 医护人员列表
     */
    public static List<User> createUserList() {
        // 先生成科室数据，再生成关联的医护人员数据
        List<Department> departments = createDepartmentList();
        return HospitalDataGenerator.generateUsers(10, departments);
    }
    
    /**
     * 创建患者数据列表
     * @return 患者列表
     */
    public static List<Patient> createPatientList() {
        // 生成科室和医护人员数据，再生成关联的患者数据
        List<Department> departments = createDepartmentList();
        List<User> users = HospitalDataGenerator.generateUsers(10, departments);
        return HospitalDataGenerator.generatePatients(15, departments, users);
    }
    
    /**
     * 创建入院患者
     * @return 入院患者
     */
    public static Patient createInPatient() {
        // 从生成的患者中筛选入院患者
        List<Patient> patients = createPatientList();
        for (Patient patient : patients) {
            if (patient.getInhospitalTime() != null && patient.getOuthospitalTime() == null) {
                return patient;
            }
        }
        
        // 如果没有找到，创建一个新的入院患者
        Patient patient = new Patient();
        patient.setInpatientInfoId("P001");
        patient.setName("张三");
        patient.setIdCard("110101199001011234");
        patient.setMobile("13800138000");
        patient.setSex(1);
        patient.setAge(30);
        patient.setBirthday(LocalDate.of(1990, 1, 1));
        patient.setHospitalizationNo("H001");
        patient.setInhospitalDiagnose("高血压");
        patient.setDeptId(1);
        patient.setSickbedNo("1-101");
        patient.setDoctorId("D001");
        patient.setNurseId("N001");
        patient.setNurseLevel(2);
        patient.setInhospitalTime(LocalDateTime.now().minusHours(6));
        patient.setOuthospitalTime(null);
        patient.setStatus(1); // 在院
        patient.setCategory("医保");
        patient.setInpatientWard("1病区");
        patient.setUpdatedAt(LocalDateTime.now());
        return patient;
    }
    
    /**
     * 创建在院患者
     * @return 在院患者
     */
    public static Patient createUpPatient() {
        // 从生成的患者中筛选在院患者
        List<Patient> patients = createPatientList();
        for (Patient patient : patients) {
            if (patient.getStatus() != null && patient.getStatus() == 1) {
                return patient;
            }
        }
        
        // 如果没有找到，创建一个新的在院患者
        Patient patient = new Patient();
        patient.setInpatientInfoId("P002");
        patient.setName("李四");
        patient.setIdCard("110101199002022345");
        patient.setMobile("13800138001");
        patient.setSex(2);
        patient.setAge(28);
        patient.setBirthday(LocalDate.of(1992, 2, 2));
        patient.setHospitalizationNo("H002");
        patient.setInhospitalDiagnose("冠心病");
        patient.setDeptId(2);
        patient.setSickbedNo("2-102");
        patient.setDoctorId("D002");
        patient.setNurseId("N002");
        patient.setNurseLevel(1);
        patient.setInhospitalTime(LocalDateTime.now().minusDays(5));
        patient.setOuthospitalTime(null);
        patient.setStatus(1); // 在院
        patient.setCategory("医保");
        patient.setInpatientWard("2病区");
        patient.setUpdatedAt(LocalDateTime.now());
        return patient;
    }
    
    /**
     * 创建出院患者
     * @return 出院患者
     */
    public static Patient createOutPatient() {
        // 从生成的患者中筛选出院患者
        List<Patient> patients = createPatientList();
        for (Patient patient : patients) {
            if (patient.getStatus() != null && patient.getStatus() == 2) {
                return patient;
            }
        }
        
        // 如果没有找到，创建一个新的出院患者
        Patient patient = new Patient();
        patient.setInpatientInfoId("P003");
        patient.setName("王五");
        patient.setIdCard("110101199003033456");
        patient.setMobile("13800138002");
        patient.setSex(1);
        patient.setAge(45);
        patient.setBirthday(LocalDate.of(1975, 3, 3));
        patient.setHospitalizationNo("H003");
        patient.setInhospitalDiagnose("肺炎");
        patient.setDeptId(3);
        patient.setSickbedNo("3-103");
        patient.setDoctorId("D003");
        patient.setNurseId("N003");
        patient.setNurseLevel(3);
        patient.setInhospitalTime(LocalDateTime.now().minusDays(15));
        patient.setOuthospitalTime(LocalDateTime.now().minusDays(2));
        patient.setStatus(2); // 出院
        patient.setCategory("自费");
        patient.setInpatientWard("3病区");
        patient.setUpdatedAt(LocalDateTime.now());
        return patient;
    }
    
    /**
     * 创建数据不一致的患者（用于测试异常场景）
     * @return 数据不一致的患者
     */
    public static Patient createInconsistentPatient() {
        Patient patient = new Patient();
        patient.setInpatientInfoId("P999");
        patient.setName("异常患者");
        patient.setIdCard("110101199909099999");
        patient.setMobile("13800138999");
        patient.setSex(1);
        patient.setAge(40);
        patient.setBirthday(LocalDate.of(1980, 9, 9));
        patient.setHospitalizationNo("H999");
        patient.setInhospitalDiagnose("测试诊断");
        patient.setDeptId(1);
        patient.setSickbedNo("9-999");
        patient.setDoctorId("D001");
        patient.setNurseId("N001");
        patient.setNurseLevel(2);
        // 数据不一致：有出院时间但状态是在院
        patient.setInhospitalTime(LocalDateTime.now().minusDays(10));
        patient.setOuthospitalTime(LocalDateTime.now().minusDays(1));
        patient.setStatus(1); // 在院状态（与出院时间不一致）
        patient.setCategory("医保");
        patient.setInpatientWard("9病区");
        patient.setUpdatedAt(LocalDateTime.now());
        return patient;
    }
} 