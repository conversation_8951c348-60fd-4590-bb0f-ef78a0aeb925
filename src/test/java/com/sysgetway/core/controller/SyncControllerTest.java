package com.sysgetway.core.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.common.util.ResponseResult;
import com.sysgetway.core.model.dto.SyncResultDTO;
import com.sysgetway.core.service.SyncService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 同步控制器测试类
 */
@ExtendWith(MockitoExtension.class)
public class SyncControllerTest {

    private MockMvc mockMvc;

    @Mock
    private SyncService syncService;

    @InjectMocks
    private SyncController syncController;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(syncController).build();
    }

    @Test
    @DisplayName("测试触发全量同步 - 成功")
    void testSyncFull_Success() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        String tableName = SyncConstants.TableName.DEPARTMENT;
        
        // 模拟服务返回结果
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .taskId("test-task-id")
                .build();

        when(syncService.syncFullAsync(hospitalId, tableName, null)).thenReturn(ResponseResult.success(result));
        
        // 执行请求并验证
        mockMvc.perform(put("/api/sync/full/{hospitalId}/{tableName}", hospitalId, tableName))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.status").value(SyncConstants.SyncStatus.IN_PROGRESS))
                .andExpect(jsonPath("$.data.message").value(SyncConstants.SyncMessage.IN_PROGRESS))
                .andExpect(jsonPath("$.data.taskId").value("test-task-id"));
        
        // 验证服务方法被调用
        verify(syncService, times(1)).syncFullAsync(hospitalId, tableName, null);
    }

    @Test
    @DisplayName("测试触发全量同步（带数据） - 成功")
    void testSyncFullWithData_Success() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        String tableName = SyncConstants.TableName.DEPARTMENT;

        // 准备请求数据
        List<Object> dataList = new ArrayList<>();
        Map<String, Object> dept1 = new HashMap<>();
        dept1.put("deptId", 1);
        dept1.put("deptName", "内科");
        dept1.put("deptCode", "NK");
        dataList.add(dept1);

        Map<String, Object> dept2 = new HashMap<>();
        dept2.put("deptId", 2);
        dept2.put("deptName", "外科");
        dept2.put("deptCode", "WK");
        dataList.add(dept2);

        // 模拟服务返回结果
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now())
                .taskId("test-task-id")
                .build();

        when(syncService.syncFullAsync(hospitalId, tableName, dataList)).thenReturn(ResponseResult.success(result));

        // 执行请求并验证
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonContent = objectMapper.writeValueAsString(dataList);

        mockMvc.perform(put("/api/sync/full/{hospitalId}/{tableName}", hospitalId, tableName)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(jsonContent))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.status").value(SyncConstants.SyncStatus.IN_PROGRESS))
                .andExpect(jsonPath("$.data.message").value(SyncConstants.SyncMessage.IN_PROGRESS))
                .andExpect(jsonPath("$.data.taskId").value("test-task-id"));

        // 验证服务方法被调用
        verify(syncService, times(1)).syncFullAsync(hospitalId, tableName, dataList);
    }
    
    @Test
    @DisplayName("测试触发增量同步 - 成功")
    void testSyncIncremental_Success() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        String tableName = SyncConstants.TableName.USER;
        
        // 模拟服务返回结果
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.SUCCESS)
                .message(SyncConstants.SyncMessage.SUCCESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .count(3)
                .startTime(LocalDateTime.now().minusSeconds(5))
                .endTime(LocalDateTime.now())
                .costTime(5000L)
                .build();
        
        when(syncService.syncIncremental(hospitalId, tableName)).thenReturn(ResponseResult.success(result));
        
        // 执行请求并验证
        mockMvc.perform(put("/api/sync/incremental/{hospitalId}/{tableName}", hospitalId, tableName))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.status").value(SyncConstants.SyncStatus.SUCCESS))
                .andExpect(jsonPath("$.data.message").value(SyncConstants.SyncMessage.SUCCESS))
                .andExpect(jsonPath("$.data.count").value(3));
        
        // 验证服务方法被调用
        verify(syncService, times(1)).syncIncremental(hospitalId, tableName);
    }
    
    @Test
    @DisplayName("测试触发患者分类同步 - 入院患者")
    void testSyncPatientByType_In() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        String patientType = SyncConstants.PatientType.IN;
        
        // 模拟服务返回结果
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.SUCCESS)
                .message(SyncConstants.SyncMessage.SUCCESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType("patient:" + patientType)
                .count(2)
                .startTime(LocalDateTime.now().minusSeconds(3))
                .endTime(LocalDateTime.now())
                .costTime(3000L)
                .build();
        
        when(syncService.syncPatientByType(hospitalId, patientType)).thenReturn(ResponseResult.success(result));
        
        // 执行请求并验证
        mockMvc.perform(put("/api/sync/patient/{hospitalId}/{patientType}", hospitalId, patientType))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.status").value(SyncConstants.SyncStatus.SUCCESS))
                .andExpect(jsonPath("$.data.message").value(SyncConstants.SyncMessage.SUCCESS))
                .andExpect(jsonPath("$.data.count").value(2));
        
        // 验证服务方法被调用
        verify(syncService, times(1)).syncPatientByType(hospitalId, patientType);
    }
    
    @Test
    @DisplayName("测试获取同步状态")
    void testGetSyncStatus() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        String tableName = SyncConstants.TableName.PATIENT;
        
        // 模拟服务返回结果
        SyncResultDTO result = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.SUCCESS)
                .message(SyncConstants.SyncMessage.SUCCESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .count(10)
                .startTime(LocalDateTime.now().minusMinutes(5))
                .endTime(LocalDateTime.now().minusMinutes(4))
                .costTime(60000L)
                .build();
        
        when(syncService.getSyncStatus(hospitalId, tableName)).thenReturn(ResponseResult.success(result));
        
        // 执行请求并验证
        mockMvc.perform(get("/api/sync/status/{hospitalId}/{tableName}", hospitalId, tableName))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.status").value(SyncConstants.SyncStatus.SUCCESS))
                .andExpect(jsonPath("$.data.message").value(SyncConstants.SyncMessage.SUCCESS))
                .andExpect(jsonPath("$.data.count").value(10));
        
        // 验证服务方法被调用
        verify(syncService, times(1)).getSyncStatus(hospitalId, tableName);
    }
    
    @Test
    @DisplayName("测试获取最后同步时间")
    void testGetLastSyncTime() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        String tableName = SyncConstants.TableName.DEPARTMENT;
        String lastSyncTime = LocalDateTime.now().minusDays(1).toString();
        
        when(syncService.getLastSyncTime(hospitalId, tableName)).thenReturn(ResponseResult.success(lastSyncTime));
        
        // 执行请求并验证
        mockMvc.perform(get("/api/sync/last-time/{hospitalId}/{tableName}", hospitalId, tableName))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(lastSyncTime));
        
        // 验证服务方法被调用
        verify(syncService, times(1)).getLastSyncTime(hospitalId, tableName);
    }
} 