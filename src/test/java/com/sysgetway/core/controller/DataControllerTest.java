package com.sysgetway.core.controller;

import com.alibaba.fastjson2.JSON;
import com.sysgetway.core.common.util.RedisUtils;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.utils.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 数据控制器测试类
 */
@ExtendWith(MockitoExtension.class)
public class DataControllerTest {

    private MockMvc mockMvc;

    @Mock
    private RedisUtils redisUtils;

    @InjectMocks
    private DataController dataController;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(dataController).build();
    }

    @Test
    @DisplayName("测试导入患者数据")
    void testImportPatients() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        List<Patient> patients = new ArrayList<>();
        
        // 添加入院患者
        patients.add(TestDataFactory.createInPatient());
        
        // 添加在院患者
        patients.add(TestDataFactory.createUpPatient());
        
        // 添加出院患者
        patients.add(TestDataFactory.createOutPatient());
        
        // 添加数据不一致的患者
        patients.add(TestDataFactory.createInconsistentPatient());
        
        // Mock Redis操作
        doNothing().when(redisUtils).set(anyString(), any(), anyLong());
        
        // 执行请求并验证
        mockMvc.perform(post("/api/patients/{hospitalId}/import", hospitalId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(patients)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalCount").value(4))
                .andExpect(jsonPath("$.data.inPatients").value(1))
                .andExpect(jsonPath("$.data.upPatients").value(2))  // 在院患者包括一个正常在院和一个状态不一致的患者
                .andExpect(jsonPath("$.data.outPatients").value(1));
        
        // 验证Redis操作
        // 验证总患者数据保存
        verify(redisUtils, times(1)).set(anyString(), any(), anyLong());
        
        // 验证按患者类型保存
        verify(redisUtils, times(4)).set(anyString(), any(), anyLong());
    }

    @Test
    @DisplayName("测试导入空患者数据")
    void testImportEmptyPatients() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        List<Patient> emptyPatients = new ArrayList<>();
        
        // 执行请求并验证
        mockMvc.perform(post("/api/patients/{hospitalId}/import", hospitalId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(emptyPatients)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("患者数据不能为空"));
    }
    
    @Test
    @DisplayName("测试患者分类查询")
    void testGetPatientsByType() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        String patientType = "in";
        List<Patient> patients = Arrays.asList(TestDataFactory.createInPatient());
        
        // Mock Redis操作
        when(redisUtils.get(anyString())).thenReturn(JSON.toJSONString(patients));
        
        // 执行请求并验证
        mockMvc.perform(get("/api/patients/{hospitalId}/{patientType}", hospitalId, patientType))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.length()").value(1));
    }
    
    @Test
    @DisplayName("测试患者统计")
    void testGetPatientStats() throws Exception {
        // 准备测试数据
        String hospitalId = "H001";
        
        // Mock Redis操作 - 不同类型的患者数据
        List<Patient> inPatients = Arrays.asList(TestDataFactory.createInPatient());
        List<Patient> upPatients = Arrays.asList(TestDataFactory.createUpPatient());
        List<Patient> outPatients = Arrays.asList(TestDataFactory.createOutPatient());
        
        when(redisUtils.get(eq("hospital:patient:type:" + hospitalId + ":in"))).thenReturn(JSON.toJSONString(inPatients));
        when(redisUtils.get(eq("hospital:patient:type:" + hospitalId + ":up"))).thenReturn(JSON.toJSONString(upPatients));
        when(redisUtils.get(eq("hospital:patient:type:" + hospitalId + ":out"))).thenReturn(JSON.toJSONString(outPatients));
        
        // 执行请求并验证
        mockMvc.perform(get("/api/patients/{hospitalId}/stats", hospitalId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.inPatients").value(1))
                .andExpect(jsonPath("$.data.upPatients").value(1))
                .andExpect(jsonPath("$.data.outPatients").value(1))
                .andExpect(jsonPath("$.data.totalPatients").value(3));
    }
} 