package com.sysgetway.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.sysgetway.core.entity.Patient;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 简单的患者序列化测试
 * 验证修复后的JSON序列化是否能正确处理Java 8时间类型
 */
public class TestPatientSerialization {

    public static void main(String[] args) {
        System.out.println("开始测试患者JSON序列化...");
        
        try {
            // 创建测试患者对象
            Patient patient = new Patient();
            patient.setInpatientInfoId("P001");
            patient.setName("张三");
            patient.setHospitalizationNo("H001");
            patient.setBirthday(LocalDate.of(1990, 1, 1));
            patient.setInhospitalTime(LocalDateTime.of(2024, 7, 18, 10, 0, 0));
            patient.setOuthospitalTime(LocalDateTime.of(2024, 7, 25, 14, 30, 0));
            patient.setUpdatedAt(LocalDateTime.now());
            patient.setStatus(1);

            System.out.println("原始患者对象:");
            System.out.println("ID: " + patient.getInpatientInfoId());
            System.out.println("姓名: " + patient.getName());
            System.out.println("住院号: " + patient.getHospitalizationNo());
            System.out.println("生日: " + patient.getBirthday());
            System.out.println("入院时间: " + patient.getInhospitalTime());
            System.out.println("出院时间: " + patient.getOuthospitalTime());
            System.out.println("状态: " + patient.getStatus());
            System.out.println();

            // 测试Jackson序列化（与Redis配置一致）
            System.out.println("正在序列化...");
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());

            String patientJson = objectMapper.writeValueAsString(patient);

            System.out.println("序列化结果:");
            System.out.println(patientJson);
            System.out.println();

            // 测试反序列化
            System.out.println("正在反序列化...");
            Patient deserializedPatient = objectMapper.readValue(patientJson, Patient.class);
            
            System.out.println("反序列化结果:");
            System.out.println("ID: " + deserializedPatient.getInpatientInfoId());
            System.out.println("姓名: " + deserializedPatient.getName());
            System.out.println("住院号: " + deserializedPatient.getHospitalizationNo());
            System.out.println("生日: " + deserializedPatient.getBirthday());
            System.out.println("入院时间: " + deserializedPatient.getInhospitalTime());
            System.out.println("出院时间: " + deserializedPatient.getOuthospitalTime());
            System.out.println("状态: " + deserializedPatient.getStatus());
            System.out.println();

            // 验证数据一致性
            boolean isValid = patient.getInpatientInfoId().equals(deserializedPatient.getInpatientInfoId()) &&
                             patient.getName().equals(deserializedPatient.getName()) &&
                             patient.getHospitalizationNo().equals(deserializedPatient.getHospitalizationNo()) &&
                             patient.getBirthday().equals(deserializedPatient.getBirthday()) &&
                             patient.getInhospitalTime().equals(deserializedPatient.getInhospitalTime()) &&
                             patient.getOuthospitalTime().equals(deserializedPatient.getOuthospitalTime()) &&
                             patient.getStatus().equals(deserializedPatient.getStatus());

            if (isValid) {
                System.out.println("✅ 患者JSON序列化/反序列化测试通过！");
                System.out.println("✅ Java 8时间类型序列化问题已修复！");
            } else {
                System.out.println("❌ 数据不一致，测试失败！");
            }

        } catch (Exception e) {
            System.out.println("❌ 测试失败，异常信息:");
            e.printStackTrace();
        }
    }
}
