package com.sysgetway.core.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter.Feature;
import com.sysgetway.core.entity.Patient;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 患者序列化测试
 * 验证修复后的JSON序列化是否能正确处理Java 8时间类型
 */
@SpringBootTest
public class PatientSerializationTest {

    @Test
    public void testPatientJsonSerialization() {
        // 创建测试患者对象
        Patient patient = new Patient();
        patient.setInpatientInfoId("P001");
        patient.setName("张三");
        patient.setHospitalizationNo("H001");
        patient.setBirthday(LocalDate.of(1990, 1, 1));
        patient.setInhospitalTime(LocalDateTime.of(2024, 7, 18, 10, 0, 0));
        patient.setOuthospitalTime(LocalDateTime.of(2024, 7, 25, 14, 30, 0));
        patient.setUpdatedAt(LocalDateTime.now());
        patient.setStatus(1);

        // 测试序列化
        String patientJson = JSON.toJSONString(patient, 
            Feature.WriteNulls, 
            Feature.WriteNullStringAsEmpty);

        assertNotNull(patientJson);
        assertFalse(patientJson.isEmpty());
        
        System.out.println("序列化结果:");
        System.out.println(patientJson);

        // 测试反序列化
        Patient deserializedPatient = JSON.parseObject(patientJson, Patient.class);
        
        assertNotNull(deserializedPatient);
        assertEquals(patient.getInpatientInfoId(), deserializedPatient.getInpatientInfoId());
        assertEquals(patient.getName(), deserializedPatient.getName());
        assertEquals(patient.getHospitalizationNo(), deserializedPatient.getHospitalizationNo());
        assertEquals(patient.getBirthday(), deserializedPatient.getBirthday());
        assertEquals(patient.getInhospitalTime(), deserializedPatient.getInhospitalTime());
        assertEquals(patient.getOuthospitalTime(), deserializedPatient.getOuthospitalTime());
        assertEquals(patient.getStatus(), deserializedPatient.getStatus());

        System.out.println("✅ 患者JSON序列化/反序列化测试通过");
    }

    @Test
    public void testPatientWithNullValues() {
        // 创建包含null值的患者对象
        Patient patient = new Patient();
        patient.setInpatientInfoId("P002");
        patient.setName("李四");
        patient.setHospitalizationNo("H002");
        // birthday, outhospitalTime等保持为null

        // 测试序列化
        String patientJson = JSON.toJSONString(patient, 
            Feature.WriteNulls, 
            Feature.WriteNullStringAsEmpty);

        assertNotNull(patientJson);
        assertFalse(patientJson.isEmpty());
        
        System.out.println("包含null值的序列化结果:");
        System.out.println(patientJson);

        // 测试反序列化
        Patient deserializedPatient = JSON.parseObject(patientJson, Patient.class);
        
        assertNotNull(deserializedPatient);
        assertEquals(patient.getInpatientInfoId(), deserializedPatient.getInpatientInfoId());
        assertEquals(patient.getName(), deserializedPatient.getName());
        assertEquals(patient.getHospitalizationNo(), deserializedPatient.getHospitalizationNo());
        assertNull(deserializedPatient.getBirthday());
        assertNull(deserializedPatient.getOuthospitalTime());

        System.out.println("✅ 包含null值的患者JSON序列化/反序列化测试通过");
    }
}
