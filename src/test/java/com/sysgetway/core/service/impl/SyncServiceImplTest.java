package com.sysgetway.core.service.impl;

import com.sysgetway.core.common.constant.SyncConstants;
import com.sysgetway.core.common.exception.BusinessException;
import com.sysgetway.core.common.util.RedisUtils;
import com.sysgetway.core.common.util.ResponseResult;
import com.sysgetway.core.model.dto.SyncResultDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 同步服务实现类测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class SyncServiceImplTest {

    @Mock
    private RedisUtils redisUtils;

    @InjectMocks
    private SyncServiceImpl syncService;

    private final String hospitalId = "H001";
    private final DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    
    @BeforeEach
    void setUp() {
        // Mock Redis工具类的通用方法
        doNothing().when(redisUtils).set(anyString(), any(), anyLong());
        doNothing().when(redisUtils).set(anyString(), any());
    }

    @Test
    @DisplayName("测试全量同步科室 - 成功")
    void testSyncFullDepartment_Success() {
        // 准备测试数据
        String tableName = SyncConstants.TableName.DEPARTMENT;
        
        // Mock Redis结果
        when(redisUtils.get(anyString())).thenReturn(null);
        
        // 执行测试
        ResponseResult<SyncResultDTO> response = syncService.syncFull(hospitalId, tableName);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(SyncConstants.SyncStatus.IN_PROGRESS, response.getData().getStatus());
        assertEquals(SyncConstants.SyncMessage.IN_PROGRESS, response.getData().getMessage());
        assertEquals(hospitalId, response.getData().getHospitalId());
        assertEquals(tableName, response.getData().getTableNameOrPatientType());
    }

    @Test
    @DisplayName("测试全量同步 - 表名无效")
    void testSyncFull_InvalidTableName() {
        // 准备测试数据
        String invalidTableName = "invalid_table";
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            syncService.syncFull(hospitalId, invalidTableName);
        });
        
        // 验证异常信息
        assertTrue(exception.getMessage().contains("无效的表名"));
    }
    
    @Test
    @DisplayName("测试全量同步 - 正在进行中")
    void testSyncFull_InProgress() {
        // 准备测试数据
        String tableName = SyncConstants.TableName.USER;
        
        // Mock Redis结果 - 返回正在进行中的同步状态
        SyncResultDTO inProgressResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.IN_PROGRESS)
                .message(SyncConstants.SyncMessage.IN_PROGRESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .startTime(LocalDateTime.now().minusSeconds(30))
                .build();
        
        when(redisUtils.get(String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableName)))
                .thenReturn(inProgressResult);
        
        // 执行测试
        ResponseResult<SyncResultDTO> response = syncService.syncFull(hospitalId, tableName);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(SyncConstants.SyncStatus.IN_PROGRESS, response.getData().getStatus());
    }
    
    @Test
    @DisplayName("测试增量同步 - 无最后同步时间")
    void testSyncIncremental_NoLastSyncTime() {
        // 准备测试数据
        String tableName = SyncConstants.TableName.PATIENT;
        
        // Mock Redis结果 - 无最后同步时间
        when(redisUtils.get(anyString())).thenReturn(null);
        
        // 执行测试
        ResponseResult<SyncResultDTO> response = syncService.syncIncremental(hospitalId, tableName);
        
        // 验证结果 - 应该执行全量同步
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(SyncConstants.SyncStatus.IN_PROGRESS, response.getData().getStatus());
    }
    
    @Test
    @DisplayName("测试增量同步 - 有最后同步时间")
    void testSyncIncremental_WithLastSyncTime() {
        // 准备测试数据
        String tableName = SyncConstants.TableName.DEPARTMENT;
        LocalDateTime lastTime = LocalDateTime.now().minusDays(1);
        String lastTimeStr = lastTime.format(formatter);
        
        // Mock Redis结果
        when(redisUtils.get(String.format(SyncConstants.RedisKeyPrefix.SYNC_LAST_TIME, hospitalId, tableName)))
                .thenReturn(lastTimeStr);
        when(redisUtils.get(String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableName)))
                .thenReturn(null);
        
        // 执行测试
        ResponseResult<SyncResultDTO> response = syncService.syncIncremental(hospitalId, tableName);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(SyncConstants.SyncStatus.IN_PROGRESS, response.getData().getStatus());
        assertNotNull(response.getData().getLastSyncTime());
    }
    
    @Test
    @DisplayName("测试患者分类同步 - 入院患者")
    void testSyncPatientByType_In() {
        // 准备测试数据
        String patientType = SyncConstants.PatientType.IN;
        
        // Mock Redis结果
        when(redisUtils.get(anyString())).thenReturn(null);
        
        // 执行测试
        ResponseResult<SyncResultDTO> response = syncService.syncPatientByType(hospitalId, patientType);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(SyncConstants.SyncStatus.IN_PROGRESS, response.getData().getStatus());
        assertEquals(hospitalId, response.getData().getHospitalId());
        assertEquals("patient:" + patientType, response.getData().getTableNameOrPatientType());
    }
    
    @Test
    @DisplayName("测试患者分类同步 - 患者类型无效")
    void testSyncPatientByType_InvalidType() {
        // 准备测试数据
        String invalidType = "invalid_type";
        
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            syncService.syncPatientByType(hospitalId, invalidType);
        });
        
        // 验证异常信息
        assertTrue(exception.getMessage().contains("无效的患者类型"));
    }
    
    @Test
    @DisplayName("测试获取同步状态")
    void testGetSyncStatus() {
        // 准备测试数据
        String tableName = SyncConstants.TableName.USER;
        
        // Mock Redis结果
        SyncResultDTO mockResult = SyncResultDTO.builder()
                .status(SyncConstants.SyncStatus.SUCCESS)
                .message(SyncConstants.SyncMessage.SUCCESS)
                .hospitalId(hospitalId)
                .tableNameOrPatientType(tableName)
                .count(5)
                .startTime(LocalDateTime.now().minusMinutes(10))
                .endTime(LocalDateTime.now().minusMinutes(9))
                .costTime(60000L)
                .build();
                
        when(redisUtils.get(String.format(SyncConstants.RedisKeyPrefix.SYNC_STATUS, hospitalId, tableName)))
                .thenReturn(mockResult);
        
        // 执行测试
        ResponseResult<SyncResultDTO> response = syncService.getSyncStatus(hospitalId, tableName);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertNotNull(response.getData());
        assertEquals(SyncConstants.SyncStatus.SUCCESS, response.getData().getStatus());
        assertEquals(5, response.getData().getCount());
    }
    
    @Test
    @DisplayName("测试获取最后同步时间")
    void testGetLastSyncTime() {
        // 准备测试数据
        String tableName = SyncConstants.TableName.DEPARTMENT;
        LocalDateTime lastTime = LocalDateTime.now().minusDays(1);
        String lastTimeStr = lastTime.format(formatter);
        
        // Mock Redis结果
        when(redisUtils.get(String.format(SyncConstants.RedisKeyPrefix.SYNC_LAST_TIME, hospitalId, tableName)))
                .thenReturn(lastTimeStr);
        
        // 执行测试
        ResponseResult<String> response = syncService.getLastSyncTime(hospitalId, tableName);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals(lastTimeStr, response.getData());
    }
    
    @Test
    @DisplayName("测试获取最后同步时间 - 无记录")
    void testGetLastSyncTime_NoRecord() {
        // 准备测试数据
        String tableName = SyncConstants.TableName.DEPARTMENT;
        
        // Mock Redis结果
        when(redisUtils.get(String.format(SyncConstants.RedisKeyPrefix.SYNC_LAST_TIME, hospitalId, tableName)))
                .thenReturn(null);
        
        // 执行测试
        ResponseResult<String> response = syncService.getLastSyncTime(hospitalId, tableName);
        
        // 验证结果
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("未找到最后同步时间记录", response.getData());
    }
} 