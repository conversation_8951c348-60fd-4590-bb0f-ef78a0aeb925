package com.sysgetway.core.example;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 简化版患者数据生成器
 * 不使用外部依赖，直接生成JSON字符串
 */
public class SimplePatientJsonGenerator {
    
    private static final Random random = new Random();
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    
    public static void main(String[] args) {
        try {
            // 默认生成20条患者数据，避免控制台输出过多
            int count = 20;
            String outputFile = "patient_data.json";
            
            // 检查是否有命令行参数指定数量和输出文件
            if (args.length > 0) {
                try {
                    count = Integer.parseInt(args[0]);
                } catch (NumberFormatException e) {
                    System.err.println("无效的数量参数，使用默认值20");
                }
                
                if (args.length > 1) {
                    outputFile = args[1];
                }
            }
            
            System.out.println("正在生成 " + count + " 条患者数据到文件 " + outputFile + " ...");
            long startTime = System.currentTimeMillis();
            
            // 生成患者数据JSON
            String jsonData = generatePatientJson(count);
            
            // 保存到文件
            saveToFile(jsonData, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println("数据生成完成，共生成 " + count + " 条患者数据，耗时: " + (endTime - startTime) / 1000 + " 秒");
            System.out.println("数据已保存到文件: " + outputFile);
            
        } catch (Exception e) {
            System.err.println("生成数据时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 生成患者数据JSON字符串
     * 
     * @param count 生成的患者数量
     * @return JSON字符串
     */
    private static String generatePatientJson(int count) {
        StringBuilder json = new StringBuilder();
        json.append("[");
        
        // 按比例分配不同类型的患者
        // 入院患者约占10%，在院患者约占60%，出院患者约占30%
        int inPatientCount = count / 10;
        int upPatientCount = (count * 6) / 10;
        int outPatientCount = count - inPatientCount - upPatientCount;
        
        // 预先定义数据变量
        String[] surnames = {"张", "李", "王", "赵", "刘", "陈", "杨", "黄", "周", "吴", "郑", "孙", "马", "朱", "胡", "林", "郭", "何", "高", "罗"};
        String[] names = {"伟", "芳", "娜", "秀英", "敏", "静", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞", "平", "刚", "桂英"};
        String[] diagnoses = {
            "高血压", "糖尿病", "冠心病", "肺炎", "胃炎", "肝炎", "肾炎", 
            "骨折", "脑梗塞", "心肌梗塞", "哮喘", "贫血", "肿瘤", "结石",
            "关节炎", "白内障", "青光眼", "中风", "心力衰竭", "胆囊炎",
            "肠炎", "支气管炎", "胃溃疡", "十二指肠溃疡", "痛风", "风湿病"
        };
        String[] categories = {"医保", "自费", "公费", "商业保险", "农村合作医疗", "城镇职工医保", "城镇居民医保"};
        
        LocalDateTime now = LocalDateTime.now();
        
        // 生成入院患者
        for (int i = 0; i < inPatientCount; i++) {
            if (i > 0) {
                json.append(",");
            }
            
            String name = surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)];
            String idCard = generateIdCard();
            int sex = random.nextInt(2) + 1;
            String mobile = "1" + (3 + random.nextInt(6)) + random.nextInt(10) + String.format("%08d", random.nextInt(100000000));
            int age = 20 + random.nextInt(60);
            LocalDate birthday = LocalDate.now().minusYears(age).minusDays(random.nextInt(365));
            String hospitalizationNo = "HIN" + String.format("%05d", i + 1);
            int deptId = random.nextInt(10) + 1;
            String sickbedNo = deptId + "-" + String.format("%03d", random.nextInt(100) + 1);
            String inpatientWard = deptId + "病区";
            String diagnosis = diagnoses[random.nextInt(diagnoses.length)];
            String doctorId = "D" + String.format("%03d", deptId * 10 + random.nextInt(5));
            String nurseId = "N" + String.format("%03d", deptId * 10 + random.nextInt(8));
            int nurseLevel = random.nextInt(4) + 1;
            String category = categories[random.nextInt(categories.length)];
            LocalDateTime inhospitalTime = now.minusHours(random.nextInt(24) + 1);
            
            json.append("{");
            json.append("\"inpatientInfoId\":\"PIN").append(String.format("%05d", i + 1)).append("\",");
            json.append("\"name\":\"").append(name).append("\",");
            json.append("\"idCard\":\"").append(idCard).append("\",");
            json.append("\"mobile\":\"").append(mobile).append("\",");
            json.append("\"sex\":").append(sex).append(",");
            json.append("\"age\":").append(age).append(",");
            json.append("\"birthday\":\"").append(birthday).append("\",");
            json.append("\"hospitalizationNo\":\"").append(hospitalizationNo).append("\",");
            json.append("\"inhospitalDiagnose\":\"").append(diagnosis).append("\",");
            json.append("\"deptId\":").append(deptId).append(",");
            json.append("\"sickbedNo\":\"").append(sickbedNo).append("\",");
            json.append("\"doctorId\":\"").append(doctorId).append("\",");
            json.append("\"nurseId\":\"").append(nurseId).append("\",");
            json.append("\"nurseLevel\":").append(nurseLevel).append(",");
            json.append("\"inhospitalTime\":\"").append(inhospitalTime.format(formatter)).append("\",");
            json.append("\"outhospitalTime\":null,");
            json.append("\"status\":1,");
            json.append("\"category\":\"").append(category).append("\",");
            json.append("\"inpatientWard\":\"").append(inpatientWard).append("\",");
            json.append("\"updatedAt\":\"").append(now.format(formatter)).append("\"");
            json.append("}");
        }
        
        // 生成在院患者
        for (int i = 0; i < upPatientCount; i++) {
            if (i > 0 || inPatientCount > 0) {
                json.append(",");
            }
            
            String name = surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)];
            String idCard = generateIdCard();
            int sex = random.nextInt(2) + 1;
            String mobile = "1" + (3 + random.nextInt(6)) + random.nextInt(10) + String.format("%08d", random.nextInt(100000000));
            int age = 20 + random.nextInt(60);
            LocalDate birthday = LocalDate.now().minusYears(age).minusDays(random.nextInt(365));
            String hospitalizationNo = "HUP" + String.format("%05d", i + 1);
            int deptId = random.nextInt(10) + 1;
            String sickbedNo = deptId + "-" + String.format("%03d", random.nextInt(100) + 1);
            String inpatientWard = deptId + "病区";
            String diagnosis = diagnoses[random.nextInt(diagnoses.length)];
            String doctorId = "D" + String.format("%03d", deptId * 10 + random.nextInt(5));
            String nurseId = "N" + String.format("%03d", deptId * 10 + random.nextInt(8));
            int nurseLevel = random.nextInt(4) + 1;
            String category = categories[random.nextInt(categories.length)];
            LocalDateTime inhospitalTime = now.minusDays(random.nextInt(30) + 1);
            
            json.append("{");
            json.append("\"inpatientInfoId\":\"PUP").append(String.format("%05d", i + 1)).append("\",");
            json.append("\"name\":\"").append(name).append("\",");
            json.append("\"idCard\":\"").append(idCard).append("\",");
            json.append("\"mobile\":\"").append(mobile).append("\",");
            json.append("\"sex\":").append(sex).append(",");
            json.append("\"age\":").append(age).append(",");
            json.append("\"birthday\":\"").append(birthday).append("\",");
            json.append("\"hospitalizationNo\":\"").append(hospitalizationNo).append("\",");
            json.append("\"inhospitalDiagnose\":\"").append(diagnosis).append("\",");
            json.append("\"deptId\":").append(deptId).append(",");
            json.append("\"sickbedNo\":\"").append(sickbedNo).append("\",");
            json.append("\"doctorId\":\"").append(doctorId).append("\",");
            json.append("\"nurseId\":\"").append(nurseId).append("\",");
            json.append("\"nurseLevel\":").append(nurseLevel).append(",");
            json.append("\"inhospitalTime\":\"").append(inhospitalTime.format(formatter)).append("\",");
            json.append("\"outhospitalTime\":null,");
            json.append("\"status\":1,");
            json.append("\"category\":\"").append(category).append("\",");
            json.append("\"inpatientWard\":\"").append(inpatientWard).append("\",");
            json.append("\"updatedAt\":\"").append(now.format(formatter)).append("\"");
            json.append("}");
        }
        
        // 生成出院患者
        for (int i = 0; i < outPatientCount; i++) {
            if (i > 0 || inPatientCount > 0 || upPatientCount > 0) {
                json.append(",");
            }
            
            String name = surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)];
            String idCard = generateIdCard();
            int sex = random.nextInt(2) + 1;
            String mobile = "1" + (3 + random.nextInt(6)) + random.nextInt(10) + String.format("%08d", random.nextInt(100000000));
            int age = 20 + random.nextInt(60);
            LocalDate birthday = LocalDate.now().minusYears(age).minusDays(random.nextInt(365));
            String hospitalizationNo = "HOUT" + String.format("%05d", i + 1);
            int deptId = random.nextInt(10) + 1;
            String sickbedNo = deptId + "-" + String.format("%03d", random.nextInt(100) + 1);
            String inpatientWard = deptId + "病区";
            String diagnosis = diagnoses[random.nextInt(diagnoses.length)];
            String doctorId = "D" + String.format("%03d", deptId * 10 + random.nextInt(5));
            String nurseId = "N" + String.format("%03d", deptId * 10 + random.nextInt(8));
            int nurseLevel = random.nextInt(4) + 1;
            String category = categories[random.nextInt(categories.length)];
            LocalDateTime inhospitalTime = now.minusDays(30 + random.nextInt(60));
            LocalDateTime outhospitalTime = now.minusDays(random.nextInt(20) + 1);
            
            // 确保出院时间晚于入院时间
            if (outhospitalTime.isBefore(inhospitalTime)) {
                outhospitalTime = inhospitalTime.plusDays(random.nextInt(30) + 1);
            }
            
            json.append("{");
            json.append("\"inpatientInfoId\":\"POUT").append(String.format("%05d", i + 1)).append("\",");
            json.append("\"name\":\"").append(name).append("\",");
            json.append("\"idCard\":\"").append(idCard).append("\",");
            json.append("\"mobile\":\"").append(mobile).append("\",");
            json.append("\"sex\":").append(sex).append(",");
            json.append("\"age\":").append(age).append(",");
            json.append("\"birthday\":\"").append(birthday).append("\",");
            json.append("\"hospitalizationNo\":\"").append(hospitalizationNo).append("\",");
            json.append("\"inhospitalDiagnose\":\"").append(diagnosis).append("\",");
            json.append("\"deptId\":").append(deptId).append(",");
            json.append("\"sickbedNo\":\"").append(sickbedNo).append("\",");
            json.append("\"doctorId\":\"").append(doctorId).append("\",");
            json.append("\"nurseId\":\"").append(nurseId).append("\",");
            json.append("\"nurseLevel\":").append(nurseLevel).append(",");
            json.append("\"inhospitalTime\":\"").append(inhospitalTime.format(formatter)).append("\",");
            json.append("\"outhospitalTime\":\"").append(outhospitalTime.format(formatter)).append("\",");
            json.append("\"status\":2,");
            json.append("\"category\":\"").append(category).append("\",");
            json.append("\"inpatientWard\":\"").append(inpatientWard).append("\",");
            json.append("\"updatedAt\":\"").append(now.format(formatter)).append("\"");
            json.append("}");
        }
        
        json.append("]");
        return json.toString();
    }
    
    /**
     * 生成随机身份证号
     */
    private static String generateIdCard() {
        // 地区码 (随机选择一个有效的地区码)
        String[] areaCodes = {
            "110101", "110102", "110105", "110106", "110107", "110108", "110109", "110111", // 北京
            "310101", "310104", "310105", "310106", "310107", "310109", "310110", "310112", // 上海
            "440103", "440104", "440105", "440106", "440111", "440112", "440113", "440114", // 广州
            "440303", "440304", "440305", "440306", "440307", "440308", // 深圳
            "330102", "330103", "330104", "330105", "330106", "330108", "330109", "330110", // 杭州
            "320102", "320104", "320105", "320106", "320111", "320113", "320114", "320115"  // 南京
        };
        String areaCode = areaCodes[random.nextInt(areaCodes.length)];
        
        // 出生日期 (1940-01-01 到 2000-12-31)
        int year = 1940 + random.nextInt(61); // 1940-2000
        int month = random.nextInt(12) + 1;
        int day = random.nextInt(28) + 1; // 简化处理，避免处理大月小月
        
        String birthDate = String.format("%04d%02d%02d", year, month, day);
        
        // 顺序码 (3位数字)
        String sequenceCode = String.format("%03d", random.nextInt(1000));
        
        // 校验码 (简化处理，随机生成)
        char[] checkCodeChars = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'X'};
        String checkCode = String.valueOf(checkCodeChars[random.nextInt(checkCodeChars.length)]);
        
        return areaCode + birthDate + sequenceCode + checkCode;
    }
    
    /**
     * 将数据保存到文件
     * 
     * @param data 要保存的数据
     * @param fileName 文件名
     * @throws IOException 如果保存失败
     */
    private static void saveToFile(String data, String fileName) throws IOException {
        try (FileWriter writer = new FileWriter(fileName)) {
            writer.write(data);
        }
    }
} 