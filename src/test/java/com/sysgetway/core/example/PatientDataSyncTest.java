package com.sysgetway.core.example;

import com.alibaba.fastjson2.JSON;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.utils.HospitalDataGenerator;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.Scanner;

/**
 * 患者数据同步测试
 * 
 * 这个类用于生成测试数据并提交到 /sysgetway/api/sync/patient/{hospitalId}/{patientType} 接口
 */
public class PatientDataSyncTest {

    private static final Random random = new Random();
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    public static void main(String[] args) {
        try {
            // 医院ID
            String hospitalId = "H001";
            
            // 生成并提交入院患者数据
            System.out.println("\n=== 生成入院患者数据 ===");
            List<Patient> inPatients = generateInPatients(5);
            submitPatientData(hospitalId, "in", inPatients);
            
            // 生成并提交在院患者数据
            System.out.println("\n=== 生成在院患者数据 ===");
            List<Patient> upPatients = generateUpPatients(8);
            submitPatientData(hospitalId, "up", upPatients);
            
            // 生成并提交出院患者数据
            System.out.println("\n=== 生成出院患者数据 ===");
            List<Patient> outPatients = generateOutPatients(6);
            submitPatientData(hospitalId, "out", outPatients);
            
            System.out.println("\n=== 测试完成 ===");
            System.out.println("1. 已生成并提交三种类型的患者数据");
            System.out.println("2. 可以通过以下接口查询数据：");
            System.out.println("   - GET /api/patients/" + hospitalId + "/in - 查询入院患者");
            System.out.println("   - GET /api/patients/" + hospitalId + "/up - 查询在院患者");
            System.out.println("   - GET /api/patients/" + hospitalId + "/out - 查询出院患者");
            System.out.println("   - GET /api/monitor/patient-sync/stats/" + hospitalId + " - 查询患者统计信息");
            
        } catch (Exception e) {
            System.out.println("发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 生成入院患者数据（最近24小时内入院的患者）
     * 
     * @param count 生成的患者数量
     * @return 入院患者列表
     */
    private static List<Patient> generateInPatients(int count) {
        List<Patient> patients = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            Patient patient = createBasePatient("IN" + (i + 1));
            
            // 设置入院患者特有属性：最近24小时内入院，无出院时间
            LocalDateTime inhospitalTime = LocalDateTime.now().minusHours(random.nextInt(24) + 1);
            patient.setInhospitalTime(inhospitalTime);
            patient.setOuthospitalTime(null);
            patient.setStatus(1); // 在院
            
            patients.add(patient);
            System.out.println("生成入院患者: " + patient.getName() + 
                    ", 住院号: " + patient.getHospitalizationNo() + 
                    ", 入院时间: " + patient.getInhospitalTime().format(formatter));
        }
        
        return patients;
    }
    
    /**
     * 生成在院患者数据（1-30天前入院的患者）
     * 
     * @param count 生成的患者数量
     * @return 在院患者列表
     */
    private static List<Patient> generateUpPatients(int count) {
        List<Patient> patients = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            Patient patient = createBasePatient("UP" + (i + 1));
            
            // 设置在院患者特有属性：1-30天前入院，无出院时间
            LocalDateTime inhospitalTime = LocalDateTime.now().minusDays(random.nextInt(30) + 1);
            patient.setInhospitalTime(inhospitalTime);
            patient.setOuthospitalTime(null);
            patient.setStatus(1); // 在院
            
            patients.add(patient);
            System.out.println("生成在院患者: " + patient.getName() + 
                    ", 住院号: " + patient.getHospitalizationNo() + 
                    ", 入院时间: " + patient.getInhospitalTime().format(formatter));
        }
        
        return patients;
    }
    
    /**
     * 生成出院患者数据（已出院的患者）
     * 
     * @param count 生成的患者数量
     * @return 出院患者列表
     */
    private static List<Patient> generateOutPatients(int count) {
        List<Patient> patients = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            Patient patient = createBasePatient("OUT" + (i + 1));
            
            // 设置出院患者特有属性：30-90天前入院，1-10天前出院
            LocalDateTime inhospitalTime = LocalDateTime.now().minusDays(30 + random.nextInt(60));
            LocalDateTime outhospitalTime = LocalDateTime.now().minusDays(random.nextInt(10) + 1);
            
            patient.setInhospitalTime(inhospitalTime);
            patient.setOuthospitalTime(outhospitalTime);
            patient.setStatus(2); // 出院
            
            patients.add(patient);
            System.out.println("生成出院患者: " + patient.getName() + 
                    ", 住院号: " + patient.getHospitalizationNo() + 
                    ", 入院时间: " + patient.getInhospitalTime().format(formatter) + 
                    ", 出院时间: " + patient.getOuthospitalTime().format(formatter));
        }
        
        return patients;
    }
    
    /**
     * 创建基础患者对象，设置共同属性
     * 
     * @param idPrefix ID前缀
     * @return 基础患者对象
     */
    private static Patient createBasePatient(String idPrefix) {
        Patient patient = new Patient();
        
        // 唯一ID
        patient.setInpatientInfoId("P" + idPrefix + System.currentTimeMillis() % 10000);
        
        // 基本信息
        String[] surnames = {"张", "李", "王", "赵", "刘", "陈", "杨", "黄", "周", "吴"};
        String[] names = {"伟", "芳", "娜", "秀英", "敏", "静", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰"};
        patient.setName(surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)]);
        
        // 身份证号
        String idCard = generateIdCard();
        patient.setIdCard(idCard);
        
        // 性别 (1:男, 2:女)
        int sex = random.nextInt(2) + 1;
        patient.setSex(sex);
        
        // 手机号
        patient.setMobile("138" + String.format("%08d", random.nextInt(100000000)));
        
        // 年龄和生日
        int age = 20 + random.nextInt(60); // 20-79岁
        patient.setAge(age);
        patient.setBirthday(LocalDate.now().minusYears(age).minusDays(random.nextInt(365)));
        
        // 住院号 (必须有，用于生成唯一标识)
        patient.setHospitalizationNo("H" + idPrefix + random.nextInt(10000));
        
        // 科室和床位信息
        int deptId = random.nextInt(5) + 1;
        patient.setDeptId(deptId);
        patient.setSickbedNo(deptId + "-" + String.format("%02d", random.nextInt(20) + 1));
        patient.setInpatientWard(deptId + "病区");
        
        // 诊断信息
        String[] diagnoses = {
            "高血压", "糖尿病", "冠心病", "肺炎", "胃炎", "肝炎", "肾炎", 
            "骨折", "脑梗塞", "心肌梗塞", "哮喘", "贫血", "肿瘤", "结石"
        };
        patient.setInhospitalDiagnose(diagnoses[random.nextInt(diagnoses.length)]);
        
        // 医护人员信息
        patient.setDoctorId("D" + String.format("%03d", deptId));
        patient.setNurseId("N" + String.format("%03d", deptId));
        patient.setNurseLevel(random.nextInt(4) + 1); // 1-4级
        
        // 患者类别
        String[] categories = {"医保", "自费", "公费", "商业保险"};
        patient.setCategory(categories[random.nextInt(categories.length)]);
        
        // 更新时间
        patient.setUpdatedAt(LocalDateTime.now());
        
        return patient;
    }
    
    /**
     * 生成随机身份证号
     */
    private static String generateIdCard() {
        // 地区码 (随机选择一个有效的地区码)
        String[] areaCodes = {"110101", "310101", "440101", "510101", "330101"};
        String areaCode = areaCodes[random.nextInt(areaCodes.length)];
        
        // 出生日期 (1960-01-01 到 2000-12-31)
        int year = 1960 + random.nextInt(41); // 1960-2000
        int month = random.nextInt(12) + 1;
        int day = random.nextInt(28) + 1; // 简化处理，避免处理大月小月
        
        String birthDate = String.format("%04d%02d%02d", year, month, day);
        
        // 顺序码 (3位数字)
        String sequenceCode = String.format("%03d", random.nextInt(1000));
        
        // 校验码 (简化处理，随机生成)
        char[] checkCodeChars = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'X'};
        String checkCode = String.valueOf(checkCodeChars[random.nextInt(checkCodeChars.length)]);
        
        return areaCode + birthDate + sequenceCode + checkCode;
    }
    
    /**
     * 提交患者数据到接口
     * 
     * @param hospitalId 医院ID
     * @param patientType 患者类型 (in, up, out)
     * @param patients 患者数据列表
     */
    private static void submitPatientData(String hospitalId, String patientType, List<Patient> patients) {
        try {
            // 将患者数据序列化为JSON
            String jsonData = JSON.toJSONString(patients);
            System.out.println("准备提交 " + patients.size() + " 条" + patientType + "类型患者数据");
            
            // 创建HTTP请求
            URL url = new URL("http://localhost:8080/api/sync/patient/" + hospitalId + "/" + patientType);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("PUT");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setDoOutput(true);
            
            // 发送请求数据
            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // 读取响应
            int responseCode = conn.getResponseCode();
            System.out.println("响应状态码：" + responseCode);
            
            StringBuilder response = new StringBuilder();
            try (Scanner scanner = new Scanner(conn.getInputStream(), StandardCharsets.UTF_8.name())) {
                while (scanner.hasNextLine()) {
                    response.append(scanner.nextLine());
                }
            }
            
            System.out.println("响应内容：" + response.toString());
            
            // 断开连接
            conn.disconnect();
            
        } catch (Exception e) {
            System.out.println("提交数据时发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }
} 