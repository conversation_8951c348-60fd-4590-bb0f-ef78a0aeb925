# 使用curl命令提交患者数据示例

本文档提供了使用curl命令向 `/sysgetway/api/sync/patient/{hospitalId}/{patientType}` 接口提交患者数据的示例。

## 准备工作

1. 确保医疗数据同步网关系统已启动并运行在本地8080端口
2. 准备符合要求的JSON格式患者数据

## 入院患者数据提交示例

```bash
curl -X PUT \
  http://localhost:8080/api/sync/patient/H001/in \
  -H 'Content-Type: application/json' \
  -d '[
    {
      "inpatientInfoId": "PIN001",
      "name": "张三",
      "idCard": "110101199001011234",
      "mobile": "13800138000",
      "sex": 1,
      "age": 30,
      "birthday": "1990-01-01",
      "hospitalizationNo": "HIN001",
      "inhospitalDiagnose": "高血压",
      "deptId": 1,
      "sickbedNo": "1-101",
      "doctorId": "D001",
      "nurseId": "N001",
      "nurseLevel": 2,
      "inhospitalTime": "2025-07-14T10:00:00",
      "outhospitalTime": null,
      "status": 1,
      "category": "医保",
      "inpatientWard": "1病区",
      "updatedAt": "2025-07-14T10:00:00"
    },
    {
      "inpatientInfoId": "PIN002",
      "name": "李四",
      "idCard": "110101199002022345",
      "mobile": "13800138001",
      "sex": 2,
      "age": 28,
      "birthday": "1992-02-02",
      "hospitalizationNo": "HIN002",
      "inhospitalDiagnose": "冠心病",
      "deptId": 2,
      "sickbedNo": "2-102",
      "doctorId": "D002",
      "nurseId": "N002",
      "nurseLevel": 1,
      "inhospitalTime": "2025-07-14T08:30:00",
      "outhospitalTime": null,
      "status": 1,
      "category": "医保",
      "inpatientWard": "2病区",
      "updatedAt": "2025-07-14T08:30:00"
    }
  ]'
```

## 在院患者数据提交示例

```bash
curl -X PUT \
  http://localhost:8080/api/sync/patient/H001/up \
  -H 'Content-Type: application/json' \
  -d '[
    {
      "inpatientInfoId": "PUP001",
      "name": "王五",
      "idCard": "110101199003033456",
      "mobile": "13800138002",
      "sex": 1,
      "age": 45,
      "birthday": "1975-03-03",
      "hospitalizationNo": "HUP001",
      "inhospitalDiagnose": "肺炎",
      "deptId": 3,
      "sickbedNo": "3-103",
      "doctorId": "D003",
      "nurseId": "N003",
      "nurseLevel": 3,
      "inhospitalTime": "2025-06-20T14:30:00",
      "outhospitalTime": null,
      "status": 1,
      "category": "自费",
      "inpatientWard": "3病区",
      "updatedAt": "2025-07-14T09:00:00"
    },
    {
      "inpatientInfoId": "PUP002",
      "name": "赵六",
      "idCard": "110101199004044567",
      "mobile": "13800138003",
      "sex": 2,
      "age": 52,
      "birthday": "1968-04-04",
      "hospitalizationNo": "HUP002",
      "inhospitalDiagnose": "糖尿病",
      "deptId": 4,
      "sickbedNo": "4-104",
      "doctorId": "D004",
      "nurseId": "N004",
      "nurseLevel": 2,
      "inhospitalTime": "2025-07-01T16:45:00",
      "outhospitalTime": null,
      "status": 1,
      "category": "医保",
      "inpatientWard": "4病区",
      "updatedAt": "2025-07-14T09:15:00"
    }
  ]'
```

## 出院患者数据提交示例

```bash
curl -X PUT \
  http://localhost:8080/api/sync/patient/H001/out \
  -H 'Content-Type: application/json' \
  -d '[
    {
      "inpatientInfoId": "POUT001",
      "name": "钱七",
      "idCard": "110101199005055678",
      "mobile": "13800138004",
      "sex": 1,
      "age": 38,
      "birthday": "1982-05-05",
      "hospitalizationNo": "HOUT001",
      "inhospitalDiagnose": "阑尾炎",
      "deptId": 5,
      "sickbedNo": "5-105",
      "doctorId": "D005",
      "nurseId": "N005",
      "nurseLevel": 2,
      "inhospitalTime": "2025-05-15T10:30:00",
      "outhospitalTime": "2025-07-10T11:45:00",
      "status": 2,
      "category": "医保",
      "inpatientWard": "5病区",
      "updatedAt": "2025-07-10T11:45:00"
    },
    {
      "inpatientInfoId": "POUT002",
      "name": "孙八",
      "idCard": "110101199006066789",
      "mobile": "13800138005",
      "sex": 2,
      "age": 42,
      "birthday": "1978-06-06",
      "hospitalizationNo": "HOUT002",
      "inhospitalDiagnose": "胆结石",
      "deptId": 1,
      "sickbedNo": "1-106",
      "doctorId": "D001",
      "nurseId": "N001",
      "nurseLevel": 3,
      "inhospitalTime": "2025-06-01T09:15:00",
      "outhospitalTime": "2025-07-12T14:30:00",
      "status": 2,
      "category": "自费",
      "inpatientWard": "1病区",
      "updatedAt": "2025-07-12T14:30:00"
    }
  ]'
```

## 查询患者数据示例

查询入院患者：

```bash
curl -X GET http://localhost:8080/api/patients/H001/in
```

查询在院患者：

```bash
curl -X GET http://localhost:8080/api/patients/H001/up
```

查询出院患者：

```bash
curl -X GET http://localhost:8080/api/patients/H001/out
```

查询患者统计信息：

```bash
curl -X GET http://localhost:8080/api/monitor/patient-sync/stats/H001
```

## 注意事项

1. 确保每个患者都有唯一的 `inpatientInfoId`
2. 确保每个患者都有 `hospitalizationNo`(住院号) 和 `inhospitalTime`(入院时间)，这是生成唯一标识的必要条件
3. 入院患者和在院患者的状态应为 1（在院），出院患者的状态应为 2（出院）
4. 入院患者和在院患者的 `outhospitalTime` 应为 null
5. 出院患者必须同时有 `inhospitalTime` 和 `outhospitalTime` 