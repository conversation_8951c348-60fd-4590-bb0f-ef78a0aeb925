package com.sysgetway.core.example;

import com.alibaba.fastjson2.JSON;
import com.sysgetway.core.entity.Department;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.entity.User;
import com.sysgetway.core.utils.HospitalDataGenerator;
import com.sysgetway.core.utils.TestDataFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

/**
 * 医院数据同步示例
 * 
 * 这个类提供一个可以直接运行的main方法，用于演示医院数据的完整同步流程
 * 包括科室、医护人员和患者数据的同步
 */
public class HospitalDataSyncExample {
    
    // 服务器地址和端口
    private static final String SERVER_URL = "http://localhost:8080";
    
    /**
     * 主入口方法，用于演示完整的数据同步流程
     * @param args 命令行参数(不使用)
     */
    public static void main(String[] args) {
        try {
            System.out.println("=== 医院数据同步示例程序 ===");
            System.out.println("本程序将演示如何同步医院数据(科室、医护人员、患者)到医院数据同步网关系统");
            
            // 设置医院ID
            String hospitalId = "H001";
            System.out.println("\n使用医院ID: " + hospitalId);
            
            // 选择操作
            showMenu();
            Scanner scanner = new Scanner(System.in);
            int choice;
            do {
                System.out.print("请选择操作 (1-7): ");
                try {
                    choice = Integer.parseInt(scanner.nextLine().trim());
                } catch (NumberFormatException e) {
                    choice = 0;
                }
                
                switch (choice) {
                    case 1:
                        importAllHospitalData(hospitalId);
                        break;
                    case 2:
                        importDepartments(hospitalId);
                        break;
                    case 3:
                        importUsers(hospitalId);
                        break;
                    case 4:
                        importPatients(hospitalId);
                        break;
                    case 5:
                        queryData(hospitalId);
                        break;
                    case 6:
                        showDataStructure();
                        break;
                    case 7:
                        System.out.println("退出程序...");
                        break;
                    default:
                        System.out.println("无效选择，请重新输入");
                }
                
                if (choice > 0 && choice < 7) {
                    showMenu();
                }
            } while (choice != 7);
            
        } catch (Exception e) {
            System.err.println("程序执行异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 显示菜单选项
     */
    private static void showMenu() {
        System.out.println("\n=== 操作菜单 ===");
        System.out.println("1. 导入所有数据(科室、医护人员、患者)");
        System.out.println("2. 仅导入科室数据");
        System.out.println("3. 仅导入医护人员数据");
        System.out.println("4. 仅导入患者数据");
        System.out.println("5. 查询已导入的数据");
        System.out.println("6. 显示数据结构说明");
        System.out.println("7. 退出程序");
    }
    
    /**
     * 导入所有医院数据(综合接口)
     * 
     * @param hospitalId 医院ID
     */
    private static void importAllHospitalData(String hospitalId) {
        try {
            System.out.println("\n=== 开始导入所有医院数据 ===");
            
            // 准备数据
            int departmentCount = 5;  // 默认5个科室
            int userCount = 10;       // 默认10个医护人员
            int patientCount = 15;    // 默认15个患者
            
            // 询问用户是否要自定义数量
            Scanner scanner = new Scanner(System.in);
            System.out.print("是否要自定义数据量? (y/n): ");
            String answer = scanner.nextLine().trim().toLowerCase();
            
            if ("y".equals(answer) || "yes".equals(answer)) {
                System.out.print("请输入科室数量 (1-15): ");
                try {
                    departmentCount = Integer.parseInt(scanner.nextLine().trim());
                    departmentCount = Math.max(1, Math.min(15, departmentCount));
                } catch (NumberFormatException e) {
                    System.out.println("使用默认值: 5个科室");
                    departmentCount = 5;
                }
                
                System.out.print("请输入医护人员数量 (最少等于科室数量的2倍): ");
                try {
                    userCount = Integer.parseInt(scanner.nextLine().trim());
                    userCount = Math.max(departmentCount * 2, userCount); // 至少每个科室有一个医生和一个护士
                } catch (NumberFormatException e) {
                    System.out.println("使用默认值: " + (departmentCount * 2) + "个医护人员");
                    userCount = departmentCount * 2;
                }
                
                System.out.print("请输入患者数量: ");
                try {
                    patientCount = Integer.parseInt(scanner.nextLine().trim());
                    patientCount = Math.max(1, patientCount);
                } catch (NumberFormatException e) {
                    System.out.println("使用默认值: 15个患者");
                    patientCount = 15;
                }
            }
            
            System.out.println("\n生成数据量: 科室=" + departmentCount + 
                    ", 医护人员=" + userCount + ", 患者=" + patientCount);
            
            // 使用HospitalDataGenerator生成关联的医院数据
            Map<String, Object> dataMap = HospitalDataGenerator.generateCompleteHospitalData(
                    departmentCount, userCount, patientCount);
            
            // 打印生成的数据概要
            List<Department> departments = (List<Department>) dataMap.get("departments");
            List<User> users = (List<User>) dataMap.get("users");
            List<Patient> patients = (List<Patient>) dataMap.get("patients");
            
            System.out.println("已生成科室数据: " + departments.size() + " 条");
            System.out.println("已生成医护人员数据: " + users.size() + " 条");
            System.out.println("已生成患者数据: " + patients.size() + " 条");
            
            // 数据关联验证
            Map<Integer, Integer> deptUserCounts = new HashMap<>();
            for (User user : users) {
                deptUserCounts.put(user.getDeptId(), 
                        deptUserCounts.getOrDefault(user.getDeptId(), 0) + 1);
            }
            
            System.out.println("\n数据关联验证:");
            System.out.println("- 各科室医护人员分布:");
            for (Department dept : departments) {
                int userCount1 = deptUserCounts.getOrDefault(dept.getDepartmentId(), 0);
                System.out.println("  科室 [" + dept.getCode() + " - " + dept.getName() + 
                        "] 有 " + userCount1 + " 名医护人员");
            }
            
            // 统计各类型患者数量
            int inPatients = 0, upPatients = 0, outPatients = 0;
            for (Patient patient : patients) {
                if (patient.getInhospitalTime() != null && patient.getOuthospitalTime() == null) {
                    inPatients++;
                }
                if (patient.getStatus() != null && patient.getStatus() == 1) {
                    upPatients++;
                }
                if (patient.getStatus() != null && patient.getStatus() == 2) {
                    outPatients++;
                }
            }
            
            System.out.println("- 患者类型分布:");
            System.out.println("  入院患者: " + inPatients + " 名");
            System.out.println("  在院患者: " + upPatients + " 名");
            System.out.println("  出院患者: " + outPatients + " 名");
            
            // 发送请求
            String url = SERVER_URL + "/api/hospital/" + hospitalId + "/import";
            String jsonData = JSON.toJSONString(dataMap);
            
            System.out.println("\n是否查看将要发送的JSON数据? (y/n): ");
            answer = scanner.nextLine().trim().toLowerCase();
            if ("y".equals(answer) || "yes".equals(answer)) {
                System.out.println("\n将发送的JSON数据:");
                System.out.println(formatJson(jsonData));
            }
            
            System.out.println("\n发送请求到: " + url);
            System.out.println("数据大小: " + jsonData.length() + " 字节");
            
            String response = sendPostRequest(url, jsonData);
            System.out.println("\n服务器响应: \n" + formatJson(response));
            
            System.out.println("\n=== 医院数据导入完成 ===");
        } catch (Exception e) {
            System.err.println("导入所有医院数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 仅导入科室数据
     * 
     * @param hospitalId 医院ID
     */
    private static void importDepartments(String hospitalId) {
        try {
            System.out.println("\n=== 开始导入科室数据 ===");
            
            // 询问用户要生成的科室数量
            Scanner scanner = new Scanner(System.in);
            int count = 5; // 默认值
            System.out.print("请输入要生成的科室数量 (1-15): ");
            
            try {
                count = Integer.parseInt(scanner.nextLine().trim());
                count = Math.max(1, Math.min(15, count));
            } catch (NumberFormatException e) {
                System.out.println("使用默认值: 5个科室");
            }
            
            // 准备科室数据
            List<Department> departments = HospitalDataGenerator.generateDepartments(count);
            System.out.println("已生成科室数据: " + departments.size() + " 条");
            
            // 打印科室概要
            System.out.println("\n科室列表:");
            for (Department dept : departments) {
                System.out.println("- [" + dept.getDepartmentId() + "] " + 
                        dept.getCode() + " - " + dept.getName());
            }
            
            // 构建请求数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("departments", departments);
            
            // 发送请求
            String url = SERVER_URL + "/api/hospital/" + hospitalId + "/import";
            String jsonData = JSON.toJSONString(dataMap);
            
            System.out.println("\n发送请求到: " + url);
            String response = sendPostRequest(url, jsonData);
            System.out.println("\n服务器响应: \n" + formatJson(response));
            
            System.out.println("\n=== 科室数据导入完成 ===");
        } catch (Exception e) {
            System.err.println("导入科室数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 仅导入医护人员数据
     * 
     * @param hospitalId 医院ID
     */
    private static void importUsers(String hospitalId) {
        try {
            System.out.println("\n=== 开始导入医护人员数据 ===");
            
            // 首先需要科室数据来关联
            System.out.println("查询当前系统中的科室数据...");
            String deptUrl = SERVER_URL + "/api/data/" + hospitalId + "/department";
            String deptResponse = sendGetRequest(deptUrl);
            
            List<Department> departments;
            
            // 解析科室数据
            try {
                Map<String, Object> responseMap = JSON.parseObject(deptResponse);
                if (responseMap != null && responseMap.get("code").equals(200)) {
                    Object dataObj = responseMap.get("data");
                    if (dataObj != null) {
                        departments = JSON.parseArray(JSON.toJSONString(dataObj), Department.class);
                        System.out.println("已获取到 " + departments.size() + " 个科室数据");
                    } else {
                        System.out.println("系统中没有科室数据，将生成默认科室数据");
                        departments = HospitalDataGenerator.generateDepartments(5);
                    }
                } else {
                    System.out.println("获取科室数据失败，将生成默认科室数据");
                    departments = HospitalDataGenerator.generateDepartments(5);
                }
            } catch (Exception e) {
                System.out.println("解析科室数据失败，将生成默认科室数据: " + e.getMessage());
                departments = HospitalDataGenerator.generateDepartments(5);
            }
            
            // 询问用户要生成的医护人员数量
            Scanner scanner = new Scanner(System.in);
            int count = departments.size() * 2; // 默认每个科室2个医护人员
            System.out.print("请输入要生成的医护人员数量 (最少为科室数量的2倍 " + count + "): ");
            
            try {
                int userInput = Integer.parseInt(scanner.nextLine().trim());
                count = Math.max(count, userInput);
            } catch (NumberFormatException e) {
                System.out.println("使用默认值: " + count + " 名医护人员");
            }
            
            // 准备医护人员数据
            List<User> users = HospitalDataGenerator.generateUsers(count, departments);
            System.out.println("已生成医护人员数据: " + users.size() + " 条");
            
            // 打印医护人员概要
            System.out.println("\n医护人员列表 (前5条):");
            for (int i = 0; i < Math.min(5, users.size()); i++) {
                User user = users.get(i);
                System.out.println("- [" + user.getUserName() + "] " + user.getName() + 
                        " (" + (user.getRoleId() == 1 ? "医生" : "护士") + 
                        "), 科室ID: " + user.getDeptId() + 
                        ", 病区: " + user.getInpatientWard());
            }
            if (users.size() > 5) {
                System.out.println("... 共 " + users.size() + " 条数据");
            }
            
            // 构建请求数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("users", users);
            
            // 是否同时更新科室数据
            System.out.print("\n是否同时更新科室数据? (y/n): ");
            String answer = scanner.nextLine().trim().toLowerCase();
            if ("y".equals(answer) || "yes".equals(answer)) {
                dataMap.put("departments", departments);
                System.out.println("将同时更新 " + departments.size() + " 个科室数据");
            }
            
            // 发送请求
            String url = SERVER_URL + "/api/hospital/" + hospitalId + "/import";
            String jsonData = JSON.toJSONString(dataMap);
            
            System.out.println("\n发送请求到: " + url);
            String response = sendPostRequest(url, jsonData);
            System.out.println("\n服务器响应: \n" + formatJson(response));
            
            System.out.println("\n=== 医护人员数据导入完成 ===");
        } catch (Exception e) {
            System.err.println("导入医护人员数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 仅导入患者数据
     * 
     * @param hospitalId 医院ID
     */
    private static void importPatients(String hospitalId) {
        try {
            System.out.println("\n=== 开始导入患者数据 ===");
            
            // 首先获取科室和医护人员数据以建立关联
            System.out.println("查询当前系统中的科室和医护人员数据...");
            
            // 获取科室数据
            String deptUrl = SERVER_URL + "/api/data/" + hospitalId + "/department";
            String deptResponse = sendGetRequest(deptUrl);
            List<Department> departments = new ArrayList<>();
            
            try {
                Map<String, Object> responseMap = JSON.parseObject(deptResponse);
                if (responseMap != null && responseMap.get("code").equals(200)) {
                    Object dataObj = responseMap.get("data");
                    if (dataObj != null) {
                        departments = JSON.parseArray(JSON.toJSONString(dataObj), Department.class);
                        System.out.println("已获取到 " + departments.size() + " 个科室数据");
                    }
                }
            } catch (Exception e) {
                System.out.println("解析科室数据失败: " + e.getMessage());
            }
            
            // 获取医护人员数据
            String userUrl = SERVER_URL + "/api/data/" + hospitalId + "/user";
            String userResponse = sendGetRequest(userUrl);
            List<User> users = new ArrayList<>();
            
            try {
                Map<String, Object> responseMap = JSON.parseObject(userResponse);
                if (responseMap != null && responseMap.get("code").equals(200)) {
                    Object dataObj = responseMap.get("data");
                    if (dataObj != null) {
                        users = JSON.parseArray(JSON.toJSONString(dataObj), User.class);
                        System.out.println("已获取到 " + users.size() + " 个医护人员数据");
                    }
                }
            } catch (Exception e) {
                System.out.println("解析医护人员数据失败: " + e.getMessage());
            }
            
            // 如果没有获取到数据，生成默认数据
            if (departments.isEmpty()) {
                System.out.println("没有获取到科室数据，将生成默认科室数据");
                departments = HospitalDataGenerator.generateDepartments(5);
            }
            
            if (users.isEmpty() && !departments.isEmpty()) {
                System.out.println("没有获取到医护人员数据，将生成默认医护人员数据");
                users = HospitalDataGenerator.generateUsers(departments.size() * 2, departments);
            }
            
            // 询问用户要生成的患者数量
            Scanner scanner = new Scanner(System.in);
            int count = 15; // 默认15个患者
            System.out.print("请输入要生成的患者数量: ");
            
            try {
                count = Integer.parseInt(scanner.nextLine().trim());
                count = Math.max(1, count);
            } catch (NumberFormatException e) {
                System.out.println("使用默认值: 15个患者");
            }
            
            // 准备患者数据
            List<Patient> patients = HospitalDataGenerator.generatePatients(count, departments, users);
            System.out.println("已生成患者数据: " + patients.size() + " 条");
            
            // 打印患者概要
            System.out.println("\n患者列表 (前5条):");
            for (int i = 0; i < Math.min(5, patients.size()); i++) {
                Patient patient = patients.get(i);
                String status = patient.getStatus() == 1 ? "在院" : "出院";
                String inTime = patient.getInhospitalTime() != null ? 
                        patient.getInhospitalTime().toString() : "无";
                String outTime = patient.getOuthospitalTime() != null ? 
                        patient.getOuthospitalTime().toString() : "无";
                
                System.out.println("- [" + patient.getInpatientInfoId() + "] " + patient.getName() + 
                        " (" + status + "), 科室ID: " + patient.getDeptId() + 
                        ", 入院: " + inTime + 
                        ", 出院: " + outTime);
            }
            if (patients.size() > 5) {
                System.out.println("... 共 " + patients.size() + " 条数据");
            }
            
            // 构建请求数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("patients", patients);
            
            // 询问是否同时更新科室和医护人员数据
            System.out.print("\n是否同时更新科室和医护人员数据? (y/n): ");
            String answer = scanner.nextLine().trim().toLowerCase();
            if ("y".equals(answer) || "yes".equals(answer)) {
                dataMap.put("departments", departments);
                dataMap.put("users", users);
                System.out.println("将同时更新 " + departments.size() + " 个科室数据和 " + 
                        users.size() + " 个医护人员数据");
            }
            
            // 发送请求
            String url = SERVER_URL + "/api/hospital/" + hospitalId + "/import";
            String jsonData = JSON.toJSONString(dataMap);
            
            System.out.println("\n发送请求到: " + url);
            String response = sendPostRequest(url, jsonData);
            System.out.println("\n服务器响应: \n" + formatJson(response));
            
            System.out.println("\n=== 患者数据导入完成 ===");
        } catch (Exception e) {
            System.err.println("导入患者数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 查询已导入的数据
     * 
     * @param hospitalId 医院ID
     */
    private static void queryData(String hospitalId) {
        System.out.println("\n=== 查询数据选项 ===");
        System.out.println("1. 查询科室数据");
        System.out.println("2. 查询医护人员数据");
        System.out.println("3. 查询患者数据");
        System.out.println("4. 查询患者分类统计");
        System.out.println("5. 返回上级菜单");
        
        Scanner scanner = new Scanner(System.in);
        System.out.print("请选择 (1-5): ");
        int choice;
        
        try {
            choice = Integer.parseInt(scanner.nextLine().trim());
        } catch (NumberFormatException e) {
            choice = 0;
        }
        
        try {
            String url;
            String response;
            
            switch (choice) {
                case 1:
                    url = SERVER_URL + "/api/data/" + hospitalId + "/department";
                    System.out.println("查询科室数据: " + url);
                    response = sendGetRequest(url);
                    System.out.println("\n响应结果: \n" + formatJson(response));
                    break;
                    
                case 2:
                    url = SERVER_URL + "/api/data/" + hospitalId + "/user";
                    System.out.println("查询医护人员数据: " + url);
                    response = sendGetRequest(url);
                    System.out.println("\n响应结果: \n" + formatJson(response));
                    break;
                    
                case 3:
                    System.out.println("\n=== 患者数据查询选项 ===");
                    System.out.println("1. 查询所有患者");
                    System.out.println("2. 查询入院患者(in)");
                    System.out.println("3. 查询在院患者(up)");
                    System.out.println("4. 查询出院患者(out)");
                    System.out.print("请选择 (1-4): ");
                    
                    int patientChoice;
                    try {
                        patientChoice = Integer.parseInt(scanner.nextLine().trim());
                    } catch (NumberFormatException e) {
                        patientChoice = 1;
                    }
                    
                    switch (patientChoice) {
                        case 2:
                            url = SERVER_URL + "/api/patients/" + hospitalId + "/in";
                            System.out.println("查询入院患者: " + url);
                            break;
                        case 3:
                            url = SERVER_URL + "/api/patients/" + hospitalId + "/up";
                            System.out.println("查询在院患者: " + url);
                            break;
                        case 4:
                            url = SERVER_URL + "/api/patients/" + hospitalId + "/out";
                            System.out.println("查询出院患者: " + url);
                            break;
                        case 1:
                        default:
                            url = SERVER_URL + "/api/data/" + hospitalId + "/patient";
                            System.out.println("查询所有患者: " + url);
                            break;
                    }
                    
                    response = sendGetRequest(url);
                    System.out.println("\n响应结果: \n" + formatJson(response));
                    break;
                    
                case 4:
                    url = SERVER_URL + "/api/patients/" + hospitalId + "/stats";
                    System.out.println("查询患者分类统计: " + url);
                    response = sendGetRequest(url);
                    System.out.println("\n响应结果: \n" + formatJson(response));
                    break;
                    
                case 5:
                    return;
                    
                default:
                    System.out.println("无效选择，返回上级菜单");
            }
        } catch (Exception e) {
            System.err.println("查询数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 显示数据结构说明
     */
    private static void showDataStructure() {
        System.out.println("\n=== 数据结构说明 ===");
        
        System.out.println("\n1. 科室数据结构(Department):");
        System.out.println("   - departmentId: 科室ID");
        System.out.println("   - code: 科室编码");
        System.out.println("   - name: 科室名称");
        System.out.println("   - updatedAt: 更新时间");
        
        System.out.println("\n2. 医护人员数据结构(User):");
        System.out.println("   - userName: 用户名(工号)");
        System.out.println("   - name: 姓名");
        System.out.println("   - sex: 性别(1-男, 2-女)");
        System.out.println("   - roleId: 角色ID(1-医生, 2-护士)");
        System.out.println("   - deptId: 科室ID");
        System.out.println("   - inpatientWard: 病区(多个病区用逗号分隔)");
        System.out.println("   - mobile: 手机号");
        System.out.println("   - updatedAt: 更新时间");
        
        System.out.println("\n3. 患者数据结构(Patient):");
        System.out.println("   - inpatientInfoId: 信息唯一ID");
        System.out.println("   - name: 姓名");
        System.out.println("   - idCard: 身份证号");
        System.out.println("   - mobile: 手机号码");
        System.out.println("   - sex: 性别(1-男, 2-女)");
        System.out.println("   - age: 年龄");
        System.out.println("   - birthday: 生日");
        System.out.println("   - hospitalizationNo: 住院号");
        System.out.println("   - inhospitalDiagnose: 入院诊断");
        System.out.println("   - deptId: 科室ID");
        System.out.println("   - sickbedNo: 床位号");
        System.out.println("   - doctorId: 责任医生ID");
        System.out.println("   - nurseId: 责任护士ID");
        System.out.println("   - nurseLevel: 护理级别(1-一级, 2-二级, 3-三级, 4-特级)");
        System.out.println("   - inhospitalTime: 入院时间");
        System.out.println("   - outhospitalTime: 出院时间");
        System.out.println("   - status: 患者状态(1-在院, 2-出院)");
        System.out.println("   - category: 患者类别(医保、自费等)");
        System.out.println("   - inpatientWard: 病区");
        System.out.println("   - updatedAt: 更新时间");
        
        System.out.println("\n4. 患者分类:");
        System.out.println("   - 入院患者(in): 有入院时间，无出院时间");
        System.out.println("   - 在院患者(up): 状态为在院(1)");
        System.out.println("   - 出院患者(out): 状态为出院(2)");
        
        System.out.println("\n按回车键继续...");
        new Scanner(System.in).nextLine();
    }
    
    /**
     * 发送POST请求
     * 
     * @param urlStr 请求URL
     * @param jsonData JSON格式的请求数据
     * @return 响应内容
     * @throws IOException 如果发生IO异常
     */
    private static String sendPostRequest(String urlStr, String jsonData) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setDoOutput(true);
        
        // 发送数据
        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        // 读取响应
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                response.append(line);
            }
        } catch (IOException e) {
            // 处理错误响应
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
            }
        }
        
        // 断开连接
        conn.disconnect();
        
        return response.toString();
    }
    
    /**
     * 发送GET请求
     * 
     * @param urlStr 请求URL
     * @return 响应内容
     * @throws IOException 如果发生IO异常
     */
    private static String sendGetRequest(String urlStr) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        
        // 读取响应
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                response.append(line);
            }
        } catch (IOException e) {
            // 处理错误响应
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
            }
        }
        
        // 断开连接
        conn.disconnect();
        
        return response.toString();
    }
    
    /**
     * 格式化JSON字符串(简化版)
     * 
     * @param json JSON字符串
     * @return 格式化后的字符串
     */
    private static String formatJson(String json) {
        // 简单的实现，实际项目中可以使用更复杂的格式化工具
        return json.replace(",", ",\n  ")
                   .replace("{", "{\n  ")
                   .replace("}", "\n}");
    }
} 