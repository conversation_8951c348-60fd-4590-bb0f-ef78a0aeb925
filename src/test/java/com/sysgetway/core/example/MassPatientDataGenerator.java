package com.sysgetway.core.example;

import com.sysgetway.core.entity.Patient;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 大批量患者数据生成器
 * 
 * 用于生成大量患者数据并输出为JSON格式
 */
public class MassPatientDataGenerator {
    
    private static final Random random = new Random();
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    
    // 患者类型常量
    private static final String TYPE_IN = "in";  // 入院患者
    private static final String TYPE_UP = "up";  // 在院患者
    private static final String TYPE_OUT = "out"; // 出院患者
    
    // 每批处理的患者数量
    private static final int BATCH_SIZE = 1000;
    
    public static void main(String[] args) {
        try {
            // 默认生成50000条患者数据
            int patientCount = 50000;
            
            // 检查是否有命令行参数指定数量
            if (args.length > 0) {
                try {
                    patientCount = Integer.parseInt(args[0]);
                } catch (NumberFormatException e) {
                    System.err.println("无效的数量参数，使用默认值50000");
                }
            }
            
            String outputFile = "patient_data_" + patientCount + ".json";
            
            System.out.println("开始生成" + patientCount + "条患者数据...");
            long startTime = System.currentTimeMillis();
            
            // 生成并保存患者数据（分批处理）
            saveToFileInBatches(patientCount, outputFile);
            
            long endTime = System.currentTimeMillis();
            System.out.println("数据生成完成，共耗时: " + (endTime - startTime) / 1000 + " 秒");
            System.out.println("数据已保存到文件: " + outputFile);
            
            // 输出示例数据
            System.out.println("\n示例数据（前3条）:");
            List<Patient> samplePatients = generateMassPatientData(3);
            System.out.println(patientListToJson(samplePatients, true));
            
        } catch (Exception e) {
            System.err.println("生成数据时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分批生成并保存患者数据到文件
     * 
     * @param totalCount 总患者数量
     * @param fileName 输出文件名
     * @throws IOException 如果文件操作失败
     */
    private static void saveToFileInBatches(int totalCount, String fileName) throws IOException {
        // 计算需要处理的批次数
        int batchCount = (int) Math.ceil((double) totalCount / BATCH_SIZE);
        
        try (FileWriter writer = new FileWriter(fileName)) {
            // 写入JSON数组开始标记和格式化
            writer.write("[\n");
            
            // 按比例分配不同类型的患者
            int inPatientCount = totalCount / 10;
            int upPatientCount = (totalCount * 6) / 10;
            int outPatientCount = totalCount - inPatientCount - upPatientCount;
            
            System.out.println("计划生成患者数据分布:");
            System.out.println("- 入院患者: " + inPatientCount + " 条");
            System.out.println("- 在院患者: " + upPatientCount + " 条");
            System.out.println("- 出院患者: " + outPatientCount + " 条");
            System.out.println("将分" + batchCount + "批处理，每批" + BATCH_SIZE + "条数据");
            
            // 处理入院患者
            processPatientBatches(writer, TYPE_IN, inPatientCount, 0, false, upPatientCount > 0, outPatientCount > 0);
            
            // 处理在院患者
            processPatientBatches(writer, TYPE_UP, upPatientCount, 0, inPatientCount > 0, false, outPatientCount > 0);
            
            // 处理出院患者
            processPatientBatches(writer, TYPE_OUT, outPatientCount, 0, inPatientCount > 0 || upPatientCount > 0, false, false);
            
            // 写入JSON数组结束标记和格式化
            writer.write("\n]");
        }
    }
    
    /**
     * 分批处理指定类型的患者数据
     * 
     * @param writer 文件写入器
     * @param type 患者类型
     * @param totalCount 该类型的总患者数
     * @param startIndex 起始索引
     * @param needCommaPrefix 是否需要在第一个元素前添加逗号
     * @param hasNextTypeUp 是否还有在院患者需要处理
     * @param hasNextTypeOut 是否还有出院患者需要处理
     * @throws IOException 如果写入失败
     */
    private static void processPatientBatches(FileWriter writer, String type, int totalCount, int startIndex, 
                                             boolean needCommaPrefix, boolean hasNextTypeUp, boolean hasNextTypeOut) throws IOException {
        int processedCount = 0;
        
        while (processedCount < totalCount) {
            // 计算当前批次需要处理的数量
            int currentBatchSize = Math.min(BATCH_SIZE, totalCount - processedCount);
            
            // 生成当前批次的患者数据
            List<Patient> batchPatients = new ArrayList<>(currentBatchSize);
            for (int i = 0; i < currentBatchSize; i++) {
                batchPatients.add(generatePatient(type, startIndex + processedCount + i));
            }
            
            // 转换为格式化的JSON并写入文件
            for (int i = 0; i < batchPatients.size(); i++) {
                Patient patient = batchPatients.get(i);
                
                // 添加逗号分隔符
                if ((needCommaPrefix && processedCount == 0 && i == 0) || 
                    (processedCount > 0 || i > 0)) {
                    writer.write(",\n");
                }
                
                // 写入缩进
                writer.write("    ");
                
                // 写入患者对象的JSON
                writer.write(patientToJson(patient, false));
            }
            
            // 更新已处理数量
            processedCount += currentBatchSize;
            
            System.out.println("已处理 " + type + " 类型患者: " + processedCount + "/" + totalCount);
        }
    }
    
    /**
     * 将患者列表转换为JSON字符串
     * 
     * @param patients 患者列表
     * @param prettyPrint 是否格式化输出
     * @return JSON字符串
     */
    private static String patientListToJson(List<Patient> patients, boolean prettyPrint) {
        StringBuilder json = new StringBuilder();
        json.append("[");
        
        for (int i = 0; i < patients.size(); i++) {
            if (i > 0) {
                json.append(",");
                if (prettyPrint) {
                    json.append("\n");
                }
            }
            
            Patient patient = patients.get(i);
            json.append(patientToJson(patient, prettyPrint));
        }
        
        json.append("]");
        return json.toString();
    }
    
    /**
     * 将单个患者对象转换为JSON字符串
     * 
     * @param patient 患者对象
     * @param prettyPrint 是否格式化输出
     * @return JSON字符串
     */
    private static String patientToJson(Patient patient, boolean prettyPrint) {
        StringBuilder json = new StringBuilder();
        String indent = prettyPrint ? "  " : "";
        String newline = prettyPrint ? "\n" : "";
        
        json.append("{");
        if (prettyPrint) json.append("\n");
        
        appendJsonField(json, "inpatientInfoId", patient.getInpatientInfoId(), true, indent, newline);
        appendJsonField(json, "name", patient.getName(), true, indent, newline);
        appendJsonField(json, "idCard", patient.getIdCard(), true, indent, newline);
        appendJsonField(json, "mobile", patient.getMobile(), true, indent, newline);
        appendJsonField(json, "sex", patient.getSex(), false, indent, newline);
        appendJsonField(json, "age", patient.getAge(), false, indent, newline);
        appendJsonField(json, "birthday", patient.getBirthday(), true, indent, newline);
        appendJsonField(json, "hospitalizationNo", patient.getHospitalizationNo(), true, indent, newline);
        appendJsonField(json, "inhospitalDiagnose", patient.getInhospitalDiagnose(), true, indent, newline);
        appendJsonField(json, "deptId", patient.getDeptId(), false, indent, newline);
        appendJsonField(json, "sickbedNo", patient.getSickbedNo(), true, indent, newline);
        appendJsonField(json, "doctorId", patient.getDoctorId(), true, indent, newline);
        appendJsonField(json, "nurseId", patient.getNurseId(), true, indent, newline);
        appendJsonField(json, "nurseLevel", patient.getNurseLevel(), false, indent, newline);
        appendJsonField(json, "inhospitalTime", patient.getInhospitalTime(), true, indent, newline);
        
        // 出院时间可能为null
        if (patient.getOuthospitalTime() != null) {
            appendJsonField(json, "outhospitalTime", patient.getOuthospitalTime(), true, indent, newline);
        } else {
            json.append(indent).append("\"outhospitalTime\":null,").append(newline);
        }
        
        appendJsonField(json, "status", patient.getStatus(), false, indent, newline);
        appendJsonField(json, "category", patient.getCategory(), true, indent, newline);
        appendJsonField(json, "inpatientWard", patient.getInpatientWard(), true, indent, newline);
        
        // 最后一个字段不需要逗号
        if (patient.getUpdatedAt() != null) {
            json.append(indent).append("\"updatedAt\":\"").append(patient.getUpdatedAt().format(formatter)).append("\"");
        } else {
            json.append(indent).append("\"updatedAt\":null");
        }
        
        if (prettyPrint) json.append("\n");
        json.append("}");
        
        return json.toString();
    }
    
    /**
     * 添加JSON字段
     * 
     * @param json StringBuilder对象
     * @param fieldName 字段名
     * @param value 字段值
     * @param isString 是否为字符串类型
     * @param indent 缩进
     * @param newline 换行符
     */
    private static void appendJsonField(StringBuilder json, String fieldName, Object value, boolean isString, String indent, String newline) {
        json.append(indent).append("\"").append(fieldName).append("\":");
        
        if (value == null) {
            json.append("null,").append(newline);
        } else if (isString) {
            json.append("\"").append(value).append("\",").append(newline);
        } else {
            json.append(value).append(",").append(newline);
        }
    }
    
    /**
     * 生成大量患者数据
     * 
     * @param count 生成的患者数量
     * @return 患者列表
     */
    private static List<Patient> generateMassPatientData(int count) {
        List<Patient> patients = new ArrayList<>(count);
        
        // 按比例分配不同类型的患者
        // 入院患者约占10%，在院患者约占60%，出院患者约占30%
        int inPatientCount = count / 10;
        int upPatientCount = (count * 6) / 10;
        int outPatientCount = count - inPatientCount - upPatientCount;
        
        // 生成入院患者
        for (int i = 0; i < inPatientCount; i++) {
            patients.add(generatePatient(TYPE_IN, i));
        }
        
        // 生成在院患者
        for (int i = 0; i < upPatientCount; i++) {
            patients.add(generatePatient(TYPE_UP, i));
        }
        
        // 生成出院患者
        for (int i = 0; i < outPatientCount; i++) {
            patients.add(generatePatient(TYPE_OUT, i));
        }
        
        return patients;
    }
    
    /**
     * 生成单个患者数据
     * 
     * @param type 患者类型
     * @param index 序号
     * @return 患者对象
     */
    private static Patient generatePatient(String type, int index) {
        Patient patient = new Patient();
        
        // 唯一ID
        patient.setInpatientInfoId("P" + type.toUpperCase() + String.format("%05d", index));
        
        // 基本信息
        String[] surnames = {"张", "李", "王", "赵", "刘", "陈", "杨", "黄", "周", "吴", "郑", "孙", "马", "朱", "胡", "林", "郭", "何", "高", "罗"};
        String[] names = {"伟", "芳", "娜", "秀英", "敏", "静", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞", "平", "刚", "桂英"};
        patient.setName(surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)]);
        
        // 身份证号
        patient.setIdCard(generateIdCard());
        
        // 性别 (1:男, 2:女)
        int sex = random.nextInt(2) + 1;
        patient.setSex(sex);
        
        // 手机号
        patient.setMobile("1" + (3 + random.nextInt(6)) + random.nextInt(10) + String.format("%08d", random.nextInt(100000000)));
        
        // 年龄和生日
        int age = 20 + random.nextInt(60); // 20-79岁
        patient.setAge(age);
        patient.setBirthday(LocalDate.now().minusYears(age).minusDays(random.nextInt(365)));
        
        // 住院号 (必须有，用于生成唯一标识)
        patient.setHospitalizationNo("H" + type.toUpperCase() + String.format("%05d", index));
        
        // 科室和床位信息
        int deptId = random.nextInt(10) + 1;
        patient.setDeptId(deptId);
        patient.setSickbedNo(deptId + "-" + String.format("%03d", random.nextInt(100) + 1));
        patient.setInpatientWard(deptId + "病区");
        
        // 诊断信息
        String[] diagnoses = {
            "高血压", "糖尿病", "冠心病", "肺炎", "胃炎", "肝炎", "肾炎", 
            "骨折", "脑梗塞", "心肌梗塞", "哮喘", "贫血", "肿瘤", "结石",
            "关节炎", "白内障", "青光眼", "中风", "心力衰竭", "胆囊炎",
            "肠炎", "支气管炎", "胃溃疡", "十二指肠溃疡", "痛风", "风湿病",
            "甲状腺疾病", "帕金森病", "癫痫", "抑郁症", "焦虑症", "失眠症"
        };
        patient.setInhospitalDiagnose(diagnoses[random.nextInt(diagnoses.length)]);
        
        // 医护人员信息
        patient.setDoctorId("D" + String.format("%03d", deptId * 10 + random.nextInt(5)));
        patient.setNurseId("N" + String.format("%03d", deptId * 10 + random.nextInt(8)));
        patient.setNurseLevel(random.nextInt(4) + 1); // 1-4级
        
        // 患者类别
        String[] categories = {"医保", "自费", "公费", "商业保险", "农村合作医疗", "城镇职工医保", "城镇居民医保"};
        patient.setCategory(categories[random.nextInt(categories.length)]);
        
        // 根据患者类型设置入院和出院时间
        LocalDateTime now = LocalDateTime.now();
        
        if (TYPE_IN.equals(type)) {
            // 入院患者：最近24小时内入院，无出院时间
            LocalDateTime inhospitalTime = now.minusHours(random.nextInt(24) + 1);
            patient.setInhospitalTime(inhospitalTime);
            patient.setOuthospitalTime(null);
            patient.setStatus(1); // 在院
        } else if (TYPE_UP.equals(type)) {
            // 在院患者：1-30天前入院，无出院时间
            LocalDateTime inhospitalTime = now.minusDays(random.nextInt(30) + 1);
            patient.setInhospitalTime(inhospitalTime);
            patient.setOuthospitalTime(null);
            patient.setStatus(1); // 在院
        } else {
            // 出院患者：30-90天前入院，1-20天前出院
            LocalDateTime inhospitalTime = now.minusDays(30 + random.nextInt(60));
            LocalDateTime outhospitalTime = now.minusDays(random.nextInt(20) + 1);
            
            // 确保出院时间晚于入院时间
            if (outhospitalTime.isBefore(inhospitalTime)) {
                outhospitalTime = inhospitalTime.plusDays(random.nextInt(30) + 1);
            }
            
            patient.setInhospitalTime(inhospitalTime);
            patient.setOuthospitalTime(outhospitalTime);
            patient.setStatus(2); // 出院
        }
        
        // 更新时间
        patient.setUpdatedAt(LocalDateTime.now());
        
        return patient;
    }
    
    /**
     * 生成随机身份证号
     */
    private static String generateIdCard() {
        // 地区码 (随机选择一个有效的地区码)
        String[] areaCodes = {
            "110101", "110102", "110105", "110106", "110107", "110108", "110109", "110111", // 北京
            "310101", "310104", "310105", "310106", "310107", "310109", "310110", "310112", // 上海
            "440103", "440104", "440105", "440106", "440111", "440112", "440113", "440114", // 广州
            "440303", "440304", "440305", "440306", "440307", "440308", // 深圳
            "330102", "330103", "330104", "330105", "330106", "330108", "330109", "330110", // 杭州
            "320102", "320104", "320105", "320106", "320111", "320113", "320114", "320115"  // 南京
        };
        String areaCode = areaCodes[random.nextInt(areaCodes.length)];
        
        // 出生日期 (1940-01-01 到 2000-12-31)
        int year = 1940 + random.nextInt(61); // 1940-2000
        int month = random.nextInt(12) + 1;
        int day = random.nextInt(28) + 1; // 简化处理，避免处理大月小月
        
        String birthDate = String.format("%04d%02d%02d", year, month, day);
        
        // 顺序码 (3位数字)
        String sequenceCode = String.format("%03d", random.nextInt(1000));
        
        // 校验码 (简化处理，随机生成)
        char[] checkCodeChars = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'X'};
        String checkCode = String.valueOf(checkCodeChars[random.nextInt(checkCodeChars.length)]);
        
        return areaCode + birthDate + sequenceCode + checkCode;
    }
    
    /**
     * 将数据保存到文件
     * 
     * @param data 要保存的数据
     * @param fileName 文件名
     * @throws IOException 如果保存失败
     */
    private static void saveToFile(String data, String fileName) throws IOException {
        try (FileWriter writer = new FileWriter(fileName)) {
            writer.write(data);
        }
    }
} 