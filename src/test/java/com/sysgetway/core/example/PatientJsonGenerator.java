package com.sysgetway.core.example;

import com.alibaba.fastjson2.JSON;
import com.sysgetway.core.entity.Patient;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 患者JSON生成器
 * 
 * 直接生成患者JSON数据，方便复制使用
 */
public class PatientJsonGenerator {
    
    private static final Random random = new Random();
    
    public static void main(String[] args) {
        try {
            // 默认生成20000条患者数据
            int count = 20000;
            
            // 检查是否有命令行参数指定数量
            if (args.length > 0) {
                try {
                    count = Integer.parseInt(args[0]);
                } catch (NumberFormatException e) {
                    System.err.println("无效的数量参数，使用默认值20000");
                }
            }
            
            System.out.println("正在生成 " + count + " 条患者数据...");
            long startTime = System.currentTimeMillis();
            
            // 生成患者数据
            List<Patient> patients = generatePatients(count);
            
            // 将数据转换为JSON并输出
            String jsonData = JSON.toJSONString(patients);
            System.out.println(jsonData);
            
            long endTime = System.currentTimeMillis();
            System.err.println("\n数据生成完成，共生成 " + patients.size() + " 条患者数据，耗时: " + (endTime - startTime) / 1000 + " 秒");
            
        } catch (Exception e) {
            System.err.println("生成数据时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
         /**
      * 生成患者数据
      * 
      * @param count 生成的患者数量
      * @return 患者列表
      */
     private static List<Patient> generatePatients(int count) {
         List<Patient> patients = new ArrayList<>(count);
         
         // 预先定义更多的数据变量，增加数据多样性
         String[] surnames = {"张", "李", "王", "赵", "刘", "陈", "杨", "黄", "周", "吴", "郑", "孙", "马", "朱", "胡", "林", "郭", "何", "高", "罗"};
         String[] names = {"伟", "芳", "娜", "秀英", "敏", "静", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞", "平", "刚", "桂英"};
         String[] diagnoses = {
             "高血压", "糖尿病", "冠心病", "肺炎", "胃炎", "肝炎", "肾炎", 
             "骨折", "脑梗塞", "心肌梗塞", "哮喘", "贫血", "肿瘤", "结石",
             "关节炎", "白内障", "青光眼", "中风", "心力衰竭", "胆囊炎",
             "肠炎", "支气管炎", "胃溃疡", "十二指肠溃疡", "痛风", "风湿病"
         };
         String[] categories = {"医保", "自费", "公费", "商业保险", "农村合作医疗", "城镇职工医保", "城镇居民医保"};
         
         // 按比例分配不同类型的患者
         // 入院患者约占10%，在院患者约占60%，出院患者约占30%
         int inPatientCount = count / 10;
         int upPatientCount = (count * 6) / 10;
         int outPatientCount = count - inPatientCount - upPatientCount;
         
         LocalDateTime now = LocalDateTime.now();
         
         // 生成入院患者
         for (int i = 0; i < inPatientCount; i++) {
             Patient patient = new Patient();
             
             // 唯一ID
             patient.setInpatientInfoId("PIN" + String.format("%05d", i + 1));
             
             // 基本信息
             patient.setName(surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)]);
             patient.setIdCard(generateIdCard());
             patient.setSex(random.nextInt(2) + 1);
             patient.setMobile("1" + (3 + random.nextInt(6)) + random.nextInt(10) + String.format("%08d", random.nextInt(100000000)));
             
             // 年龄和生日
             int age = 20 + random.nextInt(60);
             patient.setAge(age);
             patient.setBirthday(LocalDate.now().minusYears(age).minusDays(random.nextInt(365)));
             
             // 住院号
             patient.setHospitalizationNo("HIN" + String.format("%05d", i + 1));
             
             // 科室和床位信息
             int deptId = random.nextInt(10) + 1;
             patient.setDeptId(deptId);
             patient.setSickbedNo(deptId + "-" + String.format("%03d", random.nextInt(100) + 1));
             patient.setInpatientWard(deptId + "病区");
             
             // 诊断信息
             patient.setInhospitalDiagnose(diagnoses[random.nextInt(diagnoses.length)]);
             
             // 医护人员信息
             patient.setDoctorId("D" + String.format("%03d", deptId * 10 + random.nextInt(5)));
             patient.setNurseId("N" + String.format("%03d", deptId * 10 + random.nextInt(8)));
             patient.setNurseLevel(random.nextInt(4) + 1);
             
             // 患者类别
             patient.setCategory(categories[random.nextInt(categories.length)]);
             
             // 入院患者特有属性：最近24小时内入院，无出院时间
             LocalDateTime inhospitalTime = now.minusHours(random.nextInt(24) + 1);
             patient.setInhospitalTime(inhospitalTime);
             patient.setOuthospitalTime(null);
             patient.setStatus(1); // 在院
             
             // 更新时间
             patient.setUpdatedAt(now);
             
             patients.add(patient);
         }
         
         // 生成在院患者
         for (int i = 0; i < upPatientCount; i++) {
             Patient patient = new Patient();
             
             // 唯一ID
             patient.setInpatientInfoId("PUP" + String.format("%05d", i + 1));
             
             // 基本信息
             patient.setName(surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)]);
             patient.setIdCard(generateIdCard());
             patient.setSex(random.nextInt(2) + 1);
             patient.setMobile("1" + (3 + random.nextInt(6)) + random.nextInt(10) + String.format("%08d", random.nextInt(100000000)));
             
             // 年龄和生日
             int age = 20 + random.nextInt(60);
             patient.setAge(age);
             patient.setBirthday(LocalDate.now().minusYears(age).minusDays(random.nextInt(365)));
             
             // 住院号
             patient.setHospitalizationNo("HUP" + String.format("%05d", i + 1));
             
             // 科室和床位信息
             int deptId = random.nextInt(10) + 1;
             patient.setDeptId(deptId);
             patient.setSickbedNo(deptId + "-" + String.format("%03d", random.nextInt(100) + 1));
             patient.setInpatientWard(deptId + "病区");
             
             // 诊断信息
             patient.setInhospitalDiagnose(diagnoses[random.nextInt(diagnoses.length)]);
             
             // 医护人员信息
             patient.setDoctorId("D" + String.format("%03d", deptId * 10 + random.nextInt(5)));
             patient.setNurseId("N" + String.format("%03d", deptId * 10 + random.nextInt(8)));
             patient.setNurseLevel(random.nextInt(4) + 1);
             
             // 患者类别
             patient.setCategory(categories[random.nextInt(categories.length)]);
             
             // 在院患者特有属性：1-30天前入院，无出院时间
             LocalDateTime inhospitalTime = now.minusDays(random.nextInt(30) + 1);
             patient.setInhospitalTime(inhospitalTime);
             patient.setOuthospitalTime(null);
             patient.setStatus(1); // 在院
             
             // 更新时间
             patient.setUpdatedAt(now);
             
             patients.add(patient);
         }
         
         // 生成出院患者
         for (int i = 0; i < outPatientCount; i++) {
             Patient patient = new Patient();
             
             // 唯一ID
             patient.setInpatientInfoId("POUT" + String.format("%05d", i + 1));
             
             // 基本信息
             patient.setName(surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)]);
             patient.setIdCard(generateIdCard());
             patient.setSex(random.nextInt(2) + 1);
             patient.setMobile("1" + (3 + random.nextInt(6)) + random.nextInt(10) + String.format("%08d", random.nextInt(100000000)));
             
             // 年龄和生日
             int age = 20 + random.nextInt(60);
             patient.setAge(age);
             patient.setBirthday(LocalDate.now().minusYears(age).minusDays(random.nextInt(365)));
             
             // 住院号
             patient.setHospitalizationNo("HOUT" + String.format("%05d", i + 1));
             
             // 科室和床位信息
             int deptId = random.nextInt(10) + 1;
             patient.setDeptId(deptId);
             patient.setSickbedNo(deptId + "-" + String.format("%03d", random.nextInt(100) + 1));
             patient.setInpatientWard(deptId + "病区");
             
             // 诊断信息
             patient.setInhospitalDiagnose(diagnoses[random.nextInt(diagnoses.length)]);
             
             // 医护人员信息
             patient.setDoctorId("D" + String.format("%03d", deptId * 10 + random.nextInt(5)));
             patient.setNurseId("N" + String.format("%03d", deptId * 10 + random.nextInt(8)));
             patient.setNurseLevel(random.nextInt(4) + 1);
             
             // 患者类别
             patient.setCategory(categories[random.nextInt(categories.length)]);
             
             // 出院患者特有属性：30-90天前入院，1-20天前出院
             LocalDateTime inhospitalTime = now.minusDays(30 + random.nextInt(60));
             LocalDateTime outhospitalTime = now.minusDays(random.nextInt(20) + 1);
             
             // 确保出院时间晚于入院时间
             if (outhospitalTime.isBefore(inhospitalTime)) {
                 outhospitalTime = inhospitalTime.plusDays(random.nextInt(30) + 1);
             }
             
             patient.setInhospitalTime(inhospitalTime);
             patient.setOuthospitalTime(outhospitalTime);
             patient.setStatus(2); // 出院
             
             // 更新时间
             patient.setUpdatedAt(now);
             
             patients.add(patient);
         }
         
         return patients;
     }
    
    /**
     * 生成随机身份证号
     */
    private static String generateIdCard() {
        // 地区码 (随机选择一个有效的地区码)
        String[] areaCodes = {"110101", "310101", "440101", "510101", "330101"};
        String areaCode = areaCodes[random.nextInt(areaCodes.length)];
        
        // 出生日期 (1960-01-01 到 2000-12-31)
        int year = 1960 + random.nextInt(41); // 1960-2000
        int month = random.nextInt(12) + 1;
        int day = random.nextInt(28) + 1; // 简化处理，避免处理大月小月
        
        String birthDate = String.format("%04d%02d%02d", year, month, day);
        
        // 顺序码 (3位数字)
        String sequenceCode = String.format("%03d", random.nextInt(1000));
        
        // 校验码 (简化处理，随机生成)
        char[] checkCodeChars = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'X'};
        String checkCode = String.valueOf(checkCodeChars[random.nextInt(checkCodeChars.length)]);
        
        return areaCode + birthDate + sequenceCode + checkCode;
    }
} 