package com.sysgetway.core.example;

import com.alibaba.fastjson2.JSON;
import com.sysgetway.core.entity.Patient;
import com.sysgetway.core.utils.TestDataFactory;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * 患者数据导入示例
 * 
 * 这个示例展示了如何使用患者数据导入接口传递患者数据
 */
public class PatientDataImportExample {

    public static void main(String[] args) {
        try {
            // 医院ID
            String hospitalId = "H001";
            
            // 准备患者数据
            List<Patient> patients = new ArrayList<>();
            
            // 添加各种类型的患者
            // 1. 入院患者
            patients.add(TestDataFactory.createInPatient());
            
            // 2. 在院患者
            patients.add(TestDataFactory.createUpPatient());
            
            // 3. 出院患者
            patients.add(TestDataFactory.createOutPatient());
            
            // 4. 数据不一致的患者(异常情况测试)
            patients.add(TestDataFactory.createInconsistentPatient());
            
            // 将患者数据序列化为JSON
            String jsonData = JSON.toJSONString(patients);
            System.out.println("请求数据：" + jsonData);
            
            // 创建HTTP请求
            URL url = new URL("http://localhost:8080/api/patients/" + hospitalId + "/import");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setDoOutput(true);
            
            // 发送请求数据
            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // 读取响应
            int responseCode = conn.getResponseCode();
            System.out.println("响应状态码：" + responseCode);
            
            StringBuilder response = new StringBuilder();
            try (Scanner scanner = new Scanner(conn.getInputStream(), StandardCharsets.UTF_8.name())) {
                while (scanner.hasNextLine()) {
                    response.append(scanner.nextLine());
                }
            }
            
            System.out.println("响应内容：" + response.toString());
            
            // 断开连接
            conn.disconnect();
            
            System.out.println("\n=== 示例说明 ===");
            System.out.println("1. 本示例展示了如何向医院数据同步网关系统导入患者数据");
            System.out.println("2. 患者数据按类型分为：入院患者(in)、在院患者(up)、出院患者(out)");
            System.out.println("3. 导入接口会自动进行数据分类，并存储到Redis中");
            System.out.println("4. 导入成功后，可以通过以下接口查询数据：");
            System.out.println("   - GET /api/patients/{hospitalId}/{patientType} 查询特定类型的患者");
            System.out.println("   - GET /api/patients/{hospitalId}/stats 查询患者统计信息");
            System.out.println("   - GET /api/data/{hospitalId}/patient/{patientId} 查询单个患者详情");
            System.out.println("5. 也可以通过同步接口触发数据同步：");
            System.out.println("   - PUT /api/sync/full/{hospitalId}/patient 全量同步");
            System.out.println("   - PUT /api/sync/incremental/{hospitalId}/patient 增量同步");
            System.out.println("   - PUT /api/sync/patient/{hospitalId}/{patientType} 按患者类型同步");
            
        } catch (Exception e) {
            System.out.println("发生异常：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 直接使用RestTemplate示例（需要添加spring-web依赖）
     * 这里仅作参考，不会实际执行
     */
    public static void restTemplateExample() {
        /*
        // 准备患者数据
        List<Patient> patients = new ArrayList<>();
        patients.add(TestDataFactory.createInPatient());
        patients.add(TestDataFactory.createUpPatient());
        
        // 创建RestTemplate
        RestTemplate restTemplate = new RestTemplate();
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 创建请求实体
        HttpEntity<List<Patient>> request = new HttpEntity<>(patients, headers);
        
        // 发送请求
        String hospitalId = "H001";
        String url = "http://localhost:8080/api/patients/" + hospitalId + "/import";
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        
        // 处理响应
        System.out.println("状态码：" + response.getStatusCode());
        System.out.println("响应体：" + response.getBody());
        */
    }
} 