package com.sysgetway.core;

import com.sysgetway.core.entity.Patient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis对象存储测试
 * 验证修复后的Redis配置是否能正确存储和读取Patient对象
 */
public class TestRedisObjectStorage {

    public static void main(String[] args) {
        System.out.println("开始测试Redis对象存储...");
        
        try {
            // 创建Redis连接
            LettuceConnectionFactory connectionFactory = new LettuceConnectionFactory("localhost", 6379);
            connectionFactory.setDatabase(9);
            connectionFactory.afterPropertiesSet();
            
            // 创建RedisTemplate（与应用配置一致）
            RedisTemplate<String, Object> redisTemplate = createRedisTemplate(connectionFactory);
            
            // 创建测试患者对象
            Patient patient = new Patient();
            patient.setInpatientInfoId("P001");
            patient.setName("张三");
            patient.setHospitalizationNo("H001");
            patient.setBirthday(LocalDate.of(1990, 1, 1));
            patient.setInhospitalTime(LocalDateTime.of(2024, 7, 18, 10, 0, 0));
            patient.setOuthospitalTime(LocalDateTime.of(2024, 7, 25, 14, 30, 0));
            patient.setUpdatedAt(LocalDateTime.now());
            patient.setStatus(1);

            System.out.println("原始患者对象:");
            System.out.println("ID: " + patient.getInpatientInfoId());
            System.out.println("姓名: " + patient.getName());
            System.out.println("住院号: " + patient.getHospitalizationNo());
            System.out.println("生日: " + patient.getBirthday());
            System.out.println("入院时间: " + patient.getInhospitalTime());
            System.out.println("出院时间: " + patient.getOuthospitalTime());
            System.out.println("状态: " + patient.getStatus());
            System.out.println();

            // 测试Hash存储（模拟应用中的存储方式）
            String redisKey = "InPatient-13";
            String fieldName = "H001_2024-07-18T10:00:00";
            
            System.out.println("正在存储到Redis Hash...");
            System.out.println("Redis Key: " + redisKey);
            System.out.println("Field Name: " + fieldName);
            
            // 直接存储对象
            redisTemplate.opsForHash().put(redisKey, fieldName, patient);
            System.out.println("✅ 对象存储成功");
            System.out.println();

            // 测试读取
            System.out.println("正在从Redis读取...");
            Object retrievedData = redisTemplate.opsForHash().get(redisKey, fieldName);
            
            if (retrievedData != null) {
                System.out.println("✅ 数据读取成功");
                System.out.println("数据类型: " + retrievedData.getClass().getName());
                
                if (retrievedData instanceof Patient) {
                    Patient retrievedPatient = (Patient) retrievedData;
                    
                    System.out.println("读取的患者对象:");
                    System.out.println("ID: " + retrievedPatient.getInpatientInfoId());
                    System.out.println("姓名: " + retrievedPatient.getName());
                    System.out.println("住院号: " + retrievedPatient.getHospitalizationNo());
                    System.out.println("生日: " + retrievedPatient.getBirthday());
                    System.out.println("入院时间: " + retrievedPatient.getInhospitalTime());
                    System.out.println("出院时间: " + retrievedPatient.getOuthospitalTime());
                    System.out.println("状态: " + retrievedPatient.getStatus());
                    System.out.println();

                    // 验证数据一致性
                    boolean isValid = patient.getInpatientInfoId().equals(retrievedPatient.getInpatientInfoId()) &&
                                     patient.getName().equals(retrievedPatient.getName()) &&
                                     patient.getHospitalizationNo().equals(retrievedPatient.getHospitalizationNo()) &&
                                     patient.getBirthday().equals(retrievedPatient.getBirthday()) &&
                                     patient.getInhospitalTime().equals(retrievedPatient.getInhospitalTime()) &&
                                     patient.getOuthospitalTime().equals(retrievedPatient.getOuthospitalTime()) &&
                                     patient.getStatus().equals(retrievedPatient.getStatus());

                    if (isValid) {
                        System.out.println("✅ Redis对象存储测试通过！");
                        System.out.println("✅ Java 8时间类型序列化正常！");
                        System.out.println("✅ 可以直接存储Patient对象到Redis！");
                    } else {
                        System.out.println("❌ 数据不一致，测试失败！");
                    }
                } else {
                    System.out.println("❌ 读取的数据不是Patient对象: " + retrievedData);
                }
            } else {
                System.out.println("❌ 未能从Redis读取到数据");
            }
            
            // 清理测试数据
            redisTemplate.delete(redisKey);
            System.out.println("🧹 测试数据已清理");
            
            // 关闭连接
            connectionFactory.destroy();

        } catch (Exception e) {
            System.out.println("❌ 测试失败，异常信息:");
            e.printStackTrace();
        }
    }
    
    private static RedisTemplate<String, Object> createRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        
        // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper om = new ObjectMapper();
        
        // 注册Java 8时间模块，支持LocalDate、LocalDateTime等类型
        om.registerModule(new JavaTimeModule());
        
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        
        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        
        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        
        template.afterPropertiesSet();
        
        return template;
    }
}
