<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义样式 -->
    <style>
      .start-end { fill: #4CAF50; stroke: #2E7D32; stroke-width: 2; }
      .process { fill: #2196F3; stroke: #1976D2; stroke-width: 1.5; }
      .decision { fill: #FFC107; stroke: #F57C00; stroke-width: 1.5; }
      .storage { fill: #9C27B0; stroke: #6A1B9A; stroke-width: 1.5; }
      .error { fill: #F44336; stroke: #C62828; stroke-width: 1.5; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
      .title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .async-arrow { stroke: #666; stroke-width: 2; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
    </style>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title" fill="#333">数据同步流程图</text>
  
  <!-- 开始节点 -->
  <ellipse cx="600" cy="80" rx="60" ry="25" class="start-end"/>
  <text x="600" y="80" class="text" fill="white">开始</text>
  
  <!-- API请求 -->
  <rect x="520" y="130" width="160" height="50" rx="5" class="process"/>
  <text x="600" y="150" class="text" fill="white">API请求</text>
  <text x="600" y="165" class="text" fill="white">/api/sync/full/{hospitalId}/{tableName}</text>
  
  <!-- 验证参数 -->
  <rect x="520" y="210" width="160" height="40" rx="5" class="process"/>
  <text x="600" y="225" class="text" fill="white">验证参数</text>
  <text x="600" y="240" class="text" fill="white">检查hospitalId和tableName</text>
  
  <!-- 创建异步任务 -->
  <rect x="520" y="280" width="160" height="40" rx="5" class="process"/>
  <text x="600" y="295" class="text" fill="white">创建异步任务</text>
  <text x="600" y="310" class="text" fill="white">生成taskId</text>
  
  <!-- 初始化状态 -->
  <rect x="520" y="350" width="160" height="40" rx="5" class="process"/>
  <text x="600" y="365" class="text" fill="white">初始化同步状态</text>
  <text x="600" y="380" class="text" fill="white">IN_PROGRESS</text>
  
  <!-- 保存初始状态到Redis -->
  <rect x="520" y="420" width="160" height="40" rx="5" class="storage"/>
  <text x="600" y="435" class="text" fill="white">保存初始状态到Redis</text>
  <text x="600" y="450" class="text" fill="white">使用hospitalId和tableName</text>
  
  <!-- 立即返回 -->
  <rect x="520" y="490" width="160" height="40" rx="5" class="process"/>
  <text x="600" y="505" class="text" fill="white">立即返回初始状态</text>
  <text x="600" y="520" class="text" fill="white">包含taskId</text>
  
  <!-- 异步处理 -->
  <rect x="800" y="560" width="160" height="40" rx="5" class="process"/>
  <text x="880" y="575" class="text" fill="white">异步处理</text>
  <text x="880" y="590" class="text" fill="white">使用CompletableFuture</text>
  
  <!-- 获取数据 -->
  <rect x="800" y="630" width="160" height="40" rx="5" class="process"/>
  <text x="880" y="645" class="text" fill="white">获取数据</text>
  <text x="880" y="660" class="text" fill="white">从外部系统或请求体</text>
  
  <!-- 数据是否为空判断 -->
  <polygon points="880,700 930,720 880,740 830,720" class="decision"/>
  <text x="880" y="720" class="text">数据是否为空?</text>
  
  <!-- 无数据处理 -->
  <rect x="1000" y="700" width="120" height="40" rx="5" class="process"/>
  <text x="1060" y="715" class="text" fill="white">设置状态为SUCCESS</text>
  <text x="1060" y="730" class="text" fill="white">消息为NO_DATA</text>
  
  <!-- 是否为患者数据判断 -->
  <polygon points="880,780 930,800 880,820 830,800" class="decision"/>
  <text x="880" y="800" class="text">是否为患者数据?</text>
  
  <!-- 患者数据处理 -->
  <rect x="700" y="860" width="120" height="40" rx="5" class="process"/>
  <text x="760" y="875" class="text" fill="white">患者数据处理</text>
  <text x="760" y="890" class="text" fill="white">按状态分类</text>
  
  <!-- 非患者数据处理 -->
  <rect x="960" y="860" width="120" height="40" rx="5" class="process"/>
  <text x="1020" y="875" class="text" fill="white">非患者数据处理</text>
  <text x="1020" y="890" class="text" fill="white">转换为Map</text>
  
  <!-- 患者分类 -->
  <rect x="700" y="930" width="120" height="40" rx="5" class="process"/>
  <text x="760" y="945" class="text" fill="white">按状态分类患者</text>
  <text x="760" y="960" class="text" fill="white">IN/UP/OUT</text>
  
  <!-- 存储到Redis -->
  <rect x="800" y="1000" width="160" height="40" rx="5" class="storage"/>
  <text x="880" y="1020" class="text" fill="white">存储到Redis</text>
  
  <!-- 清除旧数据 -->
  <rect x="800" y="1070" width="160" height="30" rx="5" class="storage"/>
  <text x="880" y="1085" class="text" fill="white">清除旧数据</text>
  
  <!-- 写入新数据 -->
  <rect x="800" y="1120" width="160" height="30" rx="5" class="storage"/>
  <text x="880" y="1135" class="text" fill="white">写入新数据</text>
  
  <!-- 设置过期时间 -->
  <rect x="800" y="1170" width="160" height="30" rx="5" class="storage"/>
  <text x="880" y="1185" class="text" fill="white">设置过期时间</text>
  
  <!-- 更新同步状态 -->
  <rect x="520" y="1240" width="160" height="40" rx="5" class="process"/>
  <text x="600" y="1255" class="text" fill="white">更新同步状态</text>
  <text x="600" y="1270" class="text" fill="white">设置结束时间和耗时</text>
  
  <!-- 保存最终状态到Redis -->
  <rect x="520" y="1310" width="160" height="40" rx="5" class="storage"/>
  <text x="600" y="1325" class="text" fill="white">保存最终状态到Redis</text>
  <text x="600" y="1340" class="text" fill="white">包含详细统计信息</text>
  
  <!-- 保存最后同步时间 -->
  <rect x="520" y="1380" width="160" height="30" rx="5" class="storage"/>
  <text x="600" y="1395" class="text" fill="white">保存最后同步时间</text>
  
  <!-- 结束节点 -->
  <ellipse cx="600" cy="1460" rx="60" ry="25" class="start-end"/>
  <text x="600" y="1460" class="text" fill="white">结束</text>
  
  <!-- 错误处理 -->
  <rect x="300" y="1100" width="120" height="40" rx="5" class="error"/>
  <text x="360" y="1115" class="text" fill="white">处理异常</text>
  <text x="360" y="1130" class="text" fill="white">记录错误信息</text>

  <rect x="300" y="1160" width="120" height="40" rx="5" class="error"/>
  <text x="360" y="1175" class="text" fill="white">设置状态为FAILED</text>
  <text x="360" y="1190" class="text" fill="white">保存错误信息</text>

  <!-- 连接线 -->
  <!-- 主流程连接线 -->
  <line x1="600" y1="105" x2="600" y2="130" class="arrow"/>
  <line x1="600" y1="180" x2="600" y2="210" class="arrow"/>
  <line x1="600" y1="250" x2="600" y2="280" class="arrow"/>
  <line x1="600" y1="320" x2="600" y2="350" class="arrow"/>
  <line x1="600" y1="390" x2="600" y2="420" class="arrow"/>
  <line x1="600" y1="460" x2="600" y2="490" class="arrow"/>

  <!-- 异步处理连接线 -->
  <line x1="680" y1="510" x2="800" y2="580" class="async-arrow"/>
  <line x1="880" y1="600" x2="880" y2="630" class="arrow"/>
  <line x1="880" y1="670" x2="880" y2="700" class="arrow"/>

  <!-- 数据为空判断 -->
  <line x1="930" y1="720" x2="1000" y2="720" class="arrow"/>
  <text x="965" y="715" class="text" font-size="10">是</text>
  <line x1="880" y1="740" x2="880" y2="780" class="arrow"/>
  <text x="885" y="760" class="text" font-size="10">否</text>

  <!-- 患者数据判断 -->
  <line x1="830" y1="800" x2="760" y2="860" class="arrow"/>
  <text x="795" y="830" class="text" font-size="10">是</text>
  <line x1="930" y1="800" x2="1020" y2="860" class="arrow"/>
  <text x="975" y="830" class="text" font-size="10">否</text>

  <!-- 患者数据处理流程 -->
  <line x1="760" y1="900" x2="760" y2="930" class="arrow"/>
  <line x1="760" y1="970" x2="800" y2="1020" class="arrow"/>

  <!-- 非患者数据处理流程 -->
  <line x1="1020" y1="900" x2="960" y2="1020" class="arrow"/>

  <!-- Redis存储流程 -->
  <line x1="880" y1="1040" x2="880" y2="1070" class="arrow"/>
  <line x1="880" y1="1100" x2="880" y2="1120" class="arrow"/>
  <line x1="880" y1="1150" x2="880" y2="1170" class="arrow"/>
  <line x1="800" y1="1185" x2="680" y2="1260" class="arrow"/>

  <!-- 无数据处理流程 -->
  <line x1="1060" y1="740" x2="1060" y2="1220" class="arrow"/>
  <line x1="1060" y1="1220" x2="680" y2="1260" class="arrow"/>

  <!-- 最终状态保存流程 -->
  <line x1="600" y1="1280" x2="600" y2="1310" class="arrow"/>
  <line x1="600" y1="1350" x2="600" y2="1380" class="arrow"/>
  <line x1="600" y1="1410" x2="600" y2="1435" class="arrow"/>

  <!-- 错误处理连接线 -->
  <line x1="800" y1="650" x2="420" y2="1120" class="arrow" stroke="#F44336"/>
  <text x="610" y="885" class="text" font-size="10" fill="#F44336">异常</text>
  <line x1="800" y1="1020" x2="420" y2="1120" class="arrow" stroke="#F44336"/>
  <line x1="360" y1="1140" x2="360" y2="1160" class="arrow"/>
  <line x1="420" y1="1180" x2="520" y2="1330" class="arrow"/>

  <!-- 图例 -->
  <g transform="translate(50, 1450)">
    <text x="0" y="0" class="text" font-weight="bold">图例:</text>
    <rect x="0" y="10" width="20" height="15" class="start-end"/>
    <text x="25" y="22" class="text" font-size="10">开始/结束</text>
    <rect x="80" y="10" width="20" height="15" class="process"/>
    <text x="105" y="22" class="text" font-size="10">处理过程</text>
    <polygon points="160,10 175,17.5 160,25 145,17.5" class="decision"/>
    <text x="180" y="22" class="text" font-size="10">判断</text>
    <rect x="220" y="10" width="20" height="15" class="storage"/>
    <text x="245" y="22" class="text" font-size="10">存储</text>
    <rect x="280" y="10" width="20" height="15" class="error"/>
    <text x="305" y="22" class="text" font-size="10">错误处理</text>
    <line x1="360" y1="17.5" x2="380" y2="17.5" class="async-arrow"/>
    <text x="385" y="22" class="text" font-size="10">异步处理</text>
  </g>
</svg>
