<svg id="mermaid-1737547200000" width="100%" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="800" style="max-width: 900px;" viewBox="0 0 900 800">
  <style>
    .label { font-family: 'trebuchet ms', verdana, arial, sans-serif; font-size: 14px; fill: #333; }
    .cluster-label { fill: #333; }
    .cluster rect { fill: #FFFFDE; stroke: #AAAA33; stroke-width: 1px; }
    .node circle { fill: #ECECFF; stroke: #9370DB; stroke-width: 1.5px; }
    .node rect { fill: #ECECFF; stroke: #CCCCFF; stroke-width: 1.5px; }
    .edgePath .path { stroke: #333333; stroke-width: 1.5px; }
    .flowchart-link { stroke: #333333; fill: none; }
    .edgeLabel { background-color: #e8e8e8; text-align: center; }
    .cluster { fill: #fff2cc; stroke: #d6b656; }
  </style>
  
  <!-- 定义箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#333333"/>
    </marker>
    <marker id="arrowhead-dotted" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#666666"/>
    </marker>
  </defs>

  <!-- HIS系统 -->
  <circle cx="150" cy="80" r="40" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
  <text x="150" y="75" text-anchor="middle" class="label" style="font-size: 11px;">HIS系统</text>

  <!-- SysGetway 数据同步网关 -->
  <circle cx="450" cy="80" r="50" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="3"/>
  <text x="450" y="70" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">SysGetway</text>
  <text x="450" y="85" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">数据同步网关</text>

  <!-- 任务管理器 -->
  <circle cx="750" cy="80" r="40" fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
  <text x="750" y="75" text-anchor="middle" class="label" style="font-size: 11px;">任务管理器</text>

  <!-- 业务接口分组框 -->
  <rect x="50" y="160" width="800" height="120" rx="10" ry="10"
        fill="#e8f5e8" stroke="#388e3c" stroke-width="2" opacity="0.2"/>
  <text x="450" y="180" text-anchor="middle" class="cluster-label"
        style="font-size: 16px; font-weight: bold;">核心业务接口</text>

  <!-- 同步处理流程分组框 -->
  <rect x="50" y="320" width="800" height="140" rx="10" ry="10"
        fill="#fff2cc" stroke="#d6b656" stroke-width="2" opacity="0.2"/>
  <text x="450" y="340" text-anchor="middle" class="cluster-label"
        style="font-size: 16px; font-weight: bold;">同步处理流程</text>

  <!-- 任务调度机制分组框 -->
  <rect x="50" y="500" width="800" height="140" rx="10" ry="10"
        fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2" opacity="0.2"/>
  <text x="450" y="520" text-anchor="middle" class="cluster-label"
        style="font-size: 16px; font-weight: bold;">任务调度机制</text>



  <!-- 核心业务接口 -->
  <!-- 全量同步接口 -->
  <rect x="80" y="200" width="140" height="60" rx="8" ry="8"
        fill="#e1f5fe" stroke="#0277bd" stroke-width="2"/>
  <text x="150" y="220" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">全量同步接口</text>
  <text x="150" y="235" text-anchor="middle" class="label" style="font-size: 9px;">/api/sync/full</text>
  <text x="150" y="250" text-anchor="middle" class="label" style="font-size: 9px;">科室/医护/患者</text>

  <!-- 患者分类同步接口 -->
  <rect x="240" y="200" width="140" height="60" rx="8" ry="8"
        fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2"/>
  <text x="310" y="220" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">患者分类同步</text>
  <text x="310" y="235" text-anchor="middle" class="label" style="font-size: 9px;">/api/sync/patient</text>
  <text x="310" y="250" text-anchor="middle" class="label" style="font-size: 9px;">入院/在院/出院</text>

  <!-- 数据查询接口 -->
  <rect x="400" y="200" width="140" height="60" rx="8" ry="8"
        fill="#e8f5e8" stroke="#388e3c" stroke-width="2"/>
  <text x="470" y="220" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">数据查询接口</text>
  <text x="470" y="235" text-anchor="middle" class="label" style="font-size: 9px;">/api/data</text>
  <text x="470" y="250" text-anchor="middle" class="label" style="font-size: 9px;">条件/批量查询</text>

  <!-- 状态查询接口 -->
  <rect x="560" y="200" width="140" height="60" rx="8" ry="8"
        fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
  <text x="630" y="220" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">状态查询接口</text>
  <text x="630" y="235" text-anchor="middle" class="label" style="font-size: 9px;">/api/sync/status</text>
  <text x="630" y="250" text-anchor="middle" class="label" style="font-size: 9px;">任务状态/进度</text>

  <!-- 同步处理流程 -->
  <!-- 步骤1: 接收请求 -->
  <rect x="100" y="360" width="140" height="80" rx="8" ry="8"
        fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
  <text x="170" y="380" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">① 接收请求</text>
  <text x="170" y="395" text-anchor="middle" class="label" style="font-size: 9px;">参数验证</text>
  <text x="170" y="410" text-anchor="middle" class="label" style="font-size: 9px;">权限检查</text>
  <text x="170" y="425" text-anchor="middle" class="label" style="font-size: 9px;">创建任务ID</text>

  <!-- 步骤2: 数据获取 -->
  <rect x="260" y="360" width="140" height="80" rx="8" ry="8"
        fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2"/>
  <text x="330" y="380" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">② 数据获取</text>
  <text x="330" y="395" text-anchor="middle" class="label" style="font-size: 9px;">外部API调用</text>
  <text x="330" y="410" text-anchor="middle" class="label" style="font-size: 9px;">数据格式化</text>
  <text x="330" y="425" text-anchor="middle" class="label" style="font-size: 9px;">数据验证</text>

  <!-- 步骤3: 数据处理 -->
  <rect x="420" y="360" width="140" height="80" rx="8" ry="8"
        fill="#e8f5e8" stroke="#388e3c" stroke-width="2"/>
  <text x="490" y="380" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">③ 数据处理</text>
  <text x="490" y="395" text-anchor="middle" class="label" style="font-size: 9px;">数据分类</text>
  <text x="490" y="410" text-anchor="middle" class="label" style="font-size: 9px;">存储Redis</text>
  <text x="490" y="425" text-anchor="middle" class="label" style="font-size: 9px;">更新状态</text>

  <!-- 步骤4: 结果返回 -->
  <rect x="580" y="360" width="140" height="80" rx="8" ry="8"
        fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
  <text x="650" y="380" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">④ 结果返回</text>
  <text x="650" y="395" text-anchor="middle" class="label" style="font-size: 9px;">任务状态</text>
  <text x="650" y="410" text-anchor="middle" class="label" style="font-size: 9px;">执行结果</text>
  <text x="650" y="425" text-anchor="middle" class="label" style="font-size: 9px;">错误信息</text>



  <!-- 任务调度机制 -->
  <!-- 任务队列 -->
  <rect x="100" y="540" width="140" height="80" rx="8" ry="8"
        fill="#e1f5fe" stroke="#0277bd" stroke-width="2"/>
  <text x="170" y="560" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">任务队列</text>
  <text x="170" y="575" text-anchor="middle" class="label" style="font-size: 9px;">异步任务</text>
  <text x="170" y="590" text-anchor="middle" class="label" style="font-size: 9px;">优先级管理</text>
  <text x="170" y="605" text-anchor="middle" class="label" style="font-size: 9px;">负载均衡</text>

  <!-- 执行引擎 -->
  <rect x="260" y="540" width="140" height="80" rx="8" ry="8"
        fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
  <text x="330" y="560" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">执行引擎</text>
  <text x="330" y="575" text-anchor="middle" class="label" style="font-size: 9px;">线程池管理</text>
  <text x="330" y="590" text-anchor="middle" class="label" style="font-size: 9px;">并发控制</text>
  <text x="330" y="605" text-anchor="middle" class="label" style="font-size: 9px;">异常处理</text>

  <!-- 状态管理 -->
  <rect x="420" y="540" width="140" height="80" rx="8" ry="8"
        fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2"/>
  <text x="490" y="560" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">状态管理</text>
  <text x="490" y="575" text-anchor="middle" class="label" style="font-size: 9px;">任务状态跟踪</text>
  <text x="490" y="590" text-anchor="middle" class="label" style="font-size: 9px;">进度监控</text>
  <text x="490" y="605" text-anchor="middle" class="label" style="font-size: 9px;">结果缓存</text>

  <!-- 监控告警 -->
  <rect x="580" y="540" width="140" height="80" rx="8" ry="8"
        fill="#e8f5e8" stroke="#388e3c" stroke-width="2"/>
  <text x="650" y="560" text-anchor="middle" class="label" style="font-size: 11px; font-weight: bold;">监控告警</text>
  <text x="650" y="575" text-anchor="middle" class="label" style="font-size: 9px;">性能监控</text>
  <text x="650" y="590" text-anchor="middle" class="label" style="font-size: 9px;">异常告警</text>
  <text x="650" y="605" text-anchor="middle" class="label" style="font-size: 9px;">日志记录</text>

  <!-- 连接线 -->
  <!-- HIS系统到网关的双向连接 -->
  <path d="M 190 70 L 400 70" stroke="#666666" stroke-width="2"
        stroke-dasharray="5,5" fill="none" marker-end="url(#arrowhead-dotted)"/>
  <text x="295" y="60" class="label" style="font-size: 10px;">HTTP 请求</text>

  <path d="M 400 90 L 190 90" stroke="#666666" stroke-width="2"
        stroke-dasharray="5,5" fill="none" marker-end="url(#arrowhead-dotted)"/>
  <text x="295" y="105" class="label" style="font-size: 10px;">HTTP 响应</text>

  <!-- 网关到任务管理器 -->
  <path d="M 500 80 L 710 80" stroke="#333333" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>
  <text x="605" y="70" class="label" style="font-size: 10px;">任务调度</text>

  <!-- 网关到业务接口 -->
  <path d="M 450 130 L 450 155" stroke="#333333" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>

  <!-- 业务接口到处理流程 -->
  <path d="M 450 270 L 450 315" stroke="#333333" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>

  <!-- 处理流程步骤间连接 -->
  <path d="M 235 400 L 255 400" stroke="#666666" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 395 400 L 415 400" stroke="#666666" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 555 400 L 575 400" stroke="#666666" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>

  <!-- 处理流程到调度机制的连接 -->
  <path d="M 450 450 L 450 495" stroke="#333333" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>

  <!-- 任务管理器到调度机制 -->
  <path d="M 720 120 L 500 495" stroke="#f57c00" stroke-width="2"
        stroke-dasharray="3,3" fill="none" marker-end="url(#arrowhead)"/>

  <!-- 调度机制步骤间连接 -->
  <path d="M 235 580 L 255 580" stroke="#666666" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 395 580 L 415 580" stroke="#666666" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>
  <path d="M 555 580 L 575 580" stroke="#666666" stroke-width="2"
        fill="none" marker-end="url(#arrowhead)"/>

  <!-- 结果返回路径 -->
  <path d="M 650 360 L 780 120" stroke="#4caf50" stroke-width="2"
        stroke-dasharray="3,3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="715" y="240" class="label" style="font-size: 9px; fill: #4caf50;">结果返回</text>
  


</svg>
