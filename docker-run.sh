#!/bin/bash

# 检查是否已存在同名容器，如果存在则停止并删除
if [ "$(docker ps -a -q -f name=sysgetway)" ]; then
    echo "=== 停止并删除已存在的sysgetway容器 ==="
    docker stop sysgetway
    docker rm sysgetway
fi

# 获取当前目录的绝对路径
CURRENT_DIR=$(pwd)

# 运行新容器，挂载配置文件
echo "=== 启动sysgetway容器 ==="
docker run -d -p 9000:9000 --name sysgetway \
  -v "${CURRENT_DIR}/application-docker.yml:/app/config/application-docker.yml:ro" \
  -v "${CURRENT_DIR}/logs:/app/logs" \
  sysgetway:1.0.0

# 检查容器是否成功启动
if [ $? -eq 0 ]; then
    echo "=== 容器启动成功 ==="
    echo "=== 配置文件已挂载: ${CURRENT_DIR}/application-docker.yml ==="
    echo "=== 日志目录已挂载: ${CURRENT_DIR}/logs ==="
    echo "=== 查看容器日志 ==="
    docker logs -f sysgetway
else
    echo "=== 容器启动失败 ==="
    exit 1
fi