<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .start-end { fill: #4CAF50; stroke: #2E7D32; stroke-width: 3; }
      .api { fill: #FF9800; stroke: #E65100; stroke-width: 2; }
      .process { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
      .decision { fill: #FFC107; stroke: #F57C00; stroke-width: 2; }
      .storage { fill: #9C27B0; stroke: #6A1B9A; stroke-width: 2; }
      .async { fill: #00BCD4; stroke: #00838F; stroke-width: 2; }
      .error { fill: #F44336; stroke: #C62828; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 13px; text-anchor: middle; dominant-baseline: middle; font-weight: 500; }
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .async-arrow { stroke: #00BCD4; stroke-width: 3; fill: none; stroke-dasharray: 8,4; marker-end: url(#arrowhead); }
      .error-arrow { stroke: #F44336; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#333" />
    </marker>
  </defs>
  
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="1000" height="800" fill="url(#bgGradient)"/>
  
  <!-- 标题 -->
  <text x="500" y="40" class="title" fill="#333">数据同步流程图</text>
  <text x="500" y="65" class="subtitle" fill="#666">Data Synchronization Flow</text>
  
  <!-- 开始 -->
  <ellipse cx="500" cy="120" rx="80" ry="30" class="start-end"/>
  <text x="500" y="120" class="text" fill="white">开始</text>
  
  <!-- API请求 -->
  <rect x="400" y="180" width="200" height="60" rx="10" class="api"/>
  <text x="500" y="200" class="text" fill="white">API请求</text>
  <text x="500" y="220" class="text" fill="white">/api/sync/full/{hospitalId}/{tableName}</text>
  
  <!-- 参数验证 -->
  <rect x="420" y="270" width="160" height="40" rx="8" class="process"/>
  <text x="500" y="290" class="text" fill="white">参数验证 & 创建任务</text>
  
  <!-- 立即返回 -->
  <rect x="420" y="340" width="160" height="40" rx="8" class="process"/>
  <text x="500" y="360" class="text" fill="white">立即返回任务状态</text>
  
  <!-- 异步处理分界线 -->
  <line x1="50" y1="420" x2="950" y2="420" stroke="#00BCD4" stroke-width="2" stroke-dasharray="10,5"/>
  <text x="500" y="410" class="subtitle" fill="#00BCD4">异步处理</text>
  
  <!-- 获取数据 -->
  <rect x="420" y="450" width="160" height="40" rx="8" class="async"/>
  <text x="500" y="470" class="text" fill="white">获取数据</text>
  
  <!-- 数据处理判断 -->
  <polygon points="500,520 560,540 500,560 440,540" class="decision"/>
  <text x="500" y="535" class="text">数据类型?</text>
  <text x="500" y="550" class="text">患者/非患者</text>
  
  <!-- 患者数据处理 -->
  <rect x="250" y="600" width="140" height="50" rx="8" class="async"/>
  <text x="320" y="615" class="text" fill="white">患者数据处理</text>
  <text x="320" y="635" class="text" fill="white">自动分类 IN/UP/OUT</text>
  
  <!-- 非患者数据处理 -->
  <rect x="610" y="600" width="140" height="50" rx="8" class="async"/>
  <text x="680" y="615" class="text" fill="white">非患者数据处理</text>
  <text x="680" y="635" class="text" fill="white">转换为键值对</text>
  
  <!-- 存储到Redis -->
  <rect x="420" y="690" width="160" height="50" rx="8" class="storage"/>
  <text x="500" y="705" class="text" fill="white">存储到Redis</text>
  <text x="500" y="720" class="text" fill="white">Hash结构存储</text>
  
  <!-- 更新状态 -->
  <rect x="420" y="770" width="160" height="30" rx="8" class="process"/>
  <text x="500" y="785" class="text" fill="white">更新同步状态</text>
  
  <!-- 结束 -->
  <ellipse cx="500" cy="850" rx="60" ry="25" class="start-end"/>
  <text x="500" y="850" class="text" fill="white">完成</text>
  
  <!-- 错误处理 -->
  <rect x="750" y="520" width="120" height="40" rx="8" class="error"/>
  <text x="810" y="535" class="text" fill="white">错误处理</text>
  <text x="810" y="550" class="text" fill="white">记录异常信息</text>
  
  <!-- 连接线 -->
  <line x1="500" y1="150" x2="500" y2="180" class="arrow"/>
  <line x1="500" y1="240" x2="500" y2="270" class="arrow"/>
  <line x1="500" y1="310" x2="500" y2="340" class="arrow"/>
  <line x1="500" y1="380" x2="500" y2="450" class="async-arrow"/>
  <line x1="500" y1="490" x2="500" y2="520" class="arrow"/>
  
  <!-- 数据类型判断分支 -->
  <line x1="440" y1="540" x2="320" y2="600" class="arrow"/>
  <text x="380" y="570" class="text" font-size="12" fill="#333">患者</text>
  <line x1="560" y1="540" x2="680" y2="600" class="arrow"/>
  <text x="620" y="570" class="text" font-size="12" fill="#333">非患者</text>
  
  <!-- 汇聚到Redis存储 -->
  <line x1="320" y1="650" x2="420" y2="715" class="arrow"/>
  <line x1="680" y1="650" x2="580" y2="715" class="arrow"/>
  
  <!-- 存储到状态更新 -->
  <line x1="500" y1="740" x2="500" y2="770" class="arrow"/>
  <line x1="500" y1="800" x2="500" y2="825" class="arrow"/>
  
  <!-- 错误处理连接 -->
  <line x1="580" y1="470" x2="750" y2="540" class="error-arrow"/>
  <line x1="750" y1="540" x2="580" y2="785" class="error-arrow"/>
  
  <!-- 关键信息框 -->
  <g transform="translate(50, 100)">
    <rect x="0" y="0" width="280" height="180" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
    <text x="140" y="20" class="subtitle" fill="#495057">关键特性</text>
    
    <text x="10" y="45" class="text" font-size="12" fill="#495057">✓ 异步处理，立即响应</text>
    <text x="10" y="65" class="text" font-size="12" fill="#495057">✓ 任务状态实时跟踪</text>
    <text x="10" y="85" class="text" font-size="12" fill="#495057">✓ 患者数据智能分类</text>
    <text x="10" y="105" class="text" font-size="12" fill="#495057">✓ Redis Hash结构存储</text>
    <text x="10" y="125" class="text" font-size="12" fill="#495057">✓ 完善的错误处理</text>
    <text x="10" y="145" class="text" font-size="12" fill="#495057">✓ 性能监控统计</text>
    <text x="10" y="165" class="text" font-size="12" fill="#495057">✓ 支持全量/增量同步</text>
  </g>
  
  <!-- 数据流向说明 -->
  <g transform="translate(670, 100)">
    <rect x="0" y="0" width="280" height="180" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
    <text x="140" y="20" class="subtitle" fill="#495057">数据流向</text>
    
    <text x="10" y="45" class="text" font-size="12" fill="#495057">1. 客户端发起同步请求</text>
    <text x="10" y="65" class="text" font-size="12" fill="#495057">2. 服务端验证参数创建任务</text>
    <text x="10" y="85" class="text" font-size="12" fill="#495057">3. 立即返回任务ID和状态</text>
    <text x="10" y="105" class="text" font-size="12" fill="#495057">4. 异步获取外部系统数据</text>
    <text x="10" y="125" class="text" font-size="12" fill="#495057">5. 数据处理和分类转换</text>
    <text x="10" y="145" class="text" font-size="12" fill="#495057">6. 存储到Redis并更新状态</text>
    <text x="10" y="165" class="text" font-size="12" fill="#495057">7. 客户端可查询任务进度</text>
  </g>
  
  <!-- 图例 -->
  <g transform="translate(50, 650)">
    <rect x="0" y="0" width="280" height="120" rx="10" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
    <text x="140" y="20" class="subtitle" fill="#495057">图例说明</text>
    
    <rect x="10" y="30" width="30" height="20" class="start-end"/>
    <text x="50" y="45" class="text" font-size="11" fill="#495057">开始/结束</text>
    
    <rect x="130" y="30" width="30" height="20" class="api"/>
    <text x="170" y="45" class="text" font-size="11" fill="#495057">API接口</text>
    
    <rect x="10" y="60" width="30" height="20" class="process"/>
    <text x="50" y="75" class="text" font-size="11" fill="#495057">同步处理</text>
    
    <rect x="130" y="60" width="30" height="20" class="async"/>
    <text x="170" y="75" class="text" font-size="11" fill="#495057">异步处理</text>
    
    <polygon points="10,90 25,95 10,100 -5,95" class="decision"/>
    <text x="50" y="100" class="text" font-size="11" fill="#495057">判断分支</text>
    
    <rect x="130" y="90" width="30" height="20" class="storage"/>
    <text x="170" y="105" class="text" font-size="11" fill="#495057">数据存储</text>
    
    <rect x="210" y="90" width="30" height="20" class="error"/>
    <text x="250" y="105" class="text" font-size="11" fill="#495057">错误处理</text>
  </g>
</svg>
