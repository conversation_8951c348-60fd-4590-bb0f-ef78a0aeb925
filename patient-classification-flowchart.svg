<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .start-end { fill: #4CAF50; stroke: #2E7D32; stroke-width: 2; }
      .process { fill: #2196F3; stroke: #1976D2; stroke-width: 1.5; }
      .decision { fill: #FFC107; stroke: #F57C00; stroke-width: 1.5; }
      .classification { fill: #E91E63; stroke: #C2185B; stroke-width: 1.5; }
      .storage { fill: #9C27B0; stroke: #6A1B9A; stroke-width: 1.5; }
      .error { fill: #F44336; stroke: #C62828; stroke-width: 1.5; }
      .text { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; dominant-baseline: middle; }
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" class="title" fill="#333">患者数据分类处理流程图</text>
  
  <!-- 开始 -->
  <ellipse cx="700" cy="80" rx="80" ry="25" class="start-end"/>
  <text x="700" y="80" class="text" fill="white">患者数据处理开始</text>
  
  <!-- 检查患者数据 -->
  <polygon points="700,130 750,150 700,170 650,150" class="decision"/>
  <text x="700" y="145" class="text">患者数据</text>
  <text x="700" y="160" class="text">是否为空?</text>
  
  <!-- 无数据 -->
  <rect x="850" y="130" width="100" height="40" rx="5" class="process"/>
  <text x="900" y="150" class="text" fill="white">返回无数据状态</text>
  
  <!-- 初始化分类容器 -->
  <rect x="620" y="200" width="160" height="40" rx="5" class="process"/>
  <text x="700" y="215" class="text" fill="white">初始化分类容器</text>
  <text x="700" y="230" class="text" fill="white">IN/UP/OUT</text>
  
  <!-- 遍历患者 -->
  <rect x="620" y="270" width="160" height="30" rx="5" class="process"/>
  <text x="700" y="285" class="text" fill="white">遍历每个患者</text>
  
  <!-- 检查患者状态 -->
  <polygon points="700,330 750,350 700,370 650,350" class="decision"/>
  <text x="700" y="350" class="text">检查患者状态</text>
  
  <!-- 在院患者 (status=1) -->
  <rect x="450" y="420" width="100" height="40" rx="5" class="classification"/>
  <text x="500" y="435" class="text" fill="white">在院患者</text>
  <text x="500" y="450" class="text" fill="white">status = 1</text>
  
  <!-- 出院患者 (status=2) -->
  <rect x="650" y="420" width="100" height="40" rx="5" class="classification"/>
  <text x="700" y="435" class="text" fill="white">出院患者</text>
  <text x="700" y="450" class="text" fill="white">status = 2</text>
  
  <!-- 跳过患者 -->
  <rect x="850" y="420" width="100" height="40" rx="5" class="classification"/>
  <text x="900" y="435" class="text" fill="white">跳过患者</text>
  <text x="900" y="450" class="text" fill="white">status = null/其他</text>
  
  <!-- 添加到IN分类 -->
  <rect x="350" y="500" width="100" height="30" rx="5" class="classification"/>
  <text x="400" y="515" class="text" fill="white">添加到IN分类</text>
  
  <!-- 添加到UP分类 -->
  <rect x="470" y="500" width="100" height="30" rx="5" class="classification"/>
  <text x="520" y="515" class="text" fill="white">添加到UP分类</text>
  
  <!-- 添加到OUT分类 -->
  <rect x="650" y="500" width="100" height="30" rx="5" class="classification"/>
  <text x="700" y="515" class="text" fill="white">添加到OUT分类</text>
  
  <!-- 还有患者判断 -->
  <polygon points="700,570 750,590 700,610 650,590" class="decision"/>
  <text x="700" y="590" class="text">还有患者?</text>
  
  <!-- 处理各分类数据 -->
  <rect x="620" y="650" width="160" height="30" rx="5" class="process"/>
  <text x="700" y="665" class="text" fill="white">处理各分类数据</text>
  
  <!-- IN分类处理 -->
  <rect x="200" y="720" width="120" height="40" rx="5" class="storage"/>
  <text x="260" y="735" class="text" fill="white">处理IN分类患者</text>
  <text x="260" y="750" class="text" fill="white">转换为JSON</text>
  
  <!-- UP分类处理 -->
  <rect x="340" y="720" width="120" height="40" rx="5" class="storage"/>
  <text x="400" y="735" class="text" fill="white">处理UP分类患者</text>
  <text x="400" y="750" class="text" fill="white">转换为JSON</text>
  
  <!-- OUT分类处理 -->
  <rect x="480" y="720" width="120" height="40" rx="5" class="storage"/>
  <text x="540" y="735" class="text" fill="white">处理OUT分类患者</text>
  <text x="540" y="750" class="text" fill="white">转换为JSON</text>
  
  <!-- Redis键生成 -->
  <rect x="200" y="790" width="120" height="40" rx="5" class="storage"/>
  <text x="260" y="800" class="text" fill="white">生成Redis键</text>
  <text x="260" y="815" class="text" fill="white">hosptime_住院号_时间</text>
  
  <rect x="340" y="790" width="120" height="40" rx="5" class="storage"/>
  <text x="400" y="800" class="text" fill="white">生成Redis键</text>
  <text x="400" y="815" class="text" fill="white">hosptime_住院号_时间</text>
  
  <rect x="480" y="790" width="120" height="40" rx="5" class="storage"/>
  <text x="540" y="800" class="text" fill="white">生成Redis键</text>
  <text x="540" y="815" class="text" fill="white">hosptime_住院号_时间</text>
  
  <!-- 存储到Redis -->
  <rect x="150" y="860" width="140" height="50" rx="5" class="storage"/>
  <text x="220" y="875" class="text" fill="white">存储到Redis</text>
  <text x="220" y="890" class="text" fill="white">hospital:patient:type:</text>
  <text x="220" y="905" class="text" fill="white">{hospitalId}:in</text>
  
  <rect x="310" y="860" width="140" height="50" rx="5" class="storage"/>
  <text x="380" y="875" class="text" fill="white">存储到Redis</text>
  <text x="380" y="890" class="text" fill="white">hospital:patient:type:</text>
  <text x="380" y="905" class="text" fill="white">{hospitalId}:up</text>
  
  <rect x="470" y="860" width="140" height="50" rx="5" class="storage"/>
  <text x="540" y="875" class="text" fill="white">存储到Redis</text>
  <text x="540" y="890" class="text" fill="white">hospital:patient:type:</text>
  <text x="540" y="905" class="text" fill="white">{hospitalId}:out</text>
  
  <!-- 更新统计信息 -->
  <rect x="620" y="950" width="160" height="40" rx="5" class="process"/>
  <text x="700" y="965" class="text" fill="white">更新统计信息</text>
  <text x="700" y="980" class="text" fill="white">计算性能指标</text>
  
  <!-- 保存最终状态 -->
  <rect x="620" y="1020" width="160" height="40" rx="5" class="storage"/>
  <text x="700" y="1035" class="text" fill="white">保存最终状态到Redis</text>
  <text x="700" y="1050" class="text" fill="white">包含详细统计</text>
  
  <!-- 结束 -->
  <ellipse cx="700" cy="1110" rx="80" ry="25" class="start-end"/>
  <text x="700" y="1110" class="text" fill="white">患者数据处理完成</text>
  
  <!-- 错误处理 -->
  <rect x="1000" y="800" width="120" height="40" rx="5" class="error"/>
  <text x="1060" y="815" class="text" fill="white">JSON转换异常</text>
  <text x="1060" y="830" class="text" fill="white">处理错误</text>

  <rect x="1000" y="860" width="120" height="40" rx="5" class="error"/>
  <text x="1060" y="875" class="text" fill="white">Redis存储异常</text>
  <text x="1060" y="890" class="text" fill="white">处理错误</text>

  <!-- 连接线 -->
  <!-- 主流程 -->
  <line x1="700" y1="105" x2="700" y2="130" class="arrow"/>

  <!-- 数据为空判断 -->
  <line x1="750" y1="150" x2="850" y2="150" class="arrow"/>
  <text x="800" y="145" class="text" font-size="10">是</text>
  <line x1="700" y1="170" x2="700" y2="200" class="arrow"/>
  <text x="705" y="185" class="text" font-size="10">否</text>

  <!-- 初始化到遍历 -->
  <line x1="700" y1="240" x2="700" y2="270" class="arrow"/>
  <line x1="700" y1="300" x2="700" y2="330" class="arrow"/>

  <!-- 患者状态判断 -->
  <line x1="650" y1="350" x2="500" y2="420" class="arrow"/>
  <text x="575" y="385" class="text" font-size="10">status=1</text>
  <line x1="700" y1="370" x2="700" y2="420" class="arrow"/>
  <text x="705" y="395" class="text" font-size="10">status=2</text>
  <line x1="750" y1="350" x2="900" y2="420" class="arrow"/>
  <text x="825" y="385" class="text" font-size="10">其他</text>

  <!-- 在院患者分类 -->
  <line x1="500" y1="460" x2="400" y2="500" class="arrow"/>
  <line x1="500" y1="460" x2="520" y2="500" class="arrow"/>

  <!-- 出院患者分类 -->
  <line x1="700" y1="460" x2="700" y2="500" class="arrow"/>

  <!-- 回到判断循环 -->
  <line x1="400" y1="530" x2="400" y2="590" class="arrow"/>
  <line x1="520" y1="530" x2="650" y2="590" class="arrow"/>
  <line x1="700" y1="530" x2="700" y2="570" class="arrow"/>
  <line x1="900" y1="460" x2="900" y2="590" class="arrow"/>
  <line x1="900" y1="590" x2="750" y2="590" class="arrow"/>

  <!-- 循环判断 -->
  <line x1="650" y1="590" x2="580" y2="590" class="arrow"/>
  <line x1="580" y1="590" x2="580" y2="285" class="arrow"/>
  <line x1="580" y1="285" x2="620" y2="285" class="arrow"/>
  <text x="565" y="585" class="text" font-size="10">是</text>

  <line x1="700" y1="610" x2="700" y2="650" class="arrow"/>
  <text x="705" y="630" class="text" font-size="10">否</text>

  <!-- 处理各分类 -->
  <line x1="620" y1="665" x2="260" y2="720" class="arrow"/>
  <line x1="700" y1="680" x2="400" y2="720" class="arrow"/>
  <line x1="780" y1="665" x2="540" y2="720" class="arrow"/>

  <!-- JSON转换到键生成 -->
  <line x1="260" y1="760" x2="260" y2="790" class="arrow"/>
  <line x1="400" y1="760" x2="400" y2="790" class="arrow"/>
  <line x1="540" y1="760" x2="540" y2="790" class="arrow"/>

  <!-- 键生成到Redis存储 -->
  <line x1="260" y1="830" x2="220" y2="860" class="arrow"/>
  <line x1="400" y1="830" x2="380" y2="860" class="arrow"/>
  <line x1="540" y1="830" x2="540" y2="860" class="arrow"/>

  <!-- Redis存储到统计 -->
  <line x1="220" y1="910" x2="620" y2="970" class="arrow"/>
  <line x1="380" y1="910" x2="700" y2="950" class="arrow"/>
  <line x1="540" y1="910" x2="700" y2="950" class="arrow"/>

  <!-- 统计到最终状态 -->
  <line x1="700" y1="990" x2="700" y2="1020" class="arrow"/>

  <!-- 最终状态到结束 -->
  <line x1="700" y1="1060" x2="700" y2="1085" class="arrow"/>

  <!-- 无数据到结束 -->
  <line x1="900" y1="170" x2="900" y2="1110" class="arrow"/>
  <line x1="900" y1="1110" x2="780" y2="1110" class="arrow"/>

  <!-- 错误处理连接线 -->
  <line x1="600" y1="740" x2="1000" y2="820" class="arrow" stroke="#F44336"/>
  <line x1="610" y1="885" x2="1000" y2="880" class="arrow" stroke="#F44336"/>
  <line x1="1000" y1="820" x2="780" y2="970" class="arrow" stroke="#F44336"/>
  <line x1="1000" y1="880" x2="780" y2="970" class="arrow" stroke="#F44336"/>

  <!-- 图例 -->
  <g transform="translate(50, 1150)">
    <text x="0" y="0" class="text" font-weight="bold">图例:</text>
    <rect x="0" y="10" width="20" height="15" class="start-end"/>
    <text x="25" y="22" class="text" font-size="10">开始/结束</text>
    <rect x="80" y="10" width="20" height="15" class="process"/>
    <text x="105" y="22" class="text" font-size="10">处理过程</text>
    <polygon points="160,10 175,17.5 160,25 145,17.5" class="decision"/>
    <text x="180" y="22" class="text" font-size="10">判断</text>
    <rect x="220" y="10" width="20" height="15" class="classification"/>
    <text x="245" y="22" class="text" font-size="10">分类处理</text>
    <rect x="300" y="10" width="20" height="15" class="storage"/>
    <text x="325" y="22" class="text" font-size="10">存储</text>
    <rect x="360" y="10" width="20" height="15" class="error"/>
    <text x="385" y="22" class="text" font-size="10">错误处理</text>
  </g>
</svg>
