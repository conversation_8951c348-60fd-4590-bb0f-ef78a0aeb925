version: '3.8'

services:
  # 应用服务
  sysgetway:
    build:
      context: .
      dockerfile: Dockerfile
    image: sysgetway:1.0.0
    container_name: sysgetway
    ports:
      - "9000:9000"
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DATABASE=9
      - JVM_OPTS=-Xms512m -Xmx1024m
    volumes:
      - ./logs:/app/logs
    depends_on:
      - redis
    networks:
      - sysgetway-network



  # Redis服务
  redis:
    image: redis:6.2
    container_name: sysgetway-redis
    ports:
      - "16379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - sysgetway-network

# 持久化卷
volumes:
  redis-data:

# 网络配置
networks:
  sysgetway-network:
    driver: bridge 