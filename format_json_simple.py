#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的JSON文件格式化工具
用于将JSON文件格式化，使其具有良好的缩进和换行
"""

import json
import sys
import os
import re

def format_json_file(input_file, output_file=None):
    """
    格式化JSON文件
    
    参数:
        input_file: 输入文件路径
        output_file: 输出文件路径，如果为None，则覆盖原文件
    """
    print(f"正在读取文件: {input_file}")
    
    try:
        # 读取文件内容
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试找到JSON数组的开始和结束
        start_idx = content.find('[')
        end_idx = content.rfind(']')
        
        if start_idx == -1 or end_idx == -1:
            print("错误: 无法找到JSON数组的开始或结束")
            return False
        
        # 提取JSON数组内容
        json_content = content[start_idx:end_idx+1]
        
        # 解析JSON
        try:
            data = json.loads(json_content)
        except json.JSONDecodeError as e:
            print(f"错误: JSON解析失败 - {e}")
            
            # 尝试修复常见的JSON格式问题
            print("尝试修复JSON格式问题...")
            
            # 1. 替换可能导致问题的字符
            json_content = json_content.replace('\\"', '"')
            json_content = json_content.replace('\\\\', '\\')
            
            # 2. 处理可能的编码问题
            json_content = json_content.encode('utf-8', 'ignore').decode('utf-8')
            
            # 3. 尝试使用正则表达式提取所有JSON对象
            print("尝试使用正则表达式提取JSON对象...")
            pattern = r'\{[^{}]*\}'
            objects = re.findall(pattern, json_content)
            
            if objects:
                print(f"找到 {len(objects)} 个JSON对象")
                # 重新构建JSON数组
                json_content = "[" + ",".join(objects) + "]"
                
                try:
                    data = json.loads(json_content)
                    print("通过正则表达式修复成功")
                except json.JSONDecodeError as e:
                    print(f"通过正则表达式修复失败: {e}")
                    return False
            else:
                print("无法提取JSON对象")
                return False
        
        # 如果未指定输出文件，则覆盖原文件
        if output_file is None:
            output_file = input_file
        
        # 写入格式化的JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        # 获取文件大小
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
        
        print(f"格式化完成，已保存到: {output_file}")
        print(f"文件大小: {file_size:.2f} MB")
        print(f"对象数量: {len(data)}")
        
    except Exception as e:
        print(f"错误: {e}")
        return False
    
    return True

def main():
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("用法: python format_json_simple.py <输入文件> [输出文件]")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    format_json_file(input_file, output_file)

if __name__ == "__main__":
    main() 