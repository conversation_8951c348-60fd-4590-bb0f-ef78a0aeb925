# 数据同步轮询状态序列图

## 概述
本文档描述了数据同步系统中调用方与服务端之间的交互流程，包括异步任务的创建和状态轮询机制。

## 序列图

```mermaid
sequenceDiagram
    participant 调用方
    participant 服务端
    
    调用方->>服务端: 调用某个数据同步作业接口
    Note over 服务端: 参数中有说明就调用下对应的作业配置参数
    服务端-->>调用方: 返回任务ID
    
    调用方->>服务端: 调用获取作业结果接口
    Note over 服务端: 轮询调用 status = 1 结束轮询
    服务端-->>调用方: 返回任务结果
    
    Note over 调用方,服务端: 轮询状态说明:<br/>1: 同步成功，结束轮询<br/>0: 同步进行中，继续轮询<br/>-1: 同步失败，停止轮询
```

## 状态码说明

| 状态码 | 状态名称 | 描述 | 轮询行为 |
|--------|----------|------|----------|
| 1 | SUCCESS | 同步成功 | 结束轮询 |
| 0 | IN_PROGRESS | 同步进行中 | 继续轮询 |
| -1 | FAILED | 同步失败 | 停止轮询 |

## 接口说明

### 1. 数据同步作业接口
- **全量同步**: `/api/sync/full/{hospitalId}/{tableName}`
- **增量同步**: `/api/sync/incremental/{hospitalId}/{tableName}`
- **患者分类同步**: `/api/sync/patient/{hospitalId}/{patientType}`

### 2. 状态查询接口
- **获取同步状态**: `/api/sync/status/{hospitalId}/{tableName}`
- **获取任务状态**: `/api/sync/task/{taskId}`

## 轮询建议

1. **轮询间隔**: 建议每2-5秒轮询一次状态
2. **超时机制**: 建议设置最大轮询时间（如30分钟）
3. **错误处理**: 当状态为-1时，应停止轮询并处理错误
4. **成功处理**: 当状态为1时，可获取同步结果数据

## 示例代码

### JavaScript轮询示例
```javascript
async function pollSyncStatus(taskId, maxAttempts = 360, interval = 5000) {
    for (let i = 0; i < maxAttempts; i++) {
        try {
            const response = await fetch(`/api/sync/task/${taskId}`);
            const result = await response.json();
            
            if (result.data.status === 1) {
                console.log('同步成功:', result.data);
                return result.data;
            } else if (result.data.status === -1) {
                console.error('同步失败:', result.data.message);
                throw new Error(result.data.message);
            }
            
            // status === 0, 继续轮询
            console.log('同步进行中，继续轮询...');
            await new Promise(resolve => setTimeout(resolve, interval));
            
        } catch (error) {
            console.error('轮询出错:', error);
            throw error;
        }
    }
    
    throw new Error('轮询超时');
}
```

### Java轮询示例
```java
public SyncResultDTO pollSyncStatus(String taskId, int maxAttempts, long intervalMs) 
        throws InterruptedException {
    for (int i = 0; i < maxAttempts; i++) {
        ResponseResult<SyncResultDTO> response = syncService.getTaskStatus(taskId);
        SyncResultDTO result = response.getData();
        
        if (result.getStatus() == SyncConstants.SyncStatus.SUCCESS) {
            log.info("同步成功: {}", result);
            return result;
        } else if (result.getStatus() == SyncConstants.SyncStatus.FAILED) {
            log.error("同步失败: {}", result.getMessage());
            throw new RuntimeException(result.getMessage());
        }
        
        // status == IN_PROGRESS, 继续轮询
        log.info("同步进行中，继续轮询...");
        Thread.sleep(intervalMs);
    }
    
    throw new RuntimeException("轮询超时");
}
```

## 注意事项

1. 轮询过程中应该处理网络异常和服务异常
2. 建议实现指数退避策略，避免频繁请求
3. 对于长时间运行的同步任务，可以适当增加轮询间隔
4. 客户端应该实现超时机制，避免无限轮询
