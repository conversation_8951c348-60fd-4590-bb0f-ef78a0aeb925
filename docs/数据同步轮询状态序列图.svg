<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .participant { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .message { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .note { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; }
      .status-note { font-family: Arial, sans-serif; font-size: 10px; text-anchor: start; }
      .participant-box { fill: #e1f5fe; stroke: #0277bd; stroke-width: 2; }
      .lifeline { stroke: #666; stroke-width: 1; stroke-dasharray: 5,5; }
      .message-arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #666; stroke-width: 1.5; fill: none; marker-end: url(#returnhead); stroke-dasharray: 8,4; }
      .note-box { fill: #fff3e0; stroke: #f57c00; stroke-width: 1; }
      .status-box { fill: #f3e5f5; stroke: #7b1fa2; stroke-width: 1; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="returnhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="30" class="title">数据同步轮询状态序列图</text>
  
  <!-- 参与者 -->
  <rect x="120" y="60" width="120" height="40" class="participant-box" rx="5"/>
  <text x="180" y="85" class="participant">HIS系统</text>
  
  <rect x="560" y="60" width="120" height="40" class="participant-box" rx="5"/>
  <text x="620" y="85" class="participant">服务端</text>
  
  <!-- 生命线 -->
  <line x1="180" y1="100" x2="180" y2="520" class="lifeline"/>
  <line x1="620" y1="100" x2="620" y2="520" class="lifeline"/>
  
  <!-- 消息1: 调用数据同步接口 -->
  <line x1="180" y1="140" x2="620" y2="140" class="message-arrow"/>
  <text x="400" y="135" class="message">调用某个数据同步作业接口</text>
  
  <!-- 注释1: 参数说明 -->
  <rect x="540" y="155" width="160" height="30" class="note-box" rx="3"/>
  <text x="620" y="175" class="note">参数中有说明就调用下对应的作业配置参数</text>
  
  <!-- 返回消息1: 返回任务ID -->
  <line x1="620" y1="210" x2="180" y2="210" class="return-arrow"/>
  <text x="400" y="205" class="message">返回任务ID</text>
  
  <!-- 消息2: 调用获取结果接口 -->
  <line x1="180" y1="250" x2="620" y2="250" class="message-arrow"/>
  <text x="400" y="245" class="message">调用获取作业结果接口</text>
  
  <!-- 注释2: 轮询说明 -->
  <rect x="540" y="265" width="160" height="30" class="note-box" rx="3"/>
  <text x="620" y="285" class="note">轮询调用 status = 1 结束轮询</text>
  
  <!-- 返回消息2: 返回任务结果 -->
  <line x1="620" y1="320" x2="180" y2="320" class="return-arrow"/>
  <text x="400" y="315" class="message">返回任务结果</text>
  
  <!-- 状态说明框 -->
  <rect x="200" y="360" width="400" height="120" class="status-box" rx="5"/>
  <text x="400" y="385" class="note" style="font-weight: bold;">轮询状态说明</text>
  
  <text x="220" y="410" class="status-note">1: 同步成功，结束轮询</text>
  <text x="220" y="430" class="status-note">0: 同步进行中，继续轮询</text>
  <text x="220" y="450" class="status-note">-1: 同步失败，停止轮询</text>
  
  <!-- 状态码图例 -->
  <circle cx="210" cy="405" r="4" fill="#4caf50"/>
  <circle cx="210" cy="425" r="4" fill="#ff9800"/>
  <circle cx="210" cy="445" r="4" fill="#f44336"/>
  
  <!-- 底部说明 -->
  <text x="400" y="510" class="note" style="font-size: 10px; fill: #666;">
    基于SysGateway数据同步系统 - 支持全量同步、增量同步、患者分类同步
  </text>
</svg>
