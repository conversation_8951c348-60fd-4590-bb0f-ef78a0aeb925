spring:
  # 数据库配置
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
#  datasource:
#    driver-class-name: com.mysql.jdbc.Driver
#    url: jdbc:mysql://${DB_HOST:**************}:${DB_PORT:13306}/${DB_NAME:sysgetway}?useUnicode=true&characterEncoding=utf8&useSSL=false
#    username: ${DB_USERNAME:root}
#    password: ${DB_PASSWORD:Aa123456}
#    type: com.zaxxer.hikari.HikariDataSource
#    hikari:
#      minimum-idle: 5
#      maximum-pool-size: 15
#      auto-commit: true
#      idle-timeout: 30000
#      pool-name: HikariCP
#      max-lifetime: 1800000
#      connection-timeout: 30000
#      connection-test-query: SELECT 1

  # Redis配置
  redis:
    # host: ${REDIS_HOST:**************}
    host: ${REDIS_HOST:**************}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:9}
    # 增加超时时间，避免长时间等待
    timeout: 5000
    # 使用Lettuce客户端
    lettuce:
      pool:
        # 最大活跃连接数，增加以支持更多并发连接
        max-active: 64
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: 1000
        # 连接池中的最大空闲连接
        max-idle: 32
        # 连接池中的最小空闲连接
        min-idle: 16
        # 关闭空闲连接检测
      shutdown-timeout: 100ms

# 自定义配置
sysgetway:
  # 跨域配置
  cors:
    enabled: true
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    exposed-headers: "Authorization,Content-Type"
    allow-credentials: true
    max-age: 3600

  # 线程池配置
  thread-pool:
    core-pool-size: 10
    max-pool-size: 20
    queue-capacity: 100
    keep-alive-seconds: 60
    thread-name-prefix: sysgetway-task-
