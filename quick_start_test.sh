#!/bin/bash

# SysGetway 快速开始测试脚本
# 用于快速验证系统功能和API接口
# 使用方法: ./quick_start_test.sh

BASE_URL="http://localhost:9000/sysgetway"
HOSPITAL_ID="H001"

echo "=========================================="
echo "=== SysGetway 快速开始测试 ==="
echo "=========================================="
echo "基础URL: $BASE_URL"
echo "医院ID: $HOSPITAL_ID"
echo "测试时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""

# 函数：检查服务是否可用
check_service() {
    echo "检查服务状态..."
    
    # 检查健康状态
    HEALTH_RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/api/monitor/health" 2>/dev/null)
    
    if [ "$HEALTH_RESPONSE" = "200" ]; then
        echo "✅ 服务运行正常"
        return 0
    else
        echo "❌ 服务不可用 (HTTP状态码: $HEALTH_RESPONSE)"
        echo ""
        echo "请确保："
        echo "1. 应用已启动 (java -jar sysgetway.jar)"
        echo "2. 端口9000未被占用"
        echo "3. Redis服务正在运行"
        echo "4. 数据库连接正常"
        return 1
    fi
}

# 函数：等待并查询任务状态
wait_and_check_task() {
    local response="$1"
    local task_name="$2"
    
    TASK_ID=$(echo "$response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('taskId', ''))" 2>/dev/null)
    
    if [ ! -z "$TASK_ID" ]; then
        echo "任务ID: $TASK_ID"
        echo "等待3秒后查询任务状态..."
        sleep 3
        
        echo "[$task_name] 任务状态:"
        TASK_STATUS=$(curl -s -X GET "$BASE_URL/api/sync/task/$TASK_ID")
        echo "$TASK_STATUS" | python3 -m json.tool 2>/dev/null || echo "$TASK_STATUS"
        echo ""
        
        # 检查任务是否成功
        STATUS=$(echo "$TASK_STATUS" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('data', {}).get('status', ''))" 2>/dev/null)
        if [ "$STATUS" = "COMPLETED" ]; then
            echo "✅ $task_name 完成"
        elif [ "$STATUS" = "FAILED" ]; then
            echo "❌ $task_name 失败"
        else
            echo "⏳ $task_name 状态: $STATUS"
        fi
    else
        echo "❌ 无法获取任务ID"
    fi
    echo ""
}

# 检查服务状态
if ! check_service; then
    exit 1
fi

echo ""
echo "开始快速功能测试..."
echo ""

# 测试1: 科室数据同步
echo "===========================================" 
echo "测试1: 科室数据同步"
echo "==========================================="

DEPT_DATA='[
  {
    "departmentId": 1,
    "code": "NK",
    "name": "内科",
    "updatedAt": "2024-07-18T10:00:00"
  },
  {
    "departmentId": 2,
    "code": "WK",
    "name": "外科",
    "updatedAt": "2024-07-18T10:00:00"
  }
]'

echo "同步2个科室数据..."
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/department" \
  -H "Content-Type: application/json" \
  -d "$DEPT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

wait_and_check_task "$RESPONSE" "科室数据同步"

# 测试2: 医护人员数据同步
echo "===========================================" 
echo "测试2: 医护人员数据同步"
echo "==========================================="

USER_DATA='[
  {
    "userName": "D001",
    "name": "张医生",
    "sex": 1,
    "roleId": 1,
    "deptId": 1,
    "mobile": "13800138001",
    "inpatientWard": "内科一病区"
  },
  {
    "userName": "N001",
    "name": "李护士",
    "sex": 2,
    "roleId": 2,
    "deptId": 1,
    "mobile": "13800138002",
    "inpatientWard": "内科一病区"
  }
]'

echo "同步2个医护人员数据..."
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/user" \
  -H "Content-Type: application/json" \
  -d "$USER_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

wait_and_check_task "$RESPONSE" "医护人员数据同步"

# 测试3: 患者数据同步
echo "===========================================" 
echo "测试3: 患者数据同步"
echo "==========================================="

PATIENT_DATA='[
  {
    "inpatientInfoId": "P001",
    "name": "张三",
    "hospitalizationNo": "H001",
    "inhospitalTime": "2024-07-18T10:00:00",
    "status": 1,
    "deptId": 1,
    "category": "医保"
  },
  {
    "inpatientInfoId": "P002",
    "name": "李四",
    "hospitalizationNo": "H002",
    "inhospitalTime": "2024-07-10T10:00:00",
    "outhospitalTime": "2024-07-17T14:00:00",
    "status": 2,
    "deptId": 2,
    "category": "自费"
  }
]'

echo "同步2个患者数据（1个在院，1个出院）..."
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/full/$HOSPITAL_ID/patient" \
  -H "Content-Type: application/json" \
  -d "$PATIENT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

wait_and_check_task "$RESPONSE" "患者数据同步"

# 测试4: 患者分类同步
echo "===========================================" 
echo "测试4: 患者分类同步"
echo "==========================================="

IN_PATIENT_DATA='[
  {
    "inpatientInfoId": "P003",
    "name": "王五",
    "hospitalizationNo": "H003",
    "inhospitalTime": "2024-07-18T14:00:00",
    "status": 1,
    "deptId": 1,
    "category": "医保"
  }
]'

echo "同步1个入院患者数据..."
RESPONSE=$(curl -s -X PUT "$BASE_URL/api/sync/patient/$HOSPITAL_ID/in" \
  -H "Content-Type: application/json" \
  -d "$IN_PATIENT_DATA")

echo "响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
echo ""

wait_and_check_task "$RESPONSE" "入院患者同步"

echo "=========================================="
echo "=== 测试完成 ==="
echo "=========================================="
echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo "测试总结："
echo "✅ 科室数据同步测试"
echo "✅ 医护人员数据同步测试"
echo "✅ 患者数据同步测试（自动分类）"
echo "✅ 患者分类同步测试"
echo ""
echo "下一步："
echo "1. 查看API文档: http://localhost:9000/sysgetway/doc.html"
echo "2. 运行完整测试: ./test_all_sync_apis.sh"
echo "3. 查看详细示例: API请求示例文档.md"
echo "4. 监控系统状态: http://localhost:9000/sysgetway/api/monitor/health"
echo ""
echo "如有问题，请检查："
echo "- 应用日志: logs/system.log, logs/business.log"
echo "- Redis连接状态"
echo "- 数据库连接状态"
