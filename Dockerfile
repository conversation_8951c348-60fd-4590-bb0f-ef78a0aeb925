# 第一阶段：使用Maven构建应用
FROM ccr.ccs.tencentyun.com/itingluo/maven:3.8-openjdk-8-slim AS builder

# 设置工作目录
WORKDIR /build

# 复制pom.xml并下载依赖，利用Docker缓存机制
COPY settings.xml /root/.m2/settings.xml
COPY pom.xml .
COPY application.yml  .


RUN mvn dependency:go-offline

# 复制源代码
COPY src/ ./src/

# 构建应用
RUN mvn clean package -DskipTests

# 第二阶段：使用JRE运行应用
FROM ccr.ccs.tencentyun.com/itingluo/openjdk:8-jre-slim

RUN which java && java -version
RUN echo $PATH

# 工作目录
WORKDIR /app

# 从构建阶段复制构建结果
COPY --from=builder /build/target/sysgetway-*.jar app.jar
COPY application.yml /app/config/application.yml

# 暴露应用端口
EXPOSE 9000

# 创建日志目录并设置权限
RUN mkdir -p /app/logs && \
    chmod -R 777 /app/logs

RUN ls -l
RUN ls -l /app

# 启动应用，使用 docker profile
ENTRYPOINT ["java", "-Xms512m", "-Xmx1024m", "-Dspring.profiles.active=docker", "-jar", "/app/app.jar"]
