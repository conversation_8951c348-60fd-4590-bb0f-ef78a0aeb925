<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .client { fill: #4CAF50; stroke: #2E7D32; stroke-width: 3; }
      .api { fill: #FF9800; stroke: #E65100; stroke-width: 2; }
      .response { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
      .query { fill: #9C27B0; stroke: #6A1B9A; stroke-width: 2; }
      .success { fill: #4CAF50; stroke: #2E7D32; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; dominant-baseline: middle; font-weight: 500; }
      .title { font-family: Arial, sans-serif; font-size: 28px; font-weight: bold; text-anchor: middle; }
      .subtitle { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .code { font-family: 'Courier New', monospace; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
      .arrow { stroke: #333; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .return-arrow { stroke: #2196F3; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .query-arrow { stroke: #9C27B0; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 6,3; }
    </style>
    
    <marker id="arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#333" />
    </marker>
  </defs>
  
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="1200" height="900" fill="url(#bgGradient)"/>
  
  <!-- 标题 -->
  <text x="600" y="40" class="title" fill="#333">数据同步接口对接流程</text>
  <text x="600" y="70" class="subtitle" fill="#666">API Integration Flow for Data Synchronization</text>
  
  <!-- 客户端 -->
  <rect x="50" y="120" width="150" height="60" rx="10" class="client"/>
  <text x="125" y="145" class="text" fill="white">客户端系统</text>
  <text x="125" y="165" class="text" fill="white">Client System</text>
  
  <!-- 同步网关 -->
  <rect x="950" y="120" width="150" height="60" rx="10" class="api"/>
  <text x="1025" y="145" class="text" fill="white">同步网关</text>
  <text x="1025" y="165" class="text" fill="white">Sync Gateway</text>
  
  <!-- 步骤1: 发起同步请求 -->
  <rect x="250" y="220" width="700" height="50" rx="8" class="api"/>
  <text x="600" y="235" class="text" fill="white">步骤1: 发起全量同步请求</text>
  <text x="600" y="255" class="code" fill="white">PUT /api/sync/full/{hospitalId}/{tableName}</text>
  
  <!-- 步骤2: 立即返回任务状态 -->
  <rect x="250" y="300" width="700" height="70" rx="8" class="response"/>
  <text x="600" y="320" class="text" fill="white">步骤2: 立即返回任务状态 (异步处理)</text>
  <text x="600" y="340" class="code" fill="white">{"status": 0, "message": "同步进行中", "taskId": "task_123456"}</text>
  <text x="600" y="355" class="code" fill="white">{"hospitalId": "hospital001", "startTime": "2024-01-01T10:00:00"}</text>
  
  <!-- 步骤3: 查询任务状态 -->
  <rect x="250" y="400" width="700" height="50" rx="8" class="query"/>
  <text x="600" y="415" class="text" fill="white">步骤3: 查询任务执行状态 (可选)</text>
  <text x="600" y="435" class="code" fill="white">GET /api/sync/status/{taskId}</text>
  
  <!-- 步骤4: 返回详细状态 -->
  <rect x="250" y="480" width="700" height="70" rx="8" class="response"/>
  <text x="600" y="500" class="text" fill="white">步骤4: 返回详细执行状态</text>
  <text x="600" y="520" class="code" fill="white">{"status": 1, "message": "同步成功", "count": 1500}</text>
  <text x="600" y="535" class="code" fill="white">{"costTime": 30000, "endTime": "2024-01-01T10:00:30"}</text>
  
  <!-- 连接箭头 -->
  <line x1="200" y1="150" x2="250" y2="245" class="arrow"/>
  <line x1="950" y1="245" x2="1000" y2="150" class="return-arrow"/>
  
  <line x1="950" y1="335" x2="1000" y2="150" class="return-arrow"/>
  
  <line x1="200" y1="150" x2="250" y2="425" class="query-arrow"/>
  <line x1="950" y1="515" x2="1000" y2="150" class="return-arrow"/>
  
  <!-- 接口详细说明 -->
  <g transform="translate(50, 580)">
    <rect x="0" y="0" width="550" height="280" rx="10" fill="white" stroke="#dee2e6" stroke-width="2"/>
    <text x="275" y="25" class="subtitle" fill="#495057">接口详细说明</text>
    
    <!-- 全量同步接口 -->
    <text x="20" y="50" class="text" font-size="16" font-weight="bold" fill="#E65100">1. 全量同步接口</text>
    <text x="20" y="70" class="code" font-size="12" fill="#495057">PUT /api/sync/full/{hospitalId}/{tableName}</text>
    <text x="20" y="90" class="text" font-size="12" fill="#495057">• hospitalId: 医院ID (必填)</text>
    <text x="20" y="105" class="text" font-size="12" fill="#495057">• tableName: 表名 (department/user/patient)</text>
    <text x="20" y="120" class="text" font-size="12" fill="#495057">• 请求体: JSON数组 (可选，不传则从外部系统获取)</text>
    <text x="20" y="135" class="text" font-size="12" fill="#495057">• 返回: 立即返回任务状态，包含taskId</text>
    
    <!-- 状态查询接口 -->
    <text x="20" y="160" class="text" font-size="16" font-weight="bold" fill="#7B1FA2">2. 状态查询接口</text>
    <text x="20" y="180" class="code" font-size="12" fill="#495057">GET /api/sync/status/{taskId}</text>
    <text x="20" y="200" class="text" font-size="12" fill="#495057">• taskId: 任务ID (从步骤1返回结果中获取)</text>
    <text x="20" y="215" class="text" font-size="12" fill="#495057">• 返回: 详细的任务执行状态和统计信息</text>
    
    <!-- 增量同步接口 -->
    <text x="20" y="240" class="text" font-size="16" font-weight="bold" fill="#388E3C">3. 增量同步接口</text>
    <text x="20" y="260" class="code" font-size="12" fill="#495057">PUT /api/sync/incremental/{hospitalId}/{tableName}</text>
    <text x="20" y="280" class="text" font-size="12" fill="#495057">• 基于最后同步时间进行增量数据同步</text>
  </g>
  
  <!-- 状态码说明 -->
  <g transform="translate(620, 580)">
    <rect x="0" y="0" width="530" height="280" rx="10" fill="white" stroke="#dee2e6" stroke-width="2"/>
    <text x="265" y="25" class="subtitle" fill="#495057">状态码说明</text>
    
    <!-- 同步状态 -->
    <text x="20" y="50" class="text" font-size="16" font-weight="bold" fill="#1976D2">同步状态 (status)</text>
    <text x="20" y="70" class="text" font-size="12" fill="#495057">• 0: 同步进行中 (IN_PROGRESS)</text>
    <text x="20" y="85" class="text" font-size="12" fill="#495057">• 1: 同步成功 (SUCCESS)</text>
    <text x="20" y="100" class="text" font-size="12" fill="#495057">• -1: 同步失败 (FAILED)</text>
    
    <!-- 返回字段 -->
    <text x="20" y="125" class="text" font-size="16" font-weight="bold" fill="#1976D2">主要返回字段</text>
    <text x="20" y="145" class="text" font-size="12" fill="#495057">• taskId: 任务唯一标识</text>
    <text x="20" y="160" class="text" font-size="12" fill="#495057">• hospitalId: 医院ID</text>
    <text x="20" y="175" class="text" font-size="12" fill="#495057">• tableNameOrPatientType: 表名或患者类型</text>
    <text x="20" y="190" class="text" font-size="12" fill="#495057">• count: 同步数据总数</text>
    <text x="20" y="205" class="text" font-size="12" fill="#495057">• costTime: 耗时(毫秒)</text>
    <text x="20" y="220" class="text" font-size="12" fill="#495057">• startTime/endTime: 开始/结束时间</text>
    <text x="20" y="235" class="text" font-size="12" fill="#495057">• errorMessage: 错误信息(如果失败)</text>
    
    <!-- 注意事项 -->
    <text x="20" y="260" class="text" font-size="14" font-weight="bold" fill="#F57C00">⚠️ 注意: 异步处理，需轮询查询状态</text>
  </g>
  
  <!-- 时间轴 -->
  <g transform="translate(100, 100)">
    <line x1="0" y1="0" x2="0" y2="450" stroke="#dee2e6" stroke-width="2"/>
    <text x="-15" y="120" class="text" font-size="12" fill="#6c757d" transform="rotate(-90, -15, 120)">时间轴</text>
    
    <!-- 时间点标记 -->
    <circle cx="0" cy="120" r="5" fill="#4CAF50"/>
    <text x="15" y="125" class="text" font-size="11" fill="#6c757d">T0: 发起请求</text>
    
    <circle cx="0" cy="200" r="5" fill="#2196F3"/>
    <text x="15" y="205" class="text" font-size="11" fill="#6c757d">T0+50ms: 立即响应</text>
    
    <circle cx="0" cy="300" r="5" fill="#9C27B0"/>
    <text x="15" y="305" class="text" font-size="11" fill="#6c757d">T0+5s: 查询状态</text>
    
    <circle cx="0" cy="380" r="5" fill="#4CAF50"/>
    <text x="15" y="385" class="text" font-size="11" fill="#6c757d">T0+30s: 同步完成</text>
  </g>
  
  <!-- 示例代码框 -->
  <g transform="translate(950, 300)">
    <rect x="0" y="0" width="200" height="120" rx="8" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
    <text x="100" y="20" class="text" font-size="14" font-weight="bold" fill="#495057">示例请求</text>
    
    <text x="10" y="40" class="code" font-size="10" fill="#495057">curl -X PUT \</text>
    <text x="10" y="55" class="code" font-size="10" fill="#495057">  /api/sync/full/hospital001/patient \</text>
    <text x="10" y="70" class="code" font-size="10" fill="#495057">  -H "Content-Type: application/json" \</text>
    <text x="10" y="85" class="code" font-size="10" fill="#495057">  -d '[{"id":1,"name":"张三"}]'</text>
    
    <text x="10" y="105" class="text" font-size="11" fill="#28a745">✓ 立即返回taskId</text>
  </g>
</svg>
