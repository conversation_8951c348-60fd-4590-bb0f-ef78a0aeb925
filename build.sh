#!/bin/bash

# 设置镜像名称和标签
IMAGE_NAME="sysgetway"
IMAGE_TAG="1.0.0"

# 打印操作信息
echo "=== 开始构建 ${IMAGE_NAME}:${IMAGE_TAG} Docker镜像 ==="

# 构建Docker镜像
docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "=== Docker镜像构建成功: ${IMAGE_NAME}:${IMAGE_TAG} ==="
    
    # 将镜像保存为本地tar文件
    echo "=== 正在将镜像保存为本地tar文件 ==="
    docker save -o ${IMAGE_NAME}-${IMAGE_TAG}.tar ${IMAGE_NAME}:${IMAGE_TAG}
    
    if [ $? -eq 0 ]; then
        echo "=== 镜像已保存为: ${IMAGE_NAME}-${IMAGE_TAG}.tar ==="
        echo ""
        echo "=== 使用方法 ==="
        echo "1. 加载镜像: docker load -i ${IMAGE_NAME}-${IMAGE_TAG}.tar"
        echo "2. 运行容器: docker run -d -p 9000:9000 --name ${IMAGE_NAME} ${IMAGE_NAME}:${IMAGE_TAG}"
        echo ""
        echo "如需自定义配置，可添加以下环境变量:"
        echo "  -e SPRING_PROFILES_ACTIVE=prod \\"
        echo "  -e DB_HOST=your-db-host \\"
        echo "  -e DB_PORT=your-db-port \\"
        echo "  -e DB_NAME=your-db-name \\"
        echo "  -e DB_USERNAME=your-db-username \\"
        echo "  -e DB_PASSWORD=your-db-password \\"
        echo "  -e REDIS_HOST=your-redis-host \\"
        echo "  -e REDIS_PORT=your-redis-port \\"
        echo "  -e REDIS_PASSWORD=your-redis-password \\"
        echo "  -e REDIS_DATABASE=your-redis-database \\"
        echo "  -e JVM_OPTS=\"-Xms512m -Xmx1024m\""
    else
        echo "=== 镜像保存失败 ==="
    fi
else
    echo "=== Docker镜像构建失败 ==="
    exit 1
fi 