<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .client { fill: #E3F2FD; stroke: #1976D2; stroke-width: 2; }
      .controller { fill: #F3E5F5; stroke: #7B1FA2; stroke-width: 2; }
      .service { fill: #E8F5E8; stroke: #388E3C; stroke-width: 2; }
      .task { fill: #FFF3E0; stroke: #F57C00; stroke-width: 2; }
      .storage { fill: #FCE4EC; stroke: #C2185B; stroke-width: 2; }
      .external { fill: #F1F8E9; stroke: #689F38; stroke-width: 2; }
      .model { fill: #E0F2F1; stroke: #00796B; stroke-width: 2; }
      .config { fill: #FAFAFA; stroke: #616161; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; text-anchor: middle; }
      .layer-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #666; stroke-width: 1.5; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" class="title" fill="#333">数据同步系统架构图</text>
  
  <!-- 客户端层 -->
  <rect x="50" y="80" width="300" height="120" rx="10" class="client" fill-opacity="0.3"/>
  <text x="200" y="100" class="layer-title" fill="#1976D2">客户端层</text>
  
  <rect x="80" y="120" width="120" height="50" rx="5" class="client"/>
  <text x="140" y="145" class="text">客户端应用</text>
  
  <rect x="220" y="120" width="100" height="50" rx="5" class="client"/>
  <text x="270" y="140" class="text">API文档</text>
  <text x="270" y="155" class="text">Swagger</text>
  
  <!-- 控制器层 -->
  <rect x="50" y="230" width="300" height="120" rx="10" class="controller" fill-opacity="0.3"/>
  <text x="200" y="250" class="layer-title" fill="#7B1FA2">控制器层</text>
  
  <rect x="80" y="270" width="120" height="50" rx="5" class="controller"/>
  <text x="140" y="290" class="text">SyncController</text>
  <text x="140" y="305" class="text">同步控制器</text>
  
  <rect x="220" y="270" width="100" height="50" rx="5" class="controller"/>
  <text x="270" y="290" class="text">MonitorController</text>
  <text x="270" y="305" class="text">监控控制器</text>
  
  <!-- 服务层 -->
  <rect x="50" y="380" width="300" height="120" rx="10" class="service" fill-opacity="0.3"/>
  <text x="200" y="400" class="layer-title" fill="#388E3C">服务层</text>
  
  <rect x="80" y="420" width="100" height="50" rx="5" class="service"/>
  <text x="130" y="440" class="text">SyncService</text>
  <text x="130" y="455" class="text">同步服务接口</text>
  
  <rect x="200" y="420" width="120" height="50" rx="5" class="service"/>
  <text x="260" y="440" class="text">SyncServiceImpl</text>
  <text x="260" y="455" class="text">同步服务实现</text>
  
  <!-- 任务管理层 -->
  <rect x="400" y="80" width="350" height="200" rx="10" class="task" fill-opacity="0.3"/>
  <text x="575" y="100" class="layer-title" fill="#F57C00">任务管理层</text>
  
  <rect x="430" y="120" width="140" height="50" rx="5" class="task"/>
  <text x="500" y="140" class="text">AsyncTaskManager</text>
  <text x="500" y="155" class="text">异步任务管理器</text>
  
  <rect x="590" y="120" width="130" height="50" rx="5" class="task"/>
  <text x="655" y="140" class="text">syncTaskExecutor</text>
  <text x="655" y="155" class="text">线程池</text>
  
  <rect x="430" y="190" width="140" height="50" rx="5" class="task"/>
  <text x="500" y="210" class="text">CompletableFuture</text>
  <text x="500" y="225" class="text">异步任务</text>
  
  <!-- 数据存储层 -->
  <rect x="400" y="310" width="350" height="190" rx="10" class="storage" fill-opacity="0.3"/>
  <text x="575" y="330" class="layer-title" fill="#C2185B">数据存储层</text>
  
  <ellipse cx="500" cy="380" rx="60" ry="40" class="storage"/>
  <text x="500" y="375" class="text">Redis缓存</text>
  <text x="500" y="390" class="text">数据存储</text>
  
  <rect x="590" y="350" width="120" height="50" rx="5" class="storage"/>
  <text x="650" y="370" class="text">RedisUtils</text>
  <text x="650" y="385" class="text">Redis工具类</text>
  
  <!-- Redis存储结构 -->
  <rect x="430" y="420" width="280" height="60" rx="5" class="storage"/>
  <text x="570" y="435" class="text">Redis Hash结构</text>
  <text x="570" y="450" class="text">hospital:department:{hospitalId}</text>
  <text x="570" y="465" class="text">hospital:patient:type:{hospitalId}:{type}</text>
  
  <!-- 外部系统 -->
  <rect x="800" y="80" width="300" height="120" rx="10" class="external" fill-opacity="0.3"/>
  <text x="950" y="100" class="layer-title" fill="#689F38">外部系统</text>
  
  <rect x="830" y="120" width="100" height="50" rx="5" class="external"/>
  <text x="880" y="140" class="text">外部API</text>
  <text x="880" y="155" class="text">医院系统</text>
  
  <ellipse cx="1000" cy="145" rx="60" ry="25" class="external"/>
  <text x="1000" y="140" class="text">数据库</text>
  <text x="1000" y="155" class="text">原始数据</text>
  
  <!-- 数据模型 -->
  <rect x="800" y="230" width="300" height="150" rx="10" class="model" fill-opacity="0.3"/>
  <text x="950" y="250" class="layer-title" fill="#00796B">数据模型</text>
  
  <rect x="830" y="270" width="100" height="40" rx="5" class="model"/>
  <text x="880" y="285" class="text">SyncResultDTO</text>
  <text x="880" y="300" class="text">同步结果</text>
  
  <rect x="950" y="270" width="80" height="40" rx="5" class="model"/>
  <text x="990" y="285" class="text">Patient</text>
  <text x="990" y="300" class="text">患者实体</text>
  
  <rect x="830" y="320" width="80" height="40" rx="5" class="model"/>
  <text x="870" y="335" class="text">Department</text>
  <text x="870" y="350" class="text">科室实体</text>
  
  <rect x="930" y="320" width="80" height="40" rx="5" class="model"/>
  <text x="970" y="335" class="text">User</text>
  <text x="970" y="350" class="text">用户实体</text>
  
  <!-- 常量配置 -->
  <rect x="800" y="410" width="300" height="120" rx="10" class="config" fill-opacity="0.3"/>
  <text x="950" y="430" class="layer-title" fill="#616161">常量配置</text>
  
  <rect x="830" y="450" width="120" height="50" rx="5" class="config"/>
  <text x="890" y="470" class="text">SyncConstants</text>
  <text x="890" y="485" class="text">同步常量</text>
  
  <rect x="970" y="450" width="100" height="50" rx="5" class="config"/>
  <text x="1020" y="465" class="text">Redis键前缀</text>
  <text x="1020" y="480" class="text">过期时间配置</text>
  
  <!-- 连接线 -->
  <!-- 客户端到控制器 -->
  <line x1="140" y1="170" x2="140" y2="270" class="arrow"/>
  <line x1="270" y1="170" x2="270" y2="270" class="arrow"/>
  
  <!-- 控制器到服务 -->
  <line x1="140" y1="320" x2="130" y2="420" class="arrow"/>
  <line x1="270" y1="320" x2="130" y2="420" class="arrow"/>
  
  <!-- 服务接口到实现 -->
  <line x1="180" y1="445" x2="200" y2="445" class="arrow"/>
  
  <!-- 服务到任务管理 -->
  <line x1="320" y1="445" x2="430" y2="145" class="arrow"/>
  
  <!-- 任务管理内部连接 -->
  <line x1="570" y1="145" x2="590" y2="145" class="arrow"/>
  <line x1="500" y1="170" x2="500" y2="190" class="arrow"/>
  
  <!-- 服务到存储 -->
  <line x1="320" y1="445" x2="430" y2="380" class="arrow"/>
  
  <!-- Redis工具到Redis -->
  <line x1="590" y1="375" x2="560" y2="380" class="arrow"/>
  
  <!-- 服务到外部系统 -->
  <line x1="320" y1="445" x2="830" y2="145" class="arrow"/>
  
  <!-- 外部API到数据库 -->
  <line x1="930" y1="145" x2="940" y2="145" class="arrow"/>
  
  <!-- 服务到数据模型 -->
  <line x1="320" y1="445" x2="830" y2="290" class="dashed-arrow"/>
  
  <!-- 服务到常量配置 -->
  <line x1="320" y1="445" x2="830" y2="475" class="dashed-arrow"/>
  
  <!-- 常量内部连接 -->
  <line x1="950" y1="475" x2="970" y2="475" class="arrow"/>

  <!-- 数据流说明 -->
  <g transform="translate(50, 550)">
    <text x="0" y="0" class="layer-title" fill="#333">数据流向说明:</text>

    <text x="0" y="25" class="text" font-size="11">1. 客户端发送同步请求到控制器</text>
    <text x="0" y="45" class="text" font-size="11">2. 控制器调用服务层进行业务处理</text>
    <text x="0" y="65" class="text" font-size="11">3. 服务层创建异步任务并立即返回</text>
    <text x="0" y="85" class="text" font-size="11">4. 异步任务从外部系统获取数据</text>
    <text x="0" y="105" class="text" font-size="11">5. 数据处理后存储到Redis缓存</text>
    <text x="0" y="125" class="text" font-size="11">6. 更新任务状态和同步结果</text>
  </g>

  <!-- 关键特性说明 -->
  <g transform="translate(400, 550)">
    <text x="0" y="0" class="layer-title" fill="#333">关键特性:</text>

    <text x="0" y="25" class="text" font-size="11">• 异步处理: 使用CompletableFuture实现非阻塞处理</text>
    <text x="0" y="45" class="text" font-size="11">• 任务管理: AsyncTaskManager管理任务生命周期</text>
    <text x="0" y="65" class="text" font-size="11">• 状态跟踪: 多层次状态保存和查询</text>
    <text x="0" y="85" class="text" font-size="11">• 数据分类: 智能患者数据自动分类</text>
    <text x="0" y="105" class="text" font-size="11">• 错误处理: 完善的异常处理和重试机制</text>
    <text x="0" y="125" class="text" font-size="11">• 性能监控: 详细的性能统计和监控指标</text>
  </g>

  <!-- Redis存储结构详细说明 -->
  <g transform="translate(800, 550)">
    <text x="0" y="0" class="layer-title" fill="#333">Redis存储结构:</text>

    <text x="0" y="25" class="text" font-size="11">• 医院数据: hospital:{type}:{hospitalId}</text>
    <text x="0" y="45" class="text" font-size="11">• 患者分类: hospital:patient:type:{hospitalId}:{type}</text>
    <text x="0" y="65" class="text" font-size="11">• 同步状态: hospital:{hospitalId}:sync:status:{tableName}</text>
    <text x="0" y="85" class="text" font-size="11">• 任务状态: sync:task:{taskId}</text>
    <text x="0" y="105" class="text" font-size="11">• 最后同步时间: hospital:{hospitalId}:sync:last_time:{tableName}</text>
    <text x="0" y="125" class="text" font-size="11">• 过期时间: 24小时(数据) / 7天(状态) / 永久(同步时间)</text>
  </g>

  <!-- 图例 -->
  <g transform="translate(50, 720)">
    <text x="0" y="0" class="layer-title" fill="#333">图例:</text>

    <rect x="0" y="15" width="30" height="20" class="client"/>
    <text x="40" y="30" class="text" font-size="11">客户端层</text>

    <rect x="120" y="15" width="30" height="20" class="controller"/>
    <text x="160" y="30" class="text" font-size="11">控制器层</text>

    <rect x="240" y="15" width="30" height="20" class="service"/>
    <text x="280" y="30" class="text" font-size="11">服务层</text>

    <rect x="340" y="15" width="30" height="20" class="task"/>
    <text x="380" y="30" class="text" font-size="11">任务管理层</text>

    <rect x="460" y="15" width="30" height="20" class="storage"/>
    <text x="500" y="30" class="text" font-size="11">数据存储层</text>

    <rect x="580" y="15" width="30" height="20" class="external"/>
    <text x="620" y="30" class="text" font-size="11">外部系统</text>

    <rect x="700" y="15" width="30" height="20" class="model"/>
    <text x="740" y="30" class="text" font-size="11">数据模型</text>

    <rect x="820" y="15" width="30" height="20" class="config"/>
    <text x="860" y="30" class="text" font-size="11">常量配置</text>

    <line x1="0" y1="50" x2="30" y2="50" class="arrow"/>
    <text x="40" y="55" class="text" font-size="11">直接调用</text>

    <line x1="120" y1="50" x2="150" y2="50" class="dashed-arrow"/>
    <text x="160" y="55" class="text" font-size="11">依赖关系</text>
  </g>
</svg>
