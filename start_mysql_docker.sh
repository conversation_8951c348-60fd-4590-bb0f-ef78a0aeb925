#!/bin/bash

# --- Configuration ---
CONTAINER_NAME="mysql8.0"
MYSQL_ROOT_PASSWORD="Aa123456" # !! IMPORTANT: Change this to a secure password !!
HOST_PORT="8306"
CONTAINER_PORT="3306"
VOLUME_NAME="mysql_data"
MYSQL_IMAGE="mysql/mysql-server:8.0"

# --- Script Logic ---

echo "Starting Docker MySQL 8.0 container..."
echo "Container Name: $CONTAINER_NAME"
echo "Host Port: $HOST_PORT"
echo "Container Port: $CONTAINER_PORT"
echo "Data Volume: $VOLUME_NAME"
echo "MySQL Image: $MYSQL_IMAGE"

# Check if the container already exists and is running
if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    echo "Container '${CONTAINER_NAME}' already exists."
    if docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        echo "Container '${CONTAINER_NAME}' is already running."
    else
        echo "Container '${CONTAINER_NAME}' exists but is stopped. Starting it..."
        docker start "$CONTAINER_NAME"
    fi
else
    echo "Container '${CONTAINER_NAME}' does not exist. Creating and starting it..."
    docker run \
        --name "$CONTAINER_NAME" \
        -e MYSQL_ROOT_PASSWORD="$MYSQL_ROOT_PASSWORD" \
        -p "${HOST_PORT}:${CONTAINER_PORT}" \
        -v "$VOLUME_NAME:/var/lib/mysql" \
        -d "$MYSQL_IMAGE"

    # Check if the run command was successful
    if [ $? -eq 0 ]; then
        echo "MySQL container '${CONTAINER_NAME}' started successfully!"
        echo "You can connect using: mysql -h 127.0.0.1 -P $HOST_PORT -u root -p"
        echo "Remember to use the password you set: '$MYSQL_ROOT_PASSWORD'"
    else
        echo "Error: Failed to start MySQL container."
        exit 1
    fi
fi

echo "To view container logs: docker logs $CONTAINER_NAME"
echo "To stop the container: docker stop $CONTAINER_NAME"
echo "To remove the container (data will persist in volume '$VOLUME_NAME'): docker rm $CONTAINER_NAME"
echo "To remove the data volume (DANGER: data will be lost!): docker volume rm $VOLUME_NAME"