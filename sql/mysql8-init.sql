-- MySQL 8.0 数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS sysgetway 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 创建测试数据库
CREATE DATABASE IF NOT EXISTS sysgetway_test 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE sysgetway;

-- 创建科室表
CREATE TABLE IF NOT EXISTS department (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    dept_code VARCHAR(50) NOT NULL COMMENT '科室编码',
    dept_name VARCHAR(100) NOT NULL COMMENT '科室名称',
    parent_id BIGINT DEFAULT NULL COMMENT '父科室ID',
    dept_level INT DEFAULT 1 COMMENT '科室级别',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    INDEX idx_dept_code (dept_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status),
    INDEX idx_deleted (deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='科室表';

-- 创建用户表（医护人员）
CREATE TABLE IF NOT EXISTS user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_code VARCHAR(50) NOT NULL COMMENT '用户编码',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    dept_id BIGINT NOT NULL COMMENT '科室ID',
    position VARCHAR(50) DEFAULT NULL COMMENT '职位',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    UNIQUE KEY uk_user_code (user_code),
    UNIQUE KEY uk_username (username),
    INDEX idx_dept_id (dept_id),
    INDEX idx_real_name (real_name),
    INDEX idx_status (status),
    INDEX idx_deleted (deleted),
    FOREIGN KEY (dept_id) REFERENCES department(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表（医护人员）';

-- 创建患者表
CREATE TABLE IF NOT EXISTS patient (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    patient_id VARCHAR(50) NOT NULL COMMENT '患者ID',
    name VARCHAR(50) NOT NULL COMMENT '患者姓名',
    gender TINYINT DEFAULT NULL COMMENT '性别：1-男，2-女，0-未知',
    birth_date DATE DEFAULT NULL COMMENT '出生日期',
    age INT DEFAULT NULL COMMENT '年龄',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    id_card VARCHAR(20) DEFAULT NULL COMMENT '身份证号',
    address VARCHAR(200) DEFAULT NULL COMMENT '地址',
    emergency_contact VARCHAR(50) DEFAULT NULL COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) DEFAULT NULL COMMENT '紧急联系人电话',
    medical_history TEXT DEFAULT NULL COMMENT '病史',
    allergy_history TEXT DEFAULT NULL COMMENT '过敏史',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    UNIQUE KEY uk_patient_id (patient_id),
    INDEX idx_name (name),
    INDEX idx_phone (phone),
    INDEX idx_id_card (id_card),
    INDEX idx_status (status),
    INDEX idx_deleted (deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者表';

-- 创建同步任务表
CREATE TABLE IF NOT EXISTS sync_task (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    task_id VARCHAR(50) NOT NULL COMMENT '任务ID',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_type VARCHAR(20) NOT NULL COMMENT '任务类型：FULL-全量同步，INCREMENTAL-增量同步',
    source_system VARCHAR(50) NOT NULL COMMENT '源系统',
    target_system VARCHAR(50) NOT NULL COMMENT '目标系统',
    data_type VARCHAR(50) NOT NULL COMMENT '数据类型',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '任务状态：PENDING-待执行，RUNNING-执行中，SUCCESS-成功，FAILED-失败',
    start_time DATETIME DEFAULT NULL COMMENT '开始时间',
    end_time DATETIME DEFAULT NULL COMMENT '结束时间',
    total_count INT DEFAULT 0 COMMENT '总记录数',
    success_count INT DEFAULT 0 COMMENT '成功记录数',
    failed_count INT DEFAULT 0 COMMENT '失败记录数',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_task_id (task_id),
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='同步任务表';

-- 插入初始数据
INSERT INTO department (dept_code, dept_name, parent_id, dept_level, sort_order, status, remark) VALUES
('ROOT', '医院', NULL, 1, 0, 1, '根科室'),
('ICU', '重症监护科', 1, 2, 1, 1, '重症监护科'),
('CARDIOLOGY', '心内科', 1, 2, 2, 1, '心内科'),
('NEUROLOGY', '神经内科', 1, 2, 3, 1, '神经内科'),
('EMERGENCY', '急诊科', 1, 2, 4, 1, '急诊科');

-- 插入测试用户数据
INSERT INTO user (user_code, username, real_name, dept_id, position, phone, email, status, remark) VALUES
('DOC001', 'doctor1', '张医生', 2, '主治医师', '13800138001', '<EMAIL>', 1, 'ICU主治医师'),
('DOC002', 'doctor2', '李医生', 3, '副主任医师', '13800138002', '<EMAIL>', 1, '心内科副主任医师'),
('NURSE001', 'nurse1', '王护士', 2, '护士长', '13800138003', '<EMAIL>', 1, 'ICU护士长');

-- 插入测试患者数据
INSERT INTO patient (patient_id, name, gender, birth_date, age, phone, id_card, address, emergency_contact, emergency_phone, status, remark) VALUES
('P001', '张三', 1, '1980-01-01', 44, '13900139001', '110101198001011234', '北京市朝阳区', '李四', '13900139002', 1, '测试患者1'),
('P002', '李四', 2, '1990-05-15', 34, '13900139003', '110101199005151234', '北京市海淀区', '王五', '13900139004', 1, '测试患者2'),
('P003', '王五', 1, '1975-12-20', 49, '13900139005', '110101197512201234', '北京市西城区', '赵六', '13900139006', 1, '测试患者3');

-- 设置MySQL 8.0兼容性
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- 提交事务
COMMIT;
