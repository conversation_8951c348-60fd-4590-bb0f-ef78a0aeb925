image: ccr.ccs.tencentyun.com/itingluo/build-base:latest

stages:
  - release
  - deployment

variables:
  DOCKER_DRIVER: overlay
  SERVICE_NAME: bangbang
  IMAGE_NAME: ccr.ccs.tencentyun.com/itingluo/sysgateway
  CONTAINER_SHA_IMAGE: $IMAGE_NAME:$CI_COMMIT_SHORT_SHA
  CONTAINER_RELEASE_IMAGE: $IMAGE_NAME
  NAMESPACE: default

release:
  stage: release
  before_script:
    - eval $(ssh-agent -s)
    - echo "$IVANKA_PUBLISH_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'

  script:
    - REVISION=`git rev-parse --short=8 HEAD || echo unknown`
    - docker login --username=100001579474 ccr.ccs.tencentyun.com -p "$CI_DOCKER_REGISTRY_TOKEN"
    - docker build --tag="$IMAGE_NAME:master" .
    - docker push "$IMAGE_NAME:master"
  only:
    - release

release:sit:
  stage: release
  before_script:
    - eval $(ssh-agent -s)
    - echo "$IVANKA_PUBLISH_SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    - REVISION=`git rev-parse --short=8 HEAD || echo unknown`
    - docker login --username=100001579474 ccr.ccs.tencentyun.com -p "$JY_CI_DOCKER_REGISTRY_TOKEN"
    - docker build --tag="$CONTAINER_SHA_IMAGE" .
    - docker push "$CONTAINER_SHA_IMAGE"
  only:
    - sit
    - sit-fix-sj

deployment:prod:
  stage: deployment
  image: ccr.ccs.tencentyun.com/tingluo/ssh-client:v240321
  before_script:
    # Install ssh-agent if not already installed, it is required by Docker.
    # (change apt-get to yum if you use a CentOS-based image)
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    # Run ssh-agent (inside the build environment)
    - eval $(ssh-agent -s)
    # Add the SSH key stored in SSH_PRIVATE_KEY variable to the agent store
    - ssh-add <(echo "$IVANKA_PUBLISH_SSH_PRIVATE_KEY")
    # For Docker builds disable host key checking. Be aware that by adding that
    # you are suspectible to man-in-the-middle attacks.
    # WARNING: Use this only with the Docker executor, if you use it with shell
    # you will overwrite your user's SSH config.
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    - ssh -p 2022 root@************ "kubectl -n preprod set image deploy $SERVICE_NAME *=$CONTAINER_SHA_IMAGE"
  only:
    - release

deployment:sit:
  stage: deployment
  image: ccr.ccs.tencentyun.com/tingluo/ssh-client:v240321
  before_script:
    # Install ssh-agent if not already installed, it is required by Docker.
    # (change apt-get to yum if you use a CentOS-based image)
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    # Run ssh-agent (inside the build environment)
    - eval $(ssh-agent -s)
    # Add the SSH key stored in SSH_PRIVATE_KEY variable to the agent store
    - ssh-add <(echo "$IVANKA_PUBLISH_SSH_PRIVATE_KEY")
    # For Docker builds disable host key checking. Be aware that by adding that
    # you are suspectible to man-in-the-middle attacks.
    # WARNING: Use this only with the Docker executor, if you use it with shell
    # you will overwrite your user's SSH config.
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    - ssh -p 2022 root@************ "kubectl -n sit set image deploy $SERVICE_NAME *=$CONTAINER_SHA_IMAGE"
  only:
    - sit
    - sit-fix-sj
