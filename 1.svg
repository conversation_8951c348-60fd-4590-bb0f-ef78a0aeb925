<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1024.14453125 482" style="max-width: 1024.14453125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f"><style>#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .error-icon{fill:#a44141;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .error-text{fill:#ddd;stroke:#ddd;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edge-thickness-normal{stroke-width:1px;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edge-thickness-thick{stroke-width:3.5px;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edge-pattern-solid{stroke-dasharray:0;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .marker.cross{stroke:lightgrey;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f p{margin:0;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .cluster-label text{fill:#F9FFFE;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .cluster-label span{color:#F9FFFE;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .cluster-label span p{background-color:transparent;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .label text,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f span{fill:#ccc;color:#ccc;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .node rect,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .node circle,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .node ellipse,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .node polygon,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .rough-node .label text,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .node .label text,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .image-shape .label,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .icon-shape .label{text-anchor:middle;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .rough-node .label,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .node .label,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .image-shape .label,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .icon-shape .label{text-align:center;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .node.clickable{cursor:pointer;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .arrowheadPath{fill:lightgrey;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .cluster text{fill:#F9FFFE;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .cluster span{color:#F9FFFE;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f rect.text{fill:none;stroke-width:0;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .icon-shape,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .icon-shape p,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .icon-shape rect,#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="Java项目" class="cluster"><rect height="233" width="580.98828125" y="241" x="8" style=""></rect><g transform="translate(266.353515625, 241)" class="cluster-label"><foreignObject height="24" width="64.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Java项目</p></span></div></foreignObject></g></g><g data-look="classic" id="Go项目" class="cluster"><rect height="233" width="407.15625" y="112" x="608.98828125" style=""></rect><g transform="translate(786.86328125, 112)" class="cluster-label"><foreignObject height="24" width="51.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Go项目</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M745.977,62L745.977,66.167C745.977,70.333,745.977,78.667,745.977,87C745.977,95.333,745.977,103.667,745.977,111.333C745.977,119,745.977,126,745.977,129.5L745.977,133"></path><path marker-end="url(#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M706.042,191L699.88,195.167C693.717,199.333,681.392,207.667,675.229,216C669.066,224.333,669.066,232.667,638.543,242.261C608.02,251.856,546.973,262.712,516.45,268.141L485.926,273.569"></path><path marker-end="url(#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M450.476,320L461.867,324.167C473.259,328.333,496.042,336.667,507.433,345C518.824,353.333,518.824,361.667,535.714,371.005C552.604,380.343,586.384,390.685,603.274,395.857L620.164,401.028"></path><path marker-end="url(#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_E_3" d="M737.237,191L735.888,195.167C734.539,199.333,731.842,207.667,730.493,216C729.145,224.333,729.145,232.667,729.145,240.333C729.145,248,729.145,255,729.145,258.5L729.145,262"></path><path marker-end="url(#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_F_4" d="M831.133,189.571L845.801,193.976C860.47,198.381,889.807,207.19,904.476,215.762C919.145,224.333,919.145,232.667,919.145,240.333C919.145,248,919.145,255,919.145,258.5L919.145,262"></path><path marker-end="url(#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_G_5" d="M271.332,313.905L245.221,319.088C219.109,324.27,166.887,334.635,140.775,343.984C114.664,353.333,114.664,361.667,114.664,369.333C114.664,377,114.664,384,114.664,387.5L114.664,391"></path><path marker-end="url(#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_H_6" d="M335.988,320L329.711,324.167C323.435,328.333,310.881,336.667,304.605,345C298.328,353.333,298.328,361.667,298.328,369.333C298.328,377,298.328,384,298.328,387.5L298.328,391"></path><path marker-end="url(#mermaid-19bf06be-9c07-4a34-a9d6-e03dd910c28f_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_I_7" d="M425.64,320L433.199,324.167C440.758,328.333,455.875,336.667,463.434,345C470.992,353.333,470.992,361.667,470.992,369.333C470.992,377,470.992,384,470.992,387.5L470.992,391"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(745.9765625, 35)" id="flowchart-A-0" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>外部系统</p></span></div></foreignObject></g></g><g transform="translate(745.9765625, 164)" id="flowchart-B-1" class="node default"><rect height="54" width="170.3125" y="-27" x="-85.15625" style="" class="basic label-container"></rect><g transform="translate(-55.15625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="110.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Go gRPC Server</p></span></div></foreignObject></g></g><g transform="translate(376.66015625, 293)" id="flowchart-C-3" class="node default"><rect height="54" width="210.65625" y="-27" x="-105.328125" style="" class="basic label-container"></rect><g transform="translate(-75.328125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="150.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Java Spring Boot应用</p></span></div></foreignObject></g></g><g transform="translate(688.66015625, 422)" id="flowchart-D-5" class="node default"><rect height="54" width="129.34375" y="-27" x="-64.671875" style="" class="basic label-container"></rect><g transform="translate(-34.671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="69.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Redis存储</p></span></div></foreignObject></g></g><g transform="translate(729.14453125, 293)" id="flowchart-E-7" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据处理服务</p></span></div></foreignObject></g></g><g transform="translate(919.14453125, 293)" id="flowchart-F-9" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>业务逻辑</p></span></div></foreignObject></g></g><g transform="translate(114.6640625, 422)" id="flowchart-G-11" class="node default"><rect height="54" width="143.328125" y="-27" x="-71.6640625" style="" class="basic label-container"></rect><g transform="translate(-41.6640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="83.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>gRPC Client</p></span></div></foreignObject></g></g><g transform="translate(298.328125, 422)" id="flowchart-H-13" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>同步服务</p></span></div></foreignObject></g></g><g transform="translate(470.9921875, 422)" id="flowchart-I-15" class="node default"><rect height="54" width="121.328125" y="-27" x="-60.6640625" style="" class="basic label-container"></rect><g transform="translate(-30.6640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="61.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>REST API</p></span></div></foreignObject></g></g></g></g></g></svg>