<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .header { fill: #2C3E50; stroke: #34495E; stroke-width: 2; }
      .step { fill: #3498DB; stroke: #2980B9; stroke-width: 2; }
      .api-box { fill: #E74C3C; stroke: #C0392B; stroke-width: 2; }
      .response-box { fill: #27AE60; stroke: #229954; stroke-width: 2; }
      .note-box { fill: #F39C12; stroke: #E67E22; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; dominant-baseline: middle; font-weight: 500; }
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; }
      .api-text { font-family: 'Courier New', monospace; font-size: 13px; text-anchor: middle; dominant-baseline: middle; font-weight: bold; }
      .code { font-family: 'Courier New', monospace; font-size: 11px; text-anchor: start; dominant-baseline: middle; }
      .arrow { stroke: #2C3E50; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2C3E50" />
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1000" height="700" fill="#ECF0F1"/>
  
  <!-- 标题 -->
  <rect x="200" y="20" width="600" height="60" rx="10" class="header"/>
  <text x="500" y="55" class="title" fill="white">数据同步接口对接指南</text>
  
  <!-- 步骤1 -->
  <rect x="50" y="120" width="200" height="40" rx="8" class="step"/>
  <text x="150" y="140" class="text" fill="white">步骤1: 发起同步请求</text>
  
  <rect x="300" y="100" width="400" height="80" rx="8" class="api-box"/>
  <text x="500" y="125" class="api-text" fill="white">PUT /api/sync/full/{hospitalId}/{tableName}</text>
  <text x="500" y="145" class="text" fill="white">hospitalId: 医院编码 | tableName: department/user/patient</text>
  <text x="500" y="165" class="text" fill="white">Body: JSON数组 (可选，不传则自动获取数据)</text>
  
  <!-- 步骤2 -->
  <rect x="50" y="220" width="200" height="40" rx="8" class="step"/>
  <text x="150" y="240" class="text" fill="white">步骤2: 立即获得响应</text>
  
  <rect x="300" y="200" width="400" height="80" rx="8" class="response-box"/>
  <text x="500" y="225" class="text" fill="white">立即返回任务状态 (异步处理)</text>
  <!-- <text x="500" y="245" class="api-text" fill="white">{"status": 0, "taskId": "task_123456"}</text> -->
    <text x="500" y="245" class="api-text" fill="white">{"taskId": "task_123456"}</text>
  <!-- <text x="500" y="265" class="text" fill="white">status: 0=进行中, 1=成功, -1=失败</text> -->
  
  <!-- 步骤3 -->
  <rect x="50" y="320" width="200" height="40" rx="8" class="step"/>
  <text x="150" y="340" class="text" fill="white">步骤3: 查询执行状态</text>
  
  <rect x="300" y="300" width="400" height="80" rx="8" class="api-box"/>
  <text x="500" y="325" class="api-text" fill="white">GET /api/sync/status/{taskId}</text>
  <text x="500" y="345" class="text" fill="white">使用步骤2返回的taskId查询详细状态</text>
  <text x="500" y="365" class="text" fill="white">建议每5-10秒轮询一次直到完成</text>
  
  <!-- 步骤4 -->
  <rect x="50" y="420" width="200" height="40" rx="8" class="step"/>
  <text x="150" y="440" class="text" fill="white">步骤4: 获得最终结果</text>
  
  <rect x="300" y="400" width="400" height="80" rx="8" class="response-box"/>
  <text x="500" y="425" class="text" fill="white">同步完成状态</text>
  <text x="500" y="445" class="api-text" fill="white">{"status": 1, "count": 1500, "costTime": 30000}</text>
  <text x="500" y="465" class="text" fill="white">包含数据量、耗时、成功率等详细信息</text>
  
  <!-- 连接箭头 -->
  <line x1="250" y1="140" x2="300" y2="140" class="arrow"/>
  <line x1="250" y1="240" x2="300" y2="240" class="arrow"/>
  <line x1="250" y1="340" x2="300" y2="340" class="arrow"/>
  <line x1="250" y1="440" x2="300" y2="440" class="arrow"/>
  
  <!-- 重要提示 -->
  <rect x="750" y="120" width="240" height="360" rx="10" class="note-box"/>
  <text x="870" y="145" class="text" font-size="15" font-weight="bold" fill="white" text-anchor="middle">💡 重要提示</text>

  <!-- 第一行：异步处理和状态轮询 -->
  <text x="870" y="180" class="text" font-size="9" font-weight="bold" fill="white" text-anchor="middle">🔄 异步处理</text>
  <text x="870" y="195" class="text" font-size="8" fill="white" text-anchor="middle">立即返回，后台处理</text>

  <text x="870" y="220" class="text" font-size="9" font-weight="bold" fill="white" text-anchor="middle">📊 状态轮询</text>
  <text x="870" y="235" class="text" font-size="8" fill="white" text-anchor="middle">间隔5-10秒查询</text>

  <!-- 第二行：支持的表类型 -->
  <text x="870" y="260" class="text" font-size="9" font-weight="bold" fill="white" text-anchor="middle">🏥 表类型</text>
  <text x="870" y="275" class="text" font-size="8" fill="white" text-anchor="middle">dept | user | patient</text>

  <!-- 第三行：性能特点 -->
  <text x="870" y="300" class="text" font-size="9" font-weight="bold" fill="white" text-anchor="middle">⚡ 性能特点</text>
  <text x="870" y="315" class="text" font-size="8" fill="white" text-anchor="middle">• 大批量数据同步</text>

  <text x="870" y="328" class="text" font-size="8" fill="white" text-anchor="middle">• Redis高性能存储</text>

  <!-- 第四行：监控和格式 -->
  <text x="870" y="365" class="text" font-size="9" font-weight="bold" fill="white" text-anchor="middle">🔍 实时监控</text>
  <text x="870" y="380" class="text" font-size="8" fill="white" text-anchor="middle">taskId状态跟踪</text>

  <text x="870" y="405" class="text" font-size="9" font-weight="bold" fill="white" text-anchor="middle">📋 数据格式</text>
  <text x="870" y="420" class="text" font-size="8" fill="white" text-anchor="middle">JSON格式，UTF-8编码</text>
  <text x="870" y="435" class="text" font-size="8" fill="white" text-anchor="middle">完整支持中文字符</text>
  
  <!-- 示例代码区域 -->
  <rect x="50" y="520" width="900" height="150" rx="10" fill="white" stroke="#BDC3C7" stroke-width="2"/>
  <text x="500" y="545" class="text" font-size="16" font-weight="bold" fill="#2C3E50" text-anchor="middle">示例代码</text>

  <!-- 请求示例 -->
  <text x="120" y="570" class="text" font-size="11" font-weight="bold" fill="#E74C3C" text-anchor="start">1. 发起请求:</text>
  <text x="130" y="584" class="code" font-size="8" fill="#2C3E50">curl -X PUT "api.com/sync/full/h1/patient" \</text>
  <text x="130" y="595" class="code" font-size="8" fill="#2C3E50">  -H "Content-Type: application/json" \</text>
  <text x="130" y="606" class="code" font-size="8" fill="#2C3E50">  -d '[{"id":1,"name":"张三"}]'</text>

  <!-- 响应示例 -->
  <text x="520" y="570" class="text" font-size="11" font-weight="bold" fill="#27AE60" text-anchor="start">2. 立即响应:</text>
  <text x="530" y="584" class="code" font-size="8" fill="#2C3E50">{"code":200, "message":"success",</text>
  <text x="530" y="595" class="code" font-size="8" fill="#2C3E50"> "data":{"status":0, "taskId":"t123",</text>
  <text x="530" y="606" class="code" font-size="8" fill="#2C3E50">         "hospitalId":"h1"}}</text>

  <!-- 查询示例 -->
  <text x="120" y="625" class="text" font-size="11" font-weight="bold" fill="#3498DB" text-anchor="start">3. 查询状态:</text>
  <text x="130" y="640" class="code" font-size="8" fill="#2C3E50">curl "api.com/sync/status/t123"</text>

  <text x="520" y="625" class="text" font-size="11" font-weight="bold" fill="#27AE60" text-anchor="start">4. 完成响应:</text>
  <text x="530" y="640" class="code" font-size="8" fill="#2C3E50">{"status":1, "count":1500, "time":30000}</text>
</svg>
