#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JSON文件格式化工具
用于将JSON文件格式化，使其具有良好的缩进和换行
支持修复常见的JSON格式问题
"""

import json
import sys
import os
import re

def fix_json_content(content):
    """
    尝试修复JSON内容中的常见问题
    
    参数:
        content: JSON字符串
        
    返回:
        修复后的JSON字符串
    """
    # 检查是否以[开头，]结尾
    content = content.strip()
    if not content.startswith('['):
        content = '[' + content
    if not content.endswith(']'):
        content = content + ']'
    
    # 修复可能的JSON格式问题
    # 1. 处理字符串中的转义字符
    content = re.sub(r'(?<!\\)\\(?!["\\/bfnrtu])', r'\\\\', content)
    
    # 2. 处理未闭合的引号
    # 这里的修复比较复杂，可能需要更复杂的解析逻辑
    
    # 3. 处理末尾多余的逗号
    content = re.sub(r',\s*]', ']', content)
    
    return content

def format_json_file(input_file, output_file=None, fix_errors=True):
    """
    格式化JSON文件
    
    参数:
        input_file: 输入文件路径
        output_file: 输出文件路径，如果为None，则覆盖原文件
        fix_errors: 是否尝试修复JSON格式错误
    """
    print(f"正在读取文件: {input_file}")
    
    try:
        # 读取文件内容
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 如果需要，尝试修复JSON格式
        if fix_errors:
            try:
                # 先尝试直接解析
                data = json.loads(content)
            except json.JSONDecodeError as e:
                print(f"JSON解析失败，尝试修复格式问题: {e}")
                
                # 尝试通过Python的eval函数解析（不安全，但有效）
                try:
                    # 替换JavaScript风格的null为Python的None
                    content = content.replace('null', 'None')
                    # 使用eval解析
                    data = eval(content)
                except Exception as e:
                    print(f"无法通过eval修复: {e}")
                    
                    # 尝试通过正则表达式修复
                    try:
                        fixed_content = fix_json_content(content)
                        data = json.loads(fixed_content)
                        print("通过正则表达式修复成功")
                    except Exception as e:
                        print(f"无法通过正则表达式修复: {e}")
                        
                        # 最后尝试逐行读取并拼接
                        print("尝试逐行读取并重新构建JSON...")
                        try:
                            # 重新打开文件
                            with open(input_file, 'r', encoding='utf-8') as f:
                                lines = f.readlines()
                            
                            # 尝试手动构建JSON数组
                            manual_json = "["
                            for i, line in enumerate(lines):
                                # 寻找JSON对象的开始和结束
                                if "{" in line:
                                    obj_start = i
                                    # 寻找对应的结束括号
                                    obj_content = line
                                    j = i + 1
                                    while j < len(lines) and "}" not in obj_content:
                                        obj_content += lines[j]
                                        j += 1
                                    
                                    # 提取JSON对象
                                    start_idx = obj_content.find("{")
                                    end_idx = obj_content.find("}", start_idx)
                                    if end_idx != -1:
                                        obj = obj_content[start_idx:end_idx+1]
                                        manual_json += obj + ","
                            
                            # 完成JSON数组
                            if manual_json.endswith(","):
                                manual_json = manual_json[:-1]
                            manual_json += "]"
                            
                            # 尝试解析
                            data = json.loads(manual_json)
                            print("通过手动构建JSON成功")
                        except Exception as e:
                            print(f"所有修复尝试均失败: {e}")
                            return False
        else:
            # 直接解析JSON
            data = json.loads(content)
        
        # 如果未指定输出文件，则覆盖原文件
        if output_file is None:
            output_file = input_file
        
        # 写入格式化的JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        # 获取文件大小
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
        
        print(f"格式化完成，已保存到: {output_file}")
        print(f"文件大小: {file_size:.2f} MB")
        
    except Exception as e:
        print(f"错误: {e}")
        return False
    
    return True

def main():
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("用法: python format_json.py <输入文件> [输出文件] [--no-fix]")
        return
    
    input_file = sys.argv[1]
    output_file = None
    fix_errors = True
    
    # 处理参数
    for arg in sys.argv[2:]:
        if arg == "--no-fix":
            fix_errors = False
        elif not arg.startswith("--"):
            output_file = arg
    
    format_json_file(input_file, output_file, fix_errors)

if __name__ == "__main__":
    main() 